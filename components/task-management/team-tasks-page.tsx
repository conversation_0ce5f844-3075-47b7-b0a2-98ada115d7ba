"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Users, ListTodo, Clock, CheckCircle, AlertCircle, Calendar } from "lucide-react"
import Link from "next/link"

export function TeamTasksPage() {
  // Mock team data - replace with actual API call
  const teamMembers = [
    {
      id: "1",
      name: "<PERSON>",
      role: "Developer",
      activeTasks: 5,
      completedTasks: 12,
      overdueTasks: 1
    },
    {
      id: "2", 
      name: "<PERSON>",
      role: "Designer",
      activeTasks: 3,
      completedTasks: 8,
      overdueTasks: 0
    },
    {
      id: "3",
      name: "<PERSON>", 
      role: "Project Manager",
      activeTasks: 7,
      completedTasks: 15,
      overdueTasks: 2
    }
  ]

  const teamTasks = [
    {
      id: "1",
      title: "Website Redesign",
      assignee: "<PERSON>",
      status: "in-progress",
      priority: "high",
      dueDate: "2024-01-20"
    },
    {
      id: "2",
      title: "Mobile App Testing",
      assignee: "<PERSON>", 
      status: "completed",
      priority: "medium",
      dueDate: "2024-01-18"
    },
    {
      id: "3",
      title: "Database Migration",
      assignee: "Mike <PERSON>",
      status: "blocked",
      priority: "urgent", 
      dueDate: "2024-01-15"
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800"
      case "in-progress":
        return "bg-blue-100 text-blue-800"
      case "blocked":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-100 text-red-800"
      case "high":
        return "bg-orange-100 text-orange-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="space-y-6">
      {/* Team Overview */}
      <div className="grid gap-4 md:grid-cols-3">
        {teamMembers.map((member) => (
          <Card key={member.id}>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">{member.name}</CardTitle>
              <CardDescription>{member.role}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Active Tasks</span>
                  <Badge variant="outline">{member.activeTasks}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Completed</span>
                  <Badge className="bg-green-100 text-green-800">{member.completedTasks}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Overdue</span>
                  <Badge className={member.overdueTasks > 0 ? "bg-red-100 text-red-800" : "bg-gray-100 text-gray-800"}>
                    {member.overdueTasks}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Team Tasks */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ListTodo className="h-5 w-5" />
            Team Tasks
          </CardTitle>
          <CardDescription>
            Current tasks assigned to team members
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {teamTasks.map((task) => (
              <div key={task.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium">{task.title}</h4>
                  <p className="text-sm text-muted-foreground">Assigned to: {task.assignee}</p>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge className={getStatusColor(task.status)}>
                      {task.status.replace("-", " ")}
                    </Badge>
                    <Badge variant="outline" className={getPriorityColor(task.priority)}>
                      {task.priority}
                    </Badge>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      {new Date(task.dueDate).toLocaleDateString()}
                    </div>
                  </div>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href={`/dashboard/tasks/${task.id}`}>
                    View
                  </Link>
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Team Management</CardTitle>
          <CardDescription>
            Manage team tasks and assignments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-2">
            <Button variant="outline" className="justify-start" asChild>
              <Link href="/dashboard/project">
                <ListTodo className="h-4 w-4 mr-2" />
                Create New Task
              </Link>
            </Button>
            
            <Button variant="outline" className="justify-start" asChild>
              <Link href="/dashboard/tasks/reports">
                <Users className="h-4 w-4 mr-2" />
                Team Reports
              </Link>
            </Button>
            
            <Button variant="outline" className="justify-start" asChild>
              <Link href="/dashboard/tasks/calendar">
                <Calendar className="h-4 w-4 mr-2" />
                Team Calendar
              </Link>
            </Button>
            
            <Button variant="outline" className="justify-start" asChild>
              <Link href="/dashboard/project">
                <CheckCircle className="h-4 w-4 mr-2" />
                Project Dashboard
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

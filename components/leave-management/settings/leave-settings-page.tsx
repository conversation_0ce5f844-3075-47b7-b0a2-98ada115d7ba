"use client"

import { useState, useEffect } from "react"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { useLeaveSettings } from "@/hooks/use-leave-settings"
import { <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>riangle } from "lucide-react"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { LeaveTypesManagement } from "./leave-types-management"
import { LeavePoliciesOverview } from "./leave-policies-overview"

interface LeaveSettingsPageProps {
  userId: string
}

const leaveSettingsSchema = z.object({
  carryOverEnabled: z.boolean().default(true),
  maxCarryOverDays: z.coerce.number().min(0, "Must be at least 0"),
  minNoticeDays: z.coerce.number().min(0, "Must be at least 0"),
  autoApproveEnabled: z.boolean().default(false),
  approvalWorkflow: z.enum(["single", "multi", "department"]),
  emailNotificationsEnabled: z.boolean().default(true),
  calendarIntegrationEnabled: z.boolean().default(true),
  defaultLeaveYear: z.enum(["calendar", "fiscal", "anniversary"]),
  fiscalYearStart: z.string(),
  leaveAccrualFrequency: z.enum(["monthly", "quarterly", "annually"]),
  proRataCalculationEnabled: z.boolean().default(true),
  holidayExclusionEnabled: z.boolean().default(true),
  weekendExclusionEnabled: z.boolean().default(true),
  halfDayLeaveEnabled: z.boolean().default(true),
  hourlyLeaveEnabled: z.boolean().default(false),
  leaveEncashmentEnabled: z.boolean().default(false),
  leaveEncashmentLimit: z.coerce.number().min(0, "Must be at least 0"),
  leaveEncashmentRate: z.coerce.number().min(0, "Must be at least 0").max(100, "Must be at most 100"),
  leaveRequestCancellationEnabled: z.boolean().default(true),
  maxCancellationDays: z.coerce.number().min(0, "Must be at least 0"),
  leaveBalanceDisplayEnabled: z.boolean().default(true),
  leaveBalanceWarningThreshold: z.coerce.number().min(0, "Must be at least 0"),
  leaveBalanceWarningEnabled: z.boolean().default(true),
  leaveDocumentationRequired: z.boolean().default(false),
  leaveDocumentationDays: z.coerce.number().min(0, "Must be at least 0"),
})

type LeaveSettingsValues = z.infer<typeof leaveSettingsSchema>

export function LeaveSettingsPage({ userId }: LeaveSettingsPageProps) {
  const [activeTab, setActiveTab] = useState("general")
  const [isResetting, setIsResetting] = useState(false)

  const {
    loading,
    error,
    settings,
    getSettings,
    updateSettings,
    resetSettings
  } = useLeaveSettings()

  const form = useForm<LeaveSettingsValues>({
    resolver: zodResolver(leaveSettingsSchema) as any,
    defaultValues: {
      carryOverEnabled: true,
      maxCarryOverDays: 5,
      minNoticeDays: 3,
      autoApproveEnabled: false,
      approvalWorkflow: "single",
      emailNotificationsEnabled: true,
      calendarIntegrationEnabled: true,
      defaultLeaveYear: "calendar",
      fiscalYearStart: "01-04",
      leaveAccrualFrequency: "monthly",
      proRataCalculationEnabled: true,
      holidayExclusionEnabled: true,
      weekendExclusionEnabled: true,
      halfDayLeaveEnabled: true,
      hourlyLeaveEnabled: false,
      leaveEncashmentEnabled: false,
      leaveEncashmentLimit: 10,
      leaveEncashmentRate: 100,
      leaveRequestCancellationEnabled: true,
      maxCancellationDays: 2,
      leaveBalanceDisplayEnabled: true,
      leaveBalanceWarningThreshold: 5,
      leaveBalanceWarningEnabled: true,
      leaveDocumentationRequired: false,
      leaveDocumentationDays: 3,
    },
  })

  // Load settings on component mount
  useEffect(() => {
    const loadSettings = async () => {
      const loadedSettings = await getSettings()
      if (loadedSettings) {
        // Update form with loaded settings
        form.reset(loadedSettings)
      }
    }
    loadSettings()
  }, [getSettings, form])

  async function onSubmit(values: LeaveSettingsValues) {
    const success = await updateSettings(values)
    if (success) {
      // Settings updated successfully
      console.log("Leave settings saved:", values)
    }
  }

  const handleReset = async () => {
    setIsResetting(true)
    const success = await resetSettings()
    if (success) {
      // Reload settings after reset
      const loadedSettings = await getSettings()
      if (loadedSettings) {
        form.reset(loadedSettings)
      }
    }
    setIsResetting(false)
  }

  return (
    <div className="space-y-6">
      {/* Header with actions */}
      <div className="flex items-center justify-between">
        <div>
          <p className="text-muted-foreground">
            Configure leave management policies and settings for your organization
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={loading || isResetting}
          >
            {isResetting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Resetting...
              </>
            ) : (
              <>
                <RotateCcw className="mr-2 h-4 w-4" />
                Reset to Defaults
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Loading State */}
      {loading && !settings && (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading settings...</span>
        </div>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4 md:w-[800px]">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="accrual">Accrual & Calculation</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="types">Leave Types</TabsTrigger>
        </TabsList>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 mt-6">
            <TabsContent value="general" className="space-y-6">
              {/* Policies Overview */}
              <LeavePoliciesOverview userId={userId} />

              <Card>
                <CardHeader>
                  <CardTitle>General Settings</CardTitle>
                  <CardDescription>
                    Configure general leave management settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control as any}
                    name="carryOverEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Leave Carry-Over
                          </FormLabel>
                          <FormDescription>
                            Allow employees to carry over unused leave days to the next year
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {form.watch("carryOverEnabled") && (
                    <FormField
                      control={form.control as any}
                      name="maxCarryOverDays"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Maximum Carry-Over Days</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormDescription>
                            Maximum number of days that can be carried over to the next year
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  <FormField
                    control={form.control as any}
                    name="minNoticeDays"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Minimum Notice Period (days)</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormDescription>
                          Minimum number of days in advance for leave requests
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control as any}
                    name="approvalWorkflow"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Approval Workflow</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select approval workflow" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="single">Single Approver</SelectItem>
                            <SelectItem value="multi">Multiple Approvers</SelectItem>
                            <SelectItem value="department">Department-based</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Configure how leave requests are approved
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control as any}
                    name="autoApproveEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Auto-Approve Requests
                          </FormLabel>
                          <FormDescription>
                            Automatically approve leave requests if they meet all criteria
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Leave Year Settings</CardTitle>
                  <CardDescription>
                    Configure how the leave year is calculated
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control as any}
                    name="defaultLeaveYear"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Default Leave Year</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select leave year type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="calendar">Calendar Year (Jan-Dec)</SelectItem>
                            <SelectItem value="fiscal">Fiscal Year</SelectItem>
                            <SelectItem value="anniversary">Anniversary-based</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Define how the leave year is calculated
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {form.watch("defaultLeaveYear") === "fiscal" && (
                    <FormField
                      control={form.control as any}
                      name="fiscalYearStart"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Fiscal Year Start (DD-MM)</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="01-04" />
                          </FormControl>
                          <FormDescription>
                            Start date of the fiscal year (e.g., 01-04 for April 1st)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="accrual" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Accrual Settings</CardTitle>
                  <CardDescription>
                    Configure how leave is accrued and calculated
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control as any}
                    name="leaveAccrualFrequency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Leave Accrual Frequency</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select accrual frequency" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="monthly">Monthly</SelectItem>
                            <SelectItem value="quarterly">Quarterly</SelectItem>
                            <SelectItem value="annually">Annually</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          How frequently leave days are accrued
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control as any}
                    name="proRataCalculationEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Pro-Rata Calculation
                          </FormLabel>
                          <FormDescription>
                            Calculate leave entitlement proportionally for partial years
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <Separator />

                  <FormField
                    control={form.control as any}
                    name="holidayExclusionEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Exclude Holidays
                          </FormLabel>
                          <FormDescription>
                            Exclude public holidays from leave calculations
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control as any}
                    name="weekendExclusionEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Exclude Weekends
                          </FormLabel>
                          <FormDescription>
                            Exclude weekends from leave calculations
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <Separator />

                  <FormField
                    control={form.control as any}
                    name="halfDayLeaveEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Half-Day Leave
                          </FormLabel>
                          <FormDescription>
                            Allow employees to request half-day leave
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control as any}
                    name="hourlyLeaveEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Hourly Leave
                          </FormLabel>
                          <FormDescription>
                            Allow employees to request leave by the hour
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Leave Encashment</CardTitle>
                  <CardDescription>
                    Configure leave encashment settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control as any}
                    name="leaveEncashmentEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Enable Leave Encashment
                          </FormLabel>
                          <FormDescription>
                            Allow employees to convert unused leave to cash
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {form.watch("leaveEncashmentEnabled") && (
                    <>
                      <FormField
                        control={form.control as any}
                        name="leaveEncashmentLimit"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Maximum Encashment Days</FormLabel>
                            <FormControl>
                              <Input type="number" {...field} />
                            </FormControl>
                            <FormDescription>
                              Maximum number of days that can be encashed per year
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control as any}
                        name="leaveEncashmentRate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Encashment Rate (%)</FormLabel>
                            <FormControl>
                              <Input type="number" {...field} />
                            </FormControl>
                            <FormDescription>
                              Percentage of daily salary paid for each encashed leave day
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notifications" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Notification Settings</CardTitle>
                  <CardDescription>
                    Configure leave-related notifications
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control as any}
                    name="emailNotificationsEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Email Notifications
                          </FormLabel>
                          <FormDescription>
                            Send email notifications for leave-related events
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control as any}
                    name="calendarIntegrationEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Calendar Integration
                          </FormLabel>
                          <FormDescription>
                            Integrate leave events with calendar applications
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control as any}
                    name="leaveBalanceWarningEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Leave Balance Warnings
                          </FormLabel>
                          <FormDescription>
                            Send warnings when leave balance is low
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {form.watch("leaveBalanceWarningEnabled") && (
                    <FormField
                      control={form.control as any}
                      name="leaveBalanceWarningThreshold"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Warning Threshold (days)</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormDescription>
                            Send warnings when leave balance falls below this threshold
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Documentation Requirements</CardTitle>
                  <CardDescription>
                    Configure documentation requirements for leave
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control as any}
                    name="leaveDocumentationRequired"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Require Documentation
                          </FormLabel>
                          <FormDescription>
                            Require supporting documentation for certain leave types
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {form.watch("leaveDocumentationRequired") && (
                    <FormField
                      control={form.control as any}
                      name="leaveDocumentationDays"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Documentation Threshold (days)</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormDescription>
                            Require documentation for leave requests longer than this many days
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="types" className="space-y-6">
              <LeaveTypesManagement userId={userId} />
            </TabsContent>

            {/* Save Button - Fixed at bottom */}
            <div className="sticky bottom-0 bg-background border-t p-6 mt-6">
              <div className="flex justify-end">
                <Button
                  type="submit"
                  disabled={loading}
                  className="min-w-[120px]"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Settings
                    </>
                  )}
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </Tabs>
    </div>
  )
}

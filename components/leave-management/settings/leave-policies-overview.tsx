"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { useLeaveSettings } from "@/hooks/use-leave-settings"
import { 
  Calendar, 
  Clock, 
  Users, 
  Settings, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Loader2,
  RefreshCw
} from "lucide-react"

interface LeavePoliciesOverviewProps {
  userId: string
}

export function LeavePoliciesOverview({ userId }: LeavePoliciesOverviewProps) {
  const { loading, settings, getSettings } = useLeaveSettings()
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    getSettings()
  }, [getSettings])

  const handleRefresh = async () => {
    setRefreshing(true)
    await getSettings()
    setRefreshing(false)
  }

  if (loading && !settings) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading policies...</span>
        </CardContent>
      </Card>
    )
  }

  if (!settings) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <p className="text-muted-foreground">No settings found</p>
        </CardContent>
      </Card>
    )
  }

  const policies = [
    {
      title: "Leave Year",
      icon: Calendar,
      value: settings.defaultLeaveYear === 'calendar' ? 'Calendar Year' : 
             settings.defaultLeaveYear === 'fiscal' ? `Fiscal Year (${settings.fiscalYearStart})` : 
             'Anniversary-based',
      status: "active"
    },
    {
      title: "Carry-Over Policy",
      icon: Clock,
      value: settings.carryOverEnabled ? 
             `Enabled (Max: ${settings.maxCarryOverDays} days)` : 
             'Disabled',
      status: settings.carryOverEnabled ? "active" : "inactive"
    },
    {
      title: "Minimum Notice",
      icon: AlertCircle,
      value: `${settings.minNoticeDays} days`,
      status: settings.minNoticeDays > 0 ? "active" : "warning"
    },
    {
      title: "Approval Workflow",
      icon: Users,
      value: settings.approvalWorkflow === 'single' ? 'Single Approver' :
             settings.approvalWorkflow === 'multi' ? 'Multiple Approvers' :
             'Department-based',
      status: "active"
    },
    {
      title: "Auto-Approval",
      icon: CheckCircle,
      value: settings.autoApproveEnabled ? 'Enabled' : 'Disabled',
      status: settings.autoApproveEnabled ? "warning" : "active"
    },
    {
      title: "Leave Accrual",
      icon: Calendar,
      value: settings.leaveAccrualFrequency.charAt(0).toUpperCase() + 
             settings.leaveAccrualFrequency.slice(1),
      status: "active"
    },
    {
      title: "Weekend Exclusion",
      icon: Calendar,
      value: settings.weekendExclusionEnabled ? 'Enabled' : 'Disabled',
      status: settings.weekendExclusionEnabled ? "active" : "inactive"
    },
    {
      title: "Holiday Exclusion",
      icon: Calendar,
      value: settings.holidayExclusionEnabled ? 'Enabled' : 'Disabled',
      status: settings.holidayExclusionEnabled ? "active" : "inactive"
    },
    {
      title: "Half-Day Leave",
      icon: Clock,
      value: settings.halfDayLeaveEnabled ? 'Enabled' : 'Disabled',
      status: settings.halfDayLeaveEnabled ? "active" : "inactive"
    },
    {
      title: "Leave Encashment",
      icon: Settings,
      value: settings.leaveEncashmentEnabled ? 
             `Enabled (Max: ${settings.leaveEncashmentLimit} days @ ${settings.leaveEncashmentRate}%)` : 
             'Disabled',
      status: settings.leaveEncashmentEnabled ? "active" : "inactive"
    },
    {
      title: "Email Notifications",
      icon: Settings,
      value: settings.emailNotificationsEnabled ? 'Enabled' : 'Disabled',
      status: settings.emailNotificationsEnabled ? "active" : "warning"
    },
    {
      title: "Balance Warnings",
      icon: AlertCircle,
      value: settings.leaveBalanceWarningEnabled ? 
             `Enabled (Threshold: ${settings.leaveBalanceWarningThreshold} days)` : 
             'Disabled',
      status: settings.leaveBalanceWarningEnabled ? "active" : "inactive"
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "warning":
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case "inactive":
        return <XCircle className="h-4 w-4 text-gray-400" />
      default:
        return <Settings className="h-4 w-4 text-blue-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>
      case "warning":
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Review</Badge>
      case "inactive":
        return <Badge variant="outline">Inactive</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Leave Policies Overview
            </CardTitle>
            <CardDescription>
              Current leave management policies and configurations
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            {refreshing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {policies.map((policy, index) => {
            const IconComponent = policy.icon
            return (
              <div
                key={index}
                className="flex items-start space-x-3 p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
              >
                <div className="flex-shrink-0 mt-1">
                  <IconComponent className="h-5 w-5 text-muted-foreground" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <p className="text-sm font-medium text-foreground">
                      {policy.title}
                    </p>
                    {getStatusIcon(policy.status)}
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {policy.value}
                  </p>
                  {getStatusBadge(policy.status)}
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}

"use client"

import { useState, useMemo } from "react"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { But<PERSON> } from "@/components/ui/button"
import { PlusCircle, Filter, AlertCircle } from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { LeaveRequestsList } from "@/components/leave-management/leave-requests-list"
import { LeaveCalendar } from "@/components/leave-management/leave-calendar"

import { LeaveRequestDetails } from "@/components/leave-management/leave-request-details"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import type { LeaveRequest, ILeaveRequestDB } from "@/types/leave-request"
import { LeaveRequestFormOverlay } from "@/components/forms/overlays/leave-request-form-overlay"
import { LeaveTypesStatus } from "@/components/leave-management/leave-types-status"
import { useLeaveRequests } from "@/hooks/use-leave-requests"
import { format } from "date-fns"

export function LeaveManagementPage() {
  const [selectedRequest, setSelectedRequest] = useState<LeaveRequest | null>(null)
  const [filterStatus, setFilterStatus] = useState<string[]>(["all"])

  const {
    requests,
    loading,
    error,
    pagination,
    fetchRequests,
    refreshRequests
  } = useLeaveRequests({
    initialParams: { page: 1, limit: 20 },
    autoFetch: true
  });

  const handleRequestSelect = (request: LeaveRequest) => {
    setSelectedRequest(request)
  }

  // Transform database requests to frontend format for compatibility
  const transformedRequests: LeaveRequest[] = useMemo(() => {
    return requests.map((request) => ({
      id: request._id.toString(),
      employee: {
        id: request.employeeId._id?.toString() || request.employeeId.toString(),
        name: `${(request.employeeId as any).firstName || ''} ${(request.employeeId as any).lastName || ''}`.trim() || 'Unknown Employee',
        position: (request.employeeId as any).position || 'Unknown Position',
        avatar: (request.employeeId as any).avatar || '/placeholder.svg',
      },
      type: (request.leaveTypeId as any).name || 'Unknown Type',
      startDate: format(new Date(request.startDate), 'yyyy-MM-dd'),
      endDate: format(new Date(request.endDate), 'yyyy-MM-dd'),
      days: request.duration,
      reason: request.reason,
      status: request.status as "pending" | "approved" | "rejected",
      requestDate: format(new Date(request.createdAt), 'yyyy-MM-dd'),
      reviewDate: request.approvalDate ? format(new Date(request.approvalDate), 'yyyy-MM-dd') : undefined,
      reviewedBy: request.approvedBy ? {
        id: (request.approvedBy as any)._id?.toString() || request.approvedBy.toString(),
        name: `${(request.approvedBy as any).firstName || ''} ${(request.approvedBy as any).lastName || ''}`.trim() || 'Unknown Reviewer',
        avatar: (request.approvedBy as any).avatar || '/placeholder.svg',
      } : undefined,
      comments: request.rejectionReason,
    }))
  }, [requests]);

  // Filter requests based on status
  const filteredRequests = useMemo(() => {
    if (filterStatus.includes("all")) {
      return transformedRequests;
    }
    return transformedRequests.filter((request) => filterStatus.includes(request.status));
  }, [transformedRequests, filterStatus]);

  return (
    <DashboardShell>
      <DashboardHeader heading="Leave Management" text="Manage employee leave requests and balances">
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 gap-1">
                <Filter className="h-4 w-4" />
                <span>Filter</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuCheckboxItem
                checked={filterStatus.includes("all")}
                onCheckedChange={() => setFilterStatus(["all"])}
              >
                All Requests
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={filterStatus.includes("pending")}
                onCheckedChange={() => {
                  if (filterStatus.includes("pending")) {
                    setFilterStatus(filterStatus.filter((s) => s !== "pending"))
                  } else {
                    setFilterStatus([...filterStatus.filter((s) => s !== "all"), "pending"])
                  }
                }}
              >
                Pending
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={filterStatus.includes("approved")}
                onCheckedChange={() => {
                  if (filterStatus.includes("approved")) {
                    setFilterStatus(filterStatus.filter((s) => s !== "approved"))
                  } else {
                    setFilterStatus([...filterStatus.filter((s) => s !== "all"), "approved"])
                  }
                }}
              >
                Approved
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={filterStatus.includes("rejected")}
                onCheckedChange={() => {
                  if (filterStatus.includes("rejected")) {
                    setFilterStatus(filterStatus.filter((s) => s !== "rejected"))
                  } else {
                    setFilterStatus([...filterStatus.filter((s) => s !== "all"), "rejected"])
                  }
                }}
              >
                Rejected
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <LeaveRequestFormOverlay
            mode="create"
            trigger={
              <Button size="sm" className="h-8 gap-1">
                <PlusCircle className="h-4 w-4" />
                <span>Request Leave</span>
              </Button>
            }
            onSuccess={refreshRequests}
          />
        </div>
      </DashboardHeader>

      {/* Leave Management Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Requests</p>
                <p className="text-2xl font-bold text-amber-600">
                  {transformedRequests.filter(req => req.status === "pending").length}
                </p>
              </div>
              <AlertCircle className="h-8 w-8 text-amber-500" />
            </div>
          </div>
        </div>
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Approved Today</p>
                <p className="text-2xl font-bold text-green-600">
                  {transformedRequests.filter(req =>
                    req.status === "approved" &&
                    req.reviewDate === format(new Date(), 'yyyy-MM-dd')
                  ).length}
                </p>
              </div>
              <PlusCircle className="h-8 w-8 text-green-500" />
            </div>
          </div>
        </div>
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Requests</p>
                <p className="text-2xl font-bold text-blue-600">{transformedRequests.length}</p>
              </div>
              <Filter className="h-8 w-8 text-blue-500" />
            </div>
          </div>
        </div>
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Rejected</p>
                <p className="text-2xl font-bold text-red-600">
                  {transformedRequests.filter(req => req.status === "rejected").length}
                </p>
              </div>
              <AlertCircle className="h-8 w-8 text-red-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Leave Types Status */}
      <LeaveTypesStatus />

      {error && (
        <Alert variant="destructive" className="mt-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="mt-6 grid grid-cols-1 gap-8 lg:grid-cols-3">
        <div className="lg:col-span-1">
          <Tabs defaultValue="pending" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="pending">Pending</TabsTrigger>
              <TabsTrigger value="approved">Approved</TabsTrigger>
              <TabsTrigger value="all">All</TabsTrigger>
            </TabsList>
            <TabsContent value="pending" className="mt-4">
              {loading ? (
                <div className="space-y-2">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <Skeleton key={i} className="h-20 w-full" />
                  ))}
                </div>
              ) : (
                <LeaveRequestsList
                  requests={transformedRequests.filter((req) => req.status === "pending")}
                  onRequestSelect={handleRequestSelect}
                  selectedRequestId={selectedRequest?.id}
                />
              )}
            </TabsContent>
            <TabsContent value="approved" className="mt-4">
              {loading ? (
                <div className="space-y-2">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <Skeleton key={i} className="h-20 w-full" />
                  ))}
                </div>
              ) : (
                <LeaveRequestsList
                  requests={transformedRequests.filter((req) => req.status === "approved")}
                  onRequestSelect={handleRequestSelect}
                  selectedRequestId={selectedRequest?.id}
                />
              )}
            </TabsContent>
            <TabsContent value="all" className="mt-4">
              {loading ? (
                <div className="space-y-2">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <Skeleton key={i} className="h-20 w-full" />
                  ))}
                </div>
              ) : (
                <LeaveRequestsList
                  requests={transformedRequests}
                  onRequestSelect={handleRequestSelect}
                  selectedRequestId={selectedRequest?.id}
                />
              )}
            </TabsContent>
          </Tabs>
        </div>
        <div className="lg:col-span-2">
          <Tabs defaultValue="details" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="details">Request Details</TabsTrigger>
              <TabsTrigger value="calendar">Calendar View</TabsTrigger>
            </TabsList>
            <TabsContent value="details" className="mt-4">
              {selectedRequest ? (
                <LeaveRequestDetails request={selectedRequest} />
              ) : (
                <div className="flex h-[400px] items-center justify-center rounded-lg border border-dashed">
                  <div className="flex flex-col items-center text-center">
                    <h3 className="text-lg font-medium">No request selected</h3>
                    <p className="text-sm text-muted-foreground">Select a request from the list to view details</p>
                    <LeaveRequestFormOverlay
                      mode="create"
                      trigger={
                        <Button variant="outline" className="mt-4">
                          Create a new request
                        </Button>
                      }
                      onSuccess={refreshRequests}
                    />
                  </div>
                </div>
              )}
            </TabsContent>
            <TabsContent value="calendar" className="mt-4">
              <LeaveCalendar />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </DashboardShell>
  )
}

"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogT<PERSON>le, DialogTrigger } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { PlusCircle } from "lucide-react"
import { AddAttendanceForm } from "./add-attendance-form"

interface AddAttendanceDialogProps {
  onSuccess?: () => void
  defaultDate?: Date
  trigger?: React.ReactNode
}

export function AddAttendanceDialog({ onSuccess, defaultDate, trigger }: AddAttendanceDialogProps) {
  const [open, setOpen] = useState(false)

  const handleSuccess = () => {
    setOpen(false)
    onSuccess?.()
  }

  const handleCancel = () => {
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button size="sm" className="h-8 gap-1">
            <PlusCircle className="h-4 w-4" />
            <span>Record Attendance</span>
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Attendance Record</DialogTitle>
        </DialogHeader>
        <AddAttendanceForm
          onSuccess={handleSuccess}
          onCancel={handleCancel}
          defaultDate={defaultDate}
        />
      </DialogContent>
    </Dialog>
  )
}

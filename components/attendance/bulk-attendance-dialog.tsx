"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Users } from "lucide-react"
import { BulkAttendanceForm } from "./bulk-attendance-form"

interface BulkAttendanceDialogProps {
  onSuccess?: () => void
  defaultDate?: Date
  trigger?: React.ReactNode
}

export function BulkAttendanceDialog({ onSuccess, defaultDate, trigger }: BulkAttendanceDialogProps) {
  const [open, setOpen] = useState(false)

  const handleSuccess = () => {
    setOpen(false)
    onSuccess?.()
  }

  const handleCancel = () => {
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm" className="h-8 gap-1">
            <Users className="h-4 w-4" />
            <span>Bulk Record</span>
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Bulk Attendance Records</DialogTitle>
        </DialogHeader>
        <BulkAttendanceForm
          onSuccess={handleSuccess}
          onCancel={handleCancel}
          defaultDate={defaultDate}
        />
      </DialogContent>
    </Dialog>
  )
}

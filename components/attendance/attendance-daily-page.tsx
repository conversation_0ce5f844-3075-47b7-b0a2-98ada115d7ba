"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { PlusCircle, Download, Filter, Clock, CheckCircle, XCircle } from "lucide-react"
import { DatePicker } from "@/components/attendance/date-picker"
import { useAttendanceRecords } from "@/lib/hooks/use-attendance"
import { AttendanceTable } from "@/components/attendance/attendance-table"
import { EmptyState } from "@/components/empty-state"

interface AttendanceDailyPageProps {
  userId: string
}

export function AttendanceDailyPage({ userId }: AttendanceDailyPageProps) {
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [department, setDepartment] = useState<string>("all")

  // Use real API data instead of mock data
  const {
    records: selectedDateRecords,
    loading,
    error,
    refetch
  } = useAttendanceRecords({
    startDate: date ? date.toISOString().split('T')[0] : undefined,
    endDate: date ? date.toISOString().split('T')[0] : undefined,
    department: department === "all" ? undefined : department,
    autoFetch: true
  })

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-2">
          <DatePicker date={date} setDate={setDate} />
          <Button variant="outline" size="sm" className="h-8 gap-1">
            <Filter className="h-4 w-4" />
            <span>Filter</span>
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="h-8 gap-1">
            <Download className="h-4 w-4" />
            <span>Export</span>
          </Button>
          <Button size="sm" className="h-8 gap-1">
            <PlusCircle className="h-4 w-4" />
            <span>Record Attendance</span>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="record">
        <TabsList className="grid w-full grid-cols-3 md:w-[500px]">
          <TabsTrigger value="record">Record Attendance</TabsTrigger>
          <TabsTrigger value="view">View Records</TabsTrigger>
          <TabsTrigger value="corrections">Corrections</TabsTrigger>
        </TabsList>
        
        <TabsContent value="record" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Record Daily Attendance</CardTitle>
              <CardDescription>
                Record attendance for employees
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <h3 className="text-lg font-medium mb-4">Select Date</h3>
                  <Calendar
                    mode="single"
                    selected={date}
                    onSelect={setDate}
                    className="rounded-md border"
                    disabled={(date) => date > new Date()}
                  />
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-4">Quick Actions</h3>
                  <div className="space-y-2">
                    <Button className="w-full justify-start gap-2">
                      <CheckCircle className="h-4 w-4" />
                      <span>Mark All Present</span>
                    </Button>
                    <Button variant="outline" className="w-full justify-start gap-2">
                      <Clock className="h-4 w-4" />
                      <span>Bulk Check-in</span>
                    </Button>
                    <Button variant="outline" className="w-full justify-start gap-2">
                      <XCircle className="h-4 w-4" />
                      <span>Mark Absences</span>
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Employee Attendance</CardTitle>
              <CardDescription>
                Record attendance for individual employees
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-2 text-muted-foreground">Loading attendance records...</p>
                </div>
              ) : error ? (
                <div className="text-center py-8">
                  <p className="text-red-600">Error loading records: {error}</p>
                  <button
                    onClick={refetch}
                    className="mt-2 px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
                  >
                    Retry
                  </button>
                </div>
              ) : selectedDateRecords.length > 0 ? (
                <AttendanceTable
                  startDate={date ? date.toISOString().split('T')[0] : undefined}
                  endDate={date ? date.toISOString().split('T')[0] : undefined}
                  department={department === "all" ? undefined : department}
                />
              ) : (
                <EmptyState
                  title="No attendance records"
                  description="There are no attendance records for the selected date."
                  icon={<Clock className="h-10 w-10 text-muted-foreground" />}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="view" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>View Attendance Records</CardTitle>
              <CardDescription>
                View and filter attendance records
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-2 text-muted-foreground">Loading attendance records...</p>
                </div>
              ) : error ? (
                <div className="text-center py-8">
                  <p className="text-red-600">Error loading records: {error}</p>
                  <button
                    onClick={refetch}
                    className="mt-2 px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
                  >
                    Retry
                  </button>
                </div>
              ) : selectedDateRecords.length > 0 ? (
                <AttendanceTable
                  startDate={date ? date.toISOString().split('T')[0] : undefined}
                  endDate={date ? date.toISOString().split('T')[0] : undefined}
                  department={department === "all" ? undefined : department}
                />
              ) : (
                <EmptyState
                  title="No attendance records"
                  description="There are no attendance records for the selected date."
                  icon={<Clock className="h-10 w-10 text-muted-foreground" />}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="corrections" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Attendance Corrections</CardTitle>
              <CardDescription>
                Request and approve attendance corrections
              </CardDescription>
            </CardHeader>
            <CardContent>
              <EmptyState
                title="No correction requests"
                description="There are no attendance correction requests for the selected date."
                icon={<Clock className="h-10 w-10 text-muted-foreground" />}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

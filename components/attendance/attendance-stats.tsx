import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { Users, Clock, AlertTriangle, CheckCircle, AlertCircle, RefreshCw } from "lucide-react"
import { useAttendanceStats } from "@/lib/hooks/use-attendance"
import { EmptyStates } from "@/components/ui/empty-state"

interface AttendanceStatsProps {
  className?: string
  startDate?: string
  endDate?: string
  department?: string
}

export function AttendanceStats({ className, startDate, endDate, department }: AttendanceStatsProps) {
  const { stats, loading, error, refetch } = useAttendanceStats({
    startDate,
    endDate,
    department
  })

  // Handle error state with proper error display and retry functionality
  if (error) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="md:col-span-2 lg:col-span-4">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>Failed to load attendance statistics: {error}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={refetch}
                className="ml-2"
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      </div>
    )
  }

  // Handle empty state when no stats are available (and not loading)
  if (!loading && !error && !stats) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="md:col-span-2 lg:col-span-4">
          <EmptyStates.ComingSoon
            title="Attendance Statistics"
            description="Attendance statistics will be available once the attendance system is implemented. This feature is currently under development."
            size="sm"
            showBackground
          />
        </div>
      </div>
    )
  }
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card className={cn("overflow-hidden", className)}>
        <CardContent className="p-0">
          <div className="bg-gradient-to-r from-primary/20 to-primary/5 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Present Today</p>
                {loading ? (
                  <Skeleton className="h-9 w-16 mt-1" />
                ) : (
                  <h3 className="mt-1 text-3xl font-bold">{stats?.presentToday || 0}</h3>
                )}
                {loading ? (
                  <Skeleton className="h-4 w-20 mt-1" />
                ) : (
                  <p className="mt-1 flex items-center text-xs font-medium text-green-600 dark:text-green-400">
                    <span className="i-lucide-trending-up mr-1 h-3 w-3" />
                    {stats?.totalEmployees ? Math.round((stats.presentToday / stats.totalEmployees) * 100) : 0}% of total
                  </p>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/20">
                <Users className="h-6 w-6 text-primary" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className={cn("overflow-hidden", className)}>
        <CardContent className="p-0">
          <div className="bg-gradient-to-r from-amber-500/20 to-amber-500/5 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Late Check-ins</p>
                {loading ? (
                  <Skeleton className="h-9 w-16 mt-1" />
                ) : (
                  <h3 className="mt-1 text-3xl font-bold">{stats?.lateToday || 0}</h3>
                )}
                {loading ? (
                  <Skeleton className="h-4 w-20 mt-1" />
                ) : (
                  <p className="mt-1 flex items-center text-xs font-medium text-amber-600 dark:text-amber-400">
                    <span className="i-lucide-trending-up mr-1 h-3 w-3" />
                    {stats?.presentToday ? Math.round((stats.lateToday / stats.presentToday) * 100) : 0}% of present
                  </p>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-amber-500/20">
                <Clock className="h-6 w-6 text-amber-600 dark:text-amber-400" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className={cn("overflow-hidden", className)}>
        <CardContent className="p-0">
          <div className="bg-gradient-to-r from-red-500/20 to-red-500/5 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Absent</p>
                {loading ? (
                  <Skeleton className="h-9 w-16 mt-1" />
                ) : (
                  <h3 className="mt-1 text-3xl font-bold">{stats?.absentToday || 0}</h3>
                )}
                {loading ? (
                  <Skeleton className="h-4 w-20 mt-1" />
                ) : (
                  <p className="mt-1 flex items-center text-xs font-medium text-red-600 dark:text-red-400">
                    <span className="i-lucide-trending-down mr-1 h-3 w-3" />
                    {stats?.totalEmployees ? Math.round((stats.absentToday / stats.totalEmployees) * 100) : 0}% of total
                  </p>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-red-500/20">
                <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className={cn("overflow-hidden", className)}>
        <CardContent className="p-0">
          <div className="bg-gradient-to-r from-green-500/20 to-green-500/5 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Average Hours</p>
                {loading ? (
                  <Skeleton className="h-9 w-16 mt-1" />
                ) : (
                  <h3 className="mt-1 text-3xl font-bold">
                    {stats?.totalHoursWorked && stats?.presentToday
                      ? Math.round((stats.totalHoursWorked / stats.presentToday) * 10) / 10
                      : 0}
                  </h3>
                )}
                {loading ? (
                  <Skeleton className="h-4 w-20 mt-1" />
                ) : (
                  <p className="mt-1 flex items-center text-xs font-medium text-green-600 dark:text-green-400">
                    <span className="i-lucide-trending-up mr-1 h-3 w-3" />
                    {stats?.averageAttendanceRate ? Math.round(stats.averageAttendanceRate) : 0}% attendance rate
                  </p>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-500/20">
                <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

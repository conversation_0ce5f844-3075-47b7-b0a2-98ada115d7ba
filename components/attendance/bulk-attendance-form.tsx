"use client"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { CalendarIcon, Users, CheckCircle, XCircle, Clock, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { cn } from "@/lib/utils"
import { toast } from "sonner"

interface Employee {
  _id: string
  firstName: string
  lastName: string
  email: string
  employeeId: string
  position: string
  departmentId: {
    name: string
    code: string
  }
  avatar?: string
  status: string
}

interface BulkAttendanceFormProps {
  onSuccess?: () => void
  onCancel?: () => void
  defaultDate?: Date
}

export function BulkAttendanceForm({ onSuccess, onCancel, defaultDate }: BulkAttendanceFormProps) {
  const [employees, setEmployees] = useState<Employee[]>([])
  const [selectedEmployees, setSelectedEmployees] = useState<Set<string>>(new Set())
  const [date, setDate] = useState<Date>(defaultDate || new Date())
  const [bulkStatus, setBulkStatus] = useState<"present" | "absent" | "late" | "half-day">("present")
  const [loadingEmployees, setLoadingEmployees] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Fetch employees on component mount
  useEffect(() => {
    fetchEmployees()
  }, [])

  const fetchEmployees = async () => {
    try {
      setLoadingEmployees(true)
      const response = await fetch('/api/employees?limit=100&status=active')
      const data = await response.json()
      
      if (data.success) {
        setEmployees(data.data)
      } else {
        toast.error('Failed to load employees')
      }
    } catch (error) {
      console.error('Error fetching employees:', error)
      toast.error('Failed to load employees')
    } finally {
      setLoadingEmployees(false)
    }
  }

  const handleEmployeeToggle = (employeeId: string) => {
    const newSelected = new Set(selectedEmployees)
    if (newSelected.has(employeeId)) {
      newSelected.delete(employeeId)
    } else {
      newSelected.add(employeeId)
    }
    setSelectedEmployees(newSelected)
  }

  const handleSelectAll = () => {
    if (selectedEmployees.size === employees.length) {
      setSelectedEmployees(new Set())
    } else {
      setSelectedEmployees(new Set(employees.map(emp => emp._id)))
    }
  }

  const onSubmit = async () => {
    if (selectedEmployees.size === 0) {
      toast.error('Please select at least one employee')
      return
    }

    try {
      setIsSubmitting(true)
      
      const attendanceRecords = Array.from(selectedEmployees).map(employeeId => ({
        employeeId,
        date: format(date, 'yyyy-MM-dd'),
        status: bulkStatus,
        checkIn: bulkStatus !== 'absent' ? '09:00' : undefined,
        checkOut: bulkStatus !== 'absent' ? '17:00' : undefined,
        workHours: bulkStatus !== 'absent' ? 8 : 0,
        overtime: 0,
        location: 'Office',
        notes: `Bulk attendance record - ${bulkStatus}`,
      }))

      // Create attendance records in parallel
      const promises = attendanceRecords.map(record =>
        fetch('/api/attendance', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(record),
        })
      )

      const results = await Promise.allSettled(promises)
      
      const successful = results.filter(result => result.status === 'fulfilled').length
      const failed = results.filter(result => result.status === 'rejected').length

      if (successful > 0) {
        toast.success(`Successfully created ${successful} attendance records`)
      }
      
      if (failed > 0) {
        toast.error(`Failed to create ${failed} attendance records`)
      }

      if (successful > 0) {
        setSelectedEmployees(new Set())
        onSuccess?.()
      }
    } catch (error) {
      console.error('Error creating bulk attendance records:', error)
      toast.error('Failed to create attendance records')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Bulk Attendance Record
        </CardTitle>
        <CardDescription>
          Create attendance records for multiple employees at once
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Date and Status Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Date</label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !date && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={(date) => setDate(date || new Date())}
                  disabled={(date) => date > new Date() || date < new Date("2020-01-01")}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Attendance Status</label>
            <Select value={bulkStatus} onValueChange={(value) => setBulkStatus(value as any)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="present">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    Present
                  </div>
                </SelectItem>
                <SelectItem value="late">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-yellow-500" />
                    Late
                  </div>
                </SelectItem>
                <SelectItem value="half-day">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-blue-500" />
                    Half Day
                  </div>
                </SelectItem>
                <SelectItem value="absent">
                  <div className="flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    Absent
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Employee Selection */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Select Employees</h3>
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {selectedEmployees.size} of {employees.length} selected
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSelectAll}
                disabled={loadingEmployees}
              >
                {selectedEmployees.size === employees.length ? 'Deselect All' : 'Select All'}
              </Button>
            </div>
          </div>

          {loadingEmployees ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading employees...</span>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-96 overflow-y-auto border rounded-lg p-4">
              {employees.map((employee) => (
                <div
                  key={employee._id}
                  className={cn(
                    "flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-colors",
                    selectedEmployees.has(employee._id)
                      ? "bg-primary/10 border-primary"
                      : "hover:bg-muted/50"
                  )}
                  onClick={() => handleEmployeeToggle(employee._id)}
                >
                  <Checkbox
                    checked={selectedEmployees.has(employee._id)}
                    onChange={() => handleEmployeeToggle(employee._id)}
                  />
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={employee.avatar} />
                    <AvatarFallback>
                      {employee.firstName.charAt(0)}{employee.lastName.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {employee.firstName} {employee.lastName}
                    </p>
                    <p className="text-xs text-muted-foreground truncate">
                      {employee.employeeId} • {employee.departmentId?.name}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={onSubmit}
            disabled={isSubmitting || selectedEmployees.size === 0}
          >
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isSubmitting 
              ? `Creating ${selectedEmployees.size} records...` 
              : `Create ${selectedEmployees.size} Attendance Records`
            }
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

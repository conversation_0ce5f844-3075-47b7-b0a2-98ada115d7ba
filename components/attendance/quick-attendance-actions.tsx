"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, XCircle, Clock, Users, Loader2 } from "lucide-react"
import { toast } from "sonner"
import { format } from "date-fns"

interface QuickAttendanceActionsProps {
  date?: Date
  onSuccess?: () => void
}

export function QuickAttendanceActions({ date = new Date(), onSuccess }: QuickAttendanceActionsProps) {
  const [isProcessing, setIsProcessing] = useState<string | null>(null)

  const handleQuickAction = async (action: 'all-present' | 'all-absent' | 'mark-late') => {
    try {
      setIsProcessing(action)
      
      // First, fetch all active employees
      const employeesResponse = await fetch('/api/employees?limit=100&status=active')
      const employeesData = await employeesResponse.json()
      
      if (!employeesData.success) {
        toast.error('Failed to load employees')
        return
      }

      const employees = employeesData.data
      
      // Prepare attendance records based on action
      let attendanceRecords = []
      
      switch (action) {
        case 'all-present':
          attendanceRecords = employees.map((emp: any) => ({
            employeeId: emp._id,
            date: format(date, 'yyyy-MM-dd'),
            status: 'present',
            checkIn: '09:00',
            checkOut: '17:00',
            workHours: 8,
            overtime: 0,
            location: 'Office',
            notes: 'Quick action - All present',
          }))
          break
          
        case 'all-absent':
          attendanceRecords = employees.map((emp: any) => ({
            employeeId: emp._id,
            date: format(date, 'yyyy-MM-dd'),
            status: 'absent',
            workHours: 0,
            overtime: 0,
            notes: 'Quick action - All absent',
          }))
          break
          
        case 'mark-late':
          attendanceRecords = employees.map((emp: any) => ({
            employeeId: emp._id,
            date: format(date, 'yyyy-MM-dd'),
            status: 'late',
            checkIn: '09:30',
            checkOut: '17:00',
            workHours: 7.5,
            overtime: 0,
            location: 'Office',
            notes: 'Quick action - Marked late',
          }))
          break
      }

      // Create attendance records in parallel
      const promises = attendanceRecords.map(record =>
        fetch('/api/attendance', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(record),
        })
      )

      const results = await Promise.allSettled(promises)
      
      const successful = results.filter(result => result.status === 'fulfilled').length
      const failed = results.filter(result => result.status === 'rejected').length

      if (successful > 0) {
        toast.success(`Successfully created ${successful} attendance records`)
        onSuccess?.()
      }
      
      if (failed > 0) {
        toast.error(`Failed to create ${failed} attendance records`)
      }

    } catch (error) {
      console.error('Error in quick attendance action:', error)
      toast.error('Failed to process attendance records')
    } finally {
      setIsProcessing(null)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Quick Actions
        </CardTitle>
        <CardDescription>
          Quickly mark attendance for all employees on {format(date, "PPP")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <Button
            variant="outline"
            className="h-auto p-4 flex flex-col items-center gap-2"
            onClick={() => handleQuickAction('all-present')}
            disabled={isProcessing !== null}
          >
            {isProcessing === 'all-present' ? (
              <Loader2 className="h-6 w-6 animate-spin" />
            ) : (
              <CheckCircle className="h-6 w-6 text-green-500" />
            )}
            <div className="text-center">
              <div className="font-medium">Mark All Present</div>
              <div className="text-xs text-muted-foreground">9:00 AM - 5:00 PM</div>
            </div>
          </Button>

          <Button
            variant="outline"
            className="h-auto p-4 flex flex-col items-center gap-2"
            onClick={() => handleQuickAction('mark-late')}
            disabled={isProcessing !== null}
          >
            {isProcessing === 'mark-late' ? (
              <Loader2 className="h-6 w-6 animate-spin" />
            ) : (
              <Clock className="h-6 w-6 text-yellow-500" />
            )}
            <div className="text-center">
              <div className="font-medium">Mark All Late</div>
              <div className="text-xs text-muted-foreground">9:30 AM - 5:00 PM</div>
            </div>
          </Button>

          <Button
            variant="outline"
            className="h-auto p-4 flex flex-col items-center gap-2"
            onClick={() => handleQuickAction('all-absent')}
            disabled={isProcessing !== null}
          >
            {isProcessing === 'all-absent' ? (
              <Loader2 className="h-6 w-6 animate-spin" />
            ) : (
              <XCircle className="h-6 w-6 text-red-500" />
            )}
            <div className="text-center">
              <div className="font-medium">Mark All Absent</div>
              <div className="text-xs text-muted-foreground">No work hours</div>
            </div>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

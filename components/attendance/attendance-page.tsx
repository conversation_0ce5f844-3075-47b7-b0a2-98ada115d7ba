"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { PlusCircle, Download, Filter } from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { AttendanceCalendar } from "@/components/attendance/attendance-calendar"
import { AttendanceTable } from "@/components/attendance/attendance-table"
import { AttendanceStats } from "@/components/attendance/attendance-stats"
import { DatePicker } from "@/components/attendance/date-picker"
import { AddAttendanceDialog } from "@/components/attendance/add-attendance-dialog"
import { BulkAttendanceDialog } from "@/components/attendance/bulk-attendance-dialog"
import { QuickAttendanceActions } from "@/components/attendance/quick-attendance-actions"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface AttendancePageProps {
  userId: string
}

export function AttendancePage({ userId }: AttendancePageProps) {
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [department, setDepartment] = useState<string[]>(["all"])
  const [refreshKey, setRefreshKey] = useState(0)
  const [showQuickActions, setShowQuickActions] = useState(false)

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-2">
          <DatePicker date={date} setDate={setDate} />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 gap-1">
                <Filter className="h-4 w-4" />
                <span>Department</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuCheckboxItem
                checked={department.includes("all")}
                onCheckedChange={() => setDepartment(["all"])}
              >
                All Departments
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={department.includes("engineering")}
                onCheckedChange={() => {
                  if (department.includes("engineering")) {
                    setDepartment(department.filter((d) => d !== "engineering"))
                  } else {
                    setDepartment([...department.filter((d) => d !== "all"), "engineering"])
                  }
                }}
              >
                Engineering
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={department.includes("marketing")}
                onCheckedChange={() => {
                  if (department.includes("marketing")) {
                    setDepartment(department.filter((d) => d !== "marketing"))
                  } else {
                    setDepartment([...department.filter((d) => d !== "all"), "marketing"])
                  }
                }}
              >
                Marketing
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={department.includes("hr")}
                onCheckedChange={() => {
                  if (department.includes("hr")) {
                    setDepartment(department.filter((d) => d !== "hr"))
                  } else {
                    setDepartment([...department.filter((d) => d !== "all"), "hr"])
                  }
                }}
              >
                Human Resources
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={department.includes("finance")}
                onCheckedChange={() => {
                  if (department.includes("finance")) {
                    setDepartment(department.filter((d) => d !== "finance"))
                  } else {
                    setDepartment([...department.filter((d) => d !== "all"), "finance"])
                  }
                }}
              >
                Finance
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="h-8 gap-1">
            <Download className="h-4 w-4" />
            <span>Export</span>
          </Button>
          <BulkAttendanceDialog
            defaultDate={date}
            onSuccess={() => setRefreshKey(prev => prev + 1)}
          />
          <AddAttendanceDialog
            defaultDate={date}
            onSuccess={() => setRefreshKey(prev => prev + 1)}
          />
        </div>
      </div>

      {/* Quick Actions - Collapsible */}
      <Collapsible open={showQuickActions} onOpenChange={setShowQuickActions}>
        <CollapsibleTrigger asChild>
          <Button variant="ghost" className="w-full justify-between p-0 h-auto">
            <span className="text-sm font-medium">Quick Attendance Actions</span>
            <ChevronDown className={`h-4 w-4 transition-transform ${showQuickActions ? 'rotate-180' : ''}`} />
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="mt-4">
          <QuickAttendanceActions
            date={date}
            onSuccess={() => setRefreshKey(prev => prev + 1)}
          />
        </CollapsibleContent>
      </Collapsible>

      <AttendanceStats
        key={`stats-${refreshKey}`}
        startDate={date ? date.toISOString().split('T')[0] : undefined}
        endDate={date ? date.toISOString().split('T')[0] : undefined}
        department={department.includes("all") ? undefined : department.join(",")}
      />

      <Card>
        <CardHeader>
          <CardTitle>Attendance Overview</CardTitle>
          <CardDescription>
            View and manage employee attendance records
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="table" className="mt-2">
            <TabsList className="grid w-full grid-cols-2 md:w-[400px]">
              <TabsTrigger value="table">Table View</TabsTrigger>
              <TabsTrigger value="calendar">Calendar View</TabsTrigger>
            </TabsList>
            <TabsContent value="table" className="mt-4">
              <AttendanceTable
                key={`table-${refreshKey}`}
                startDate={date ? date.toISOString().split('T')[0] : undefined}
                endDate={date ? date.toISOString().split('T')[0] : undefined}
                department={department.includes("all") ? undefined : department.join(",")}
              />
            </TabsContent>
            <TabsContent value="calendar" className="mt-4">
              <AttendanceCalendar />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}

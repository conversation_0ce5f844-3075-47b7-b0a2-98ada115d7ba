"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { format } from "date-fns"
import { CalendarIcon, Clock, User, MapPin, FileText, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"
import { toast } from "sonner"

// Form validation schema
const attendanceFormSchema = z.object({
  employeeId: z.string().min(1, "Please select an employee"),
  date: z.date({
    required_error: "Please select a date",
  }),
  checkInTime: z.string().optional(),
  checkOutTime: z.string().optional(),
  status: z.enum(["present", "absent", "late", "half-day"], {
    required_error: "Please select attendance status",
  }),
  workHours: z.number().min(0).max(24).optional(),
  overtimeHours: z.number().min(0).max(24).optional(),
  location: z.string().optional(),
  notes: z.string().optional(),
})

type AttendanceFormData = z.infer<typeof attendanceFormSchema>

interface Employee {
  _id: string
  firstName: string
  lastName: string
  email: string
  employeeId: string
  position: string
  departmentId: {
    name: string
    code: string
  }
  avatar?: string
  status: string
}

interface AddAttendanceFormProps {
  onSuccess?: () => void
  onCancel?: () => void
  defaultDate?: Date
}

export function AddAttendanceForm({ onSuccess, onCancel, defaultDate }: AddAttendanceFormProps) {
  const [employees, setEmployees] = useState<Employee[]>([])
  const [loadingEmployees, setLoadingEmployees] = useState(false)
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<AttendanceFormData>({
    resolver: zodResolver(attendanceFormSchema),
    defaultValues: {
      date: defaultDate || new Date(),
      status: "present",
      workHours: 8,
      overtimeHours: 0,
    },
  })

  // Fetch employees on component mount
  useEffect(() => {
    fetchEmployees()
  }, [])

  const fetchEmployees = async () => {
    try {
      setLoadingEmployees(true)
      const response = await fetch('/api/employees?limit=100&status=active')
      const data = await response.json()
      
      if (data.success) {
        setEmployees(data.data)
      } else {
        toast.error('Failed to load employees')
      }
    } catch (error) {
      console.error('Error fetching employees:', error)
      toast.error('Failed to load employees')
    } finally {
      setLoadingEmployees(false)
    }
  }

  const onSubmit = async (data: AttendanceFormData) => {
    try {
      setIsSubmitting(true)
      
      // Prepare attendance record data
      const attendanceData = {
        employeeId: data.employeeId,
        date: format(data.date, 'yyyy-MM-dd'),
        checkIn: data.checkInTime,
        checkOut: data.checkOutTime,
        status: data.status,
        workHours: data.workHours,
        overtime: data.overtimeHours,
        location: data.location,
        notes: data.notes,
      }

      const response = await fetch('/api/attendance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(attendanceData),
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Attendance record created successfully')
        form.reset()
        setSelectedEmployee(null)
        onSuccess?.()
      } else {
        toast.error(result.message || 'Failed to create attendance record')
      }
    } catch (error) {
      console.error('Error creating attendance record:', error)
      toast.error('Failed to create attendance record')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleEmployeeSelect = (employeeId: string) => {
    const employee = employees.find(emp => emp._id === employeeId)
    setSelectedEmployee(employee || null)
    form.setValue('employeeId', employeeId)
  }

  const calculateWorkHours = () => {
    const checkIn = form.watch('checkInTime')
    const checkOut = form.watch('checkOutTime')
    
    if (checkIn && checkOut) {
      const checkInTime = new Date(`2000-01-01T${checkIn}:00`)
      const checkOutTime = new Date(`2000-01-01T${checkOut}:00`)
      
      if (checkOutTime > checkInTime) {
        const diffMs = checkOutTime.getTime() - checkInTime.getTime()
        const diffHours = diffMs / (1000 * 60 * 60)
        form.setValue('workHours', Math.round(diffHours * 100) / 100)
      }
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Record Attendance
        </CardTitle>
        <CardDescription>
          Create a new attendance record for an employee
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Employee Selection */}
          <div className="space-y-2">
            <Label htmlFor="employee">Employee *</Label>
            <Select onValueChange={handleEmployeeSelect} disabled={loadingEmployees}>
              <SelectTrigger>
                <SelectValue placeholder={loadingEmployees ? "Loading employees..." : "Select an employee"} />
              </SelectTrigger>
              <SelectContent>
                {employees.map((employee) => (
                  <SelectItem key={employee._id} value={employee._id}>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={employee.avatar} />
                        <AvatarFallback>
                          {employee.firstName.charAt(0)}{employee.lastName.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {employee.firstName} {employee.lastName}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {employee.employeeId} • {employee.departmentId?.name}
                        </span>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.employeeId && (
              <p className="text-sm text-destructive">{form.formState.errors.employeeId.message}</p>
            )}
          </div>

          {/* Selected Employee Info */}
          {selectedEmployee && (
            <Alert>
              <User className="h-4 w-4" />
              <AlertDescription>
                <div className="flex items-center gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={selectedEmployee.avatar} />
                    <AvatarFallback>
                      {selectedEmployee.firstName.charAt(0)}{selectedEmployee.lastName.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">
                      {selectedEmployee.firstName} {selectedEmployee.lastName}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {selectedEmployee.position} • {selectedEmployee.departmentId?.name}
                    </p>
                  </div>
                  <Badge variant="outline">{selectedEmployee.employeeId}</Badge>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Date Selection */}
          <div className="space-y-2">
            <Label>Date *</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !form.watch('date') && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {form.watch('date') ? format(form.watch('date'), "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={form.watch('date')}
                  onSelect={(date) => form.setValue('date', date || new Date())}
                  disabled={(date) => date > new Date() || date < new Date("2020-01-01")}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            {form.formState.errors.date && (
              <p className="text-sm text-destructive">{form.formState.errors.date.message}</p>
            )}
          </div>

          {/* Status Selection */}
          <div className="space-y-2">
            <Label htmlFor="status">Attendance Status *</Label>
            <Select onValueChange={(value) => form.setValue('status', value as any)}>
              <SelectTrigger>
                <SelectValue placeholder="Select attendance status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="present">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    Present
                  </div>
                </SelectItem>
                <SelectItem value="late">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full" />
                    Late
                  </div>
                </SelectItem>
                <SelectItem value="half-day">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    Half Day
                  </div>
                </SelectItem>
                <SelectItem value="absent">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full" />
                    Absent
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            {form.formState.errors.status && (
              <p className="text-sm text-destructive">{form.formState.errors.status.message}</p>
            )}
          </div>

          {/* Time Fields - Only show if not absent */}
          {form.watch('status') !== 'absent' && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="checkInTime">Check In Time</Label>
                <div className="relative">
                  <Clock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="time"
                    className="pl-10"
                    {...form.register('checkInTime')}
                    onChange={(e) => {
                      form.setValue('checkInTime', e.target.value)
                      setTimeout(calculateWorkHours, 100)
                    }}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="checkOutTime">Check Out Time</Label>
                <div className="relative">
                  <Clock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="time"
                    className="pl-10"
                    {...form.register('checkOutTime')}
                    onChange={(e) => {
                      form.setValue('checkOutTime', e.target.value)
                      setTimeout(calculateWorkHours, 100)
                    }}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Work Hours - Only show if not absent */}
          {form.watch('status') !== 'absent' && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="workHours">Work Hours</Label>
                <Input
                  type="number"
                  step="0.5"
                  min="0"
                  max="24"
                  placeholder="8.0"
                  {...form.register('workHours', { valueAsNumber: true })}
                />
                <p className="text-xs text-muted-foreground">
                  Automatically calculated from check-in/out times
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="overtimeHours">Overtime Hours</Label>
                <Input
                  type="number"
                  step="0.5"
                  min="0"
                  max="24"
                  placeholder="0.0"
                  {...form.register('overtimeHours', { valueAsNumber: true })}
                />
                <p className="text-xs text-muted-foreground">
                  Additional hours beyond regular work time
                </p>
              </div>
            </div>
          )}

          {/* Location */}
          <div className="space-y-2">
            <Label htmlFor="location">Work Location</Label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                className="pl-10"
                placeholder="Office, Remote, Client Site, etc."
                {...form.register('location')}
              />
            </div>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <div className="relative">
              <FileText className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Textarea
                className="pl-10 min-h-[80px]"
                placeholder="Additional notes about attendance (optional)"
                {...form.register('notes')}
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !form.watch('employeeId') || !form.watch('date')}
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isSubmitting ? 'Creating...' : 'Create Attendance Record'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

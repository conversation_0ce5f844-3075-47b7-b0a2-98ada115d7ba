// components\attendance\attendance-table.tsx
"use client"

import { useState, useEffect, useMemo } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Search, ChevronLeft, ChevronRight, RefreshCw, AlertCircle } from "lucide-react"
import { useAttendanceRecords } from "@/lib/hooks/use-attendance"
import type { AttendanceRecord } from "@/types/attendance"

interface AttendanceTableProps {
  startDate?: string
  endDate?: string
  employeeId?: string
  department?: string
  status?: string
}

export function AttendanceTable({
  startDate,
  endDate,
  employeeId,
  department,
  status
}: AttendanceTableProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const recordsPerPage = 10

  // Use real API data instead of mock data
  const {
    records,
    total,
    loading,
    error,
    refetch
  } = useAttendanceRecords({
    startDate,
    endDate,
    employeeId,
    department,
    status,
    page: currentPage,
    limit: recordsPerPage
  })

  // Client-side filtering for search
  const filteredRecords = useMemo(() => {
    if (!searchQuery) return records

    return records.filter((record) => {
      const employeeName = record.employee?.name || record.employeeName || ''
      const employeeDept = record.employee?.department || record.department || ''

      return employeeName.toLowerCase().includes(searchQuery.toLowerCase()) ||
             employeeDept.toLowerCase().includes(searchQuery.toLowerCase())
    })
  }, [records, searchQuery])

  const totalPages = Math.ceil(total / recordsPerPage)

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1)
  }, [searchQuery, startDate, endDate, employeeId, department, status])

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search employees..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={refetch}
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Employee</TableHead>
              <TableHead>Department</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Check In</TableHead>
              <TableHead>Check Out</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Hours</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              // Loading skeletons
              Array.from({ length: recordsPerPage }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-8 w-8 rounded-full" />
                      <Skeleton className="h-4 w-32" />
                    </div>
                  </TableCell>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-12" /></TableCell>
                </TableRow>
              ))
            ) : filteredRecords.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <div className="text-muted-foreground">
                    {searchQuery ? "No records found matching your search." : "No attendance records found."}
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredRecords.map((record) => (
                <TableRow key={record.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage
                          src={record.employee?.avatar || "/placeholder.svg"}
                          alt={record.employee?.name || record.employeeName || 'Employee'}
                        />
                        <AvatarFallback>
                          {(record.employee?.name || record.employeeName || 'E').charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="font-medium">
                        {record.employee?.name || record.employeeName || 'Unknown Employee'}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>{record.employee?.department || record.department || '-'}</TableCell>
                  <TableCell>{new Date(record.date).toLocaleDateString()}</TableCell>
                  <TableCell>
                    {record.checkIn ? (
                      <span
                        className={
                          record.status === "late" ? "text-amber-600 dark:text-amber-400" : "text-foreground"
                        }
                      >
                        {typeof record.checkIn === 'string' ? record.checkIn : record.checkIn.time}
                      </span>
                    ) : (
                      "-"
                    )}
                  </TableCell>
                  <TableCell>
                    {record.checkOut ? (
                      typeof record.checkOut === 'string' ? record.checkOut : record.checkOut.time
                    ) : "-"}
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        record.status === "present"
                          ? "default"
                          : record.status === "absent"
                            ? "destructive"
                            : record.status === "late"
                              ? "outline"
                              : "secondary"
                      }
                      className={
                        record.status === "present" ? "bg-green-500" : record.status === "absent" ? "bg-red-500" : ""
                      }
                    >
                      {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                    </Badge>
                  </TableCell>
                  <TableCell>{record.hoursWorked || record.workHours || "-"}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          {loading ? (
            <Skeleton className="h-4 w-48" />
          ) : (
            `Showing ${((currentPage - 1) * recordsPerPage) + 1} to ${Math.min(currentPage * recordsPerPage, total)} of ${total} records`
          )}
        </p>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(currentPage - 1)}
            disabled={currentPage === 1 || loading}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="text-sm text-muted-foreground">
            Page {currentPage} of {totalPages || 1}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(currentPage + 1)}
            disabled={currentPage === totalPages || loading}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

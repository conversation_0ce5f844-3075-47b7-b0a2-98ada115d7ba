import mongoose, { Schema, Document } from 'mongoose';

/**
 * Interface for leave settings document
 */
export interface ILeaveSettings extends Document {
  // General Settings
  carryOverEnabled: boolean;
  maxCarryOverDays: number;
  minNoticeDays: number;
  autoApproveEnabled: boolean;
  approvalWorkflow: 'single' | 'multi' | 'department';
  
  // Leave Year Settings
  defaultLeaveYear: 'calendar' | 'fiscal' | 'anniversary';
  fiscalYearStart: string; // Format: DD-MM
  
  // Accrual Settings
  leaveAccrualFrequency: 'monthly' | 'quarterly' | 'annually';
  proRataCalculationEnabled: boolean;
  holidayExclusionEnabled: boolean;
  weekendExclusionEnabled: boolean;
  halfDayLeaveEnabled: boolean;
  hourlyLeaveEnabled: boolean;
  
  // Encashment Settings
  leaveEncashmentEnabled: boolean;
  leaveEncashmentLimit: number;
  leaveEncashmentRate: number; // Percentage
  
  // Request Management
  leaveRequestCancellationEnabled: boolean;
  maxCancellationDays: number;
  
  // Balance & Warnings
  leaveBalanceDisplayEnabled: boolean;
  leaveBalanceWarningThreshold: number;
  leaveBalanceWarningEnabled: boolean;
  
  // Documentation
  leaveDocumentationRequired: boolean;
  leaveDocumentationDays: number; // Days threshold for requiring documentation
  
  // Notification Settings
  emailNotificationsEnabled: boolean;
  calendarIntegrationEnabled: boolean;
  
  // System Fields
  organizationId?: mongoose.Types.ObjectId;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for leave settings
 */
const LeaveSettingsSchema: Schema = new Schema(
  {
    // General Settings
    carryOverEnabled: {
      type: Boolean,
      default: true,
    },
    maxCarryOverDays: {
      type: Number,
      default: 5,
      min: 0,
    },
    minNoticeDays: {
      type: Number,
      default: 3,
      min: 0,
    },
    autoApproveEnabled: {
      type: Boolean,
      default: false,
    },
    approvalWorkflow: {
      type: String,
      enum: ['single', 'multi', 'department'],
      default: 'single',
    },
    
    // Leave Year Settings
    defaultLeaveYear: {
      type: String,
      enum: ['calendar', 'fiscal', 'anniversary'],
      default: 'calendar',
    },
    fiscalYearStart: {
      type: String,
      default: '01-04', // April 1st
      validate: {
        validator: function(v: string) {
          return /^\d{2}-\d{2}$/.test(v);
        },
        message: 'Fiscal year start must be in DD-MM format'
      }
    },
    
    // Accrual Settings
    leaveAccrualFrequency: {
      type: String,
      enum: ['monthly', 'quarterly', 'annually'],
      default: 'monthly',
    },
    proRataCalculationEnabled: {
      type: Boolean,
      default: true,
    },
    holidayExclusionEnabled: {
      type: Boolean,
      default: true,
    },
    weekendExclusionEnabled: {
      type: Boolean,
      default: true,
    },
    halfDayLeaveEnabled: {
      type: Boolean,
      default: true,
    },
    hourlyLeaveEnabled: {
      type: Boolean,
      default: false,
    },
    
    // Encashment Settings
    leaveEncashmentEnabled: {
      type: Boolean,
      default: false,
    },
    leaveEncashmentLimit: {
      type: Number,
      default: 10,
      min: 0,
    },
    leaveEncashmentRate: {
      type: Number,
      default: 100,
      min: 0,
      max: 200,
    },
    
    // Request Management
    leaveRequestCancellationEnabled: {
      type: Boolean,
      default: true,
    },
    maxCancellationDays: {
      type: Number,
      default: 2,
      min: 0,
    },
    
    // Balance & Warnings
    leaveBalanceDisplayEnabled: {
      type: Boolean,
      default: true,
    },
    leaveBalanceWarningThreshold: {
      type: Number,
      default: 5,
      min: 0,
    },
    leaveBalanceWarningEnabled: {
      type: Boolean,
      default: true,
    },
    
    // Documentation
    leaveDocumentationRequired: {
      type: Boolean,
      default: false,
    },
    leaveDocumentationDays: {
      type: Number,
      default: 3,
      min: 1,
    },
    
    // Notification Settings
    emailNotificationsEnabled: {
      type: Boolean,
      default: true,
    },
    calendarIntegrationEnabled: {
      type: Boolean,
      default: true,
    },
    
    // System Fields
    organizationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Organization',
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes for faster queries
LeaveSettingsSchema.index({ organizationId: 1 });
LeaveSettingsSchema.index({ createdBy: 1 });

// Ensure only one settings document per organization (or global if no organizationId)
LeaveSettingsSchema.index({ organizationId: 1 }, { unique: true, sparse: true });

// Create and export the model
const LeaveSettings = mongoose.models.LeaveSettings || mongoose.model<ILeaveSettings>('LeaveSettings', LeaveSettingsSchema);

export default LeaveSettings;

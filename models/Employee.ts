import mongoose, { Document, Model, Schema } from 'mongoose';

// Define the interface for the Employee document
export interface IEmployee extends Document {
  employeeId: string;
  employeeNumber?: string; // Added for compatibility with accounting module
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: Date;
  gender?: string;
  maritalStatus?: 'single' | 'married' | 'divorced' | 'widowed';
  numberOfChildren?: number;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  village?: string;
  traditionalAuthority?: string;
  district?: string;
  nationalId?: string;
  departmentId?: mongoose.Types.ObjectId;
  department?: string; // Department name for dynamic lookup and validation
  position: string;
  roleId?: mongoose.Types.ObjectId; // Reference to Role model for salary calculation
  managerId?: mongoose.Types.ObjectId;
  employmentType: 'full-time' | 'part-time' | 'contract' | 'intern' | 'temporary' | 'volunteer';
  employmentSubType?: 'standard' | 'attendance-based' | 'task-based' | 'hybrid'; // New field for salary calculation type
  employmentStatus: 'active' | 'inactive' | 'on-leave' | 'terminated';
  hireDate: Date;
  terminationDate?: Date;
  salary?: number; // Deprecated - use EmployeeSalary model instead
  hourlyRate?: number; // For hourly employees (part-time, attendance-based)

  // Salary calculation rules
  salaryCalculationRules?: {
    attendanceRequired?: boolean; // Whether attendance affects salary
    taskCompletionRequired?: boolean; // Whether task completion affects salary
    minimumHours?: number; // Minimum hours required for full pay
    overtimeMultiplier?: number; // Overtime rate multiplier (e.g., 1.5 for time and half)
    performanceBonusEnabled?: boolean; // Whether performance bonuses are enabled
    taskRateOverride?: number; // Override task rate for this employee
  };
  bankName?: string;
  bankAccountNumber?: string;
  nextOfKin?: {
    name?: string;
    relationship?: string;
    phone?: string;
    address?: string;
  };
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelationship?: string;
  photo?: string;
  notes?: string;

  // Access control fields
  addedBy: mongoose.Types.ObjectId; // Reference to User who added this employee
  lastModifiedBy: mongoose.Types.ObjectId; // Reference to User who last modified this employee
  accessibleBy: mongoose.Types.ObjectId[]; // Array of User IDs who can access this employee record

  // Security fields
  isBlocked: boolean;
  blockedReason?: string;
  blockedBy?: mongoose.Types.ObjectId;
  blockedAt?: Date;

  createdAt: Date;
  updatedAt: Date;
}

// Create the Employee schema
const employeeSchema = new Schema<IEmployee>(
  {
    employeeId: {
      type: String,
      required: [true, 'Employee ID is required'],
      unique: true,
      trim: true
    },
    employeeNumber: {
      type: String,
      trim: true,
      // Do not add unique constraint - it causes issues with bulk imports
      // unique: true,
      required: [true, 'Employee Number is required'], // Make it required to avoid null values
      default: function() {
        // Generate a unique employee number if not provided
        const timestamp = Date.now().toString().slice(-6);
        const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
        return `EMP-${timestamp}-${random}`;
      }
    },
    firstName: {
      type: String,
      required: [true, 'First name is required'],
      trim: true
    },
    lastName: {
      type: String,
      required: [true, 'Last name is required'],
      trim: true
    },
    email: {
      type: String,
      required: [true, 'Email is required'],
      unique: true,
      lowercase: true,
      trim: true,
      validate: {
        validator: function(v: string) {
          return /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/.test(v);
        },
        message: (props: any) => `${props.value} is not a valid email address!`
      }
    },
    phone: {
      type: String
    },
    dateOfBirth: {
      type: Date
    },
    gender: {
      type: String
    },
    maritalStatus: {
      type: String,
      enum: ['single', 'married', 'divorced', 'widowed']
    },
    numberOfChildren: {
      type: Number,
      min: 0
    },
    address: {
      type: String
    },
    city: {
      type: String
    },
    state: {
      type: String
    },
    postalCode: {
      type: String
    },
    country: {
      type: String
    },
    village: {
      type: String
    },
    traditionalAuthority: {
      type: String
    },
    district: {
      type: String,
      default: '' // Ensure district has a default value
    },
    nationalId: {
      type: String
    },
    departmentId: {
      type: Schema.Types.ObjectId,
      ref: 'Department'
    },
    department: {
      type: String,
      trim: true,
      index: true // Add index for faster lookups
    },
    position: {
      type: String,
      required: [true, 'Position is required'],
      default: '' // Ensure position has a default value even though it's required
    },
    roleId: {
      type: Schema.Types.ObjectId,
      ref: 'Role',
      index: true // Add index for faster role-based queries
    },
    managerId: {
      type: Schema.Types.ObjectId,
      ref: 'Employee'
    },
    employmentType: {
      type: String,
      enum: ['full-time', 'part-time', 'contract', 'intern', 'temporary', 'volunteer'],
      default: 'full-time'
    },
    employmentSubType: {
      type: String,
      enum: ['standard', 'attendance-based', 'task-based', 'hybrid'],
      default: 'standard'
    },
    employmentStatus: {
      type: String,
      enum: ['active', 'inactive', 'on-leave', 'terminated'],
      default: 'active'
    },
    hireDate: {
      type: Date,
      required: [true, 'Hire date is required']
    },
    terminationDate: {
      type: Date
    },
    salary: {
      type: Number,
      // Add deprecation comment
      // Note: This field is deprecated. Use EmployeeSalary model for salary management
    },
    hourlyRate: {
      type: Number,
      min: 0,
      validate: {
        validator: function(this: IEmployee, value: number) {
          // Require hourly rate for part-time and attendance-based employees
          if (['part-time'].includes(this.employmentType) ||
              ['attendance-based', 'hybrid'].includes(this.employmentSubType || '')) {
            return value != null && value > 0;
          }
          return true;
        },
        message: 'Hourly rate is required for part-time and attendance-based employees'
      }
    },
    salaryCalculationRules: {
      attendanceRequired: {
        type: Boolean,
        default: function(this: IEmployee) {
          return ['part-time'].includes(this.employmentType) ||
                 ['attendance-based', 'hybrid'].includes(this.employmentSubType || '');
        }
      },
      taskCompletionRequired: {
        type: Boolean,
        default: function(this: IEmployee) {
          return ['contract', 'intern'].includes(this.employmentType) ||
                 ['task-based', 'hybrid'].includes(this.employmentSubType || '');
        }
      },
      minimumHours: {
        type: Number,
        min: 0,
        default: function(this: IEmployee) {
          // Default minimum hours based on employment type
          if (this.employmentType === 'full-time') return 160; // ~8 hours/day * 20 days
          if (this.employmentType === 'part-time') return 80; // ~4 hours/day * 20 days
          return 0; // No minimum for contract/intern/temporary
        }
      },
      overtimeMultiplier: {
        type: Number,
        min: 1,
        max: 3,
        default: 1.5 // Standard time and half
      },
      performanceBonusEnabled: {
        type: Boolean,
        default: function(this: IEmployee) {
          return ['intern', 'contract'].includes(this.employmentType) ||
                 ['task-based', 'hybrid'].includes(this.employmentSubType || '');
        }
      },
      taskRateOverride: {
        type: Number,
        min: 0
      }
    },
    bankName: {
      type: String
    },
    bankAccountNumber: {
      type: String
    },
    nextOfKin: {
      name: { type: String },
      relationship: { type: String },
      phone: { type: String },
      address: { type: String }
    },
    emergencyContactName: {
      type: String
    },
    emergencyContactPhone: {
      type: String
    },
    emergencyContactRelationship: {
      type: String
    },
    photo: {
      type: String
    },
    notes: {
      type: String
    },

    // Access control fields
    addedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    lastModifiedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    accessibleBy: [{
      type: Schema.Types.ObjectId,
      ref: 'User'
    }],

    // Security fields
    isBlocked: {
      type: Boolean,
      default: false
    },
    blockedReason: {
      type: String
    },
    blockedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    blockedAt: {
      type: Date
    }
  },
  {
    timestamps: true
  }
);

// Add pre-save hooks
employeeSchema.pre('save', function(next) {
  const employee = this as IEmployee;

  // Update status based on termination date
  if (employee.terminationDate && employee.terminationDate <= new Date()) {
    employee.employmentStatus = 'terminated';
  }

  // Ensure employeeNumber is set - this is critical to avoid null values
  if (!employee.employeeNumber) {
    // If employeeId exists, use it as employeeNumber
    if (employee.employeeId) {
      employee.employeeNumber = employee.employeeId;
    } else {
      // Generate a random employeeNumber as a fallback
      const timestamp = Date.now().toString().slice(-6);
      const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
      employee.employeeNumber = `EMP-${timestamp}-${random}`;
    }
  }

  // Ensure position is set and is a string
  if (employee.position === undefined || employee.position === null) {
    employee.position = ''; // Default to empty string if not set
  } else if (typeof employee.position !== 'string') {
    employee.position = String(employee.position); // Convert to string if not already
  }

  // Ensure district is set and is a string
  if (employee.district === undefined || employee.district === null) {
    employee.district = ''; // Default to empty string if not set
  } else if (typeof employee.district !== 'string') {
    employee.district = String(employee.district); // Convert to string if not already
  }

  next();
});

// Create or get the model
export const Employee = mongoose.models.Employee ||
  mongoose.model<IEmployee>('Employee', employeeSchema);

// Add default export for backward compatibility
const EmployeeModel = mongoose.models.Employee || mongoose.model<IEmployee>('Employee', employeeSchema);
export default EmployeeModel;

import mongoose, { Schema, Document } from 'mongoose';

/**
 * Interface for employee allowance document
 */
export interface IEmployeeAllowance {
  allowanceId: mongoose.Types.ObjectId;
  name: string;
  amount?: number;
  percentage?: number;
  isTaxable: boolean;
  isPensionable: boolean;
}

/**
 * Interface for employee deduction document
 */
export interface IEmployeeDeduction {
  deductionId: mongoose.Types.ObjectId;
  name: string;
  amount?: number;
  percentage?: number;
  isStatutory: boolean;
}

/**
 * Interface for employee salary document
 */
/**
 * Interface for task rate configuration
 */
export interface ITaskRate {
  taskType: string; // e.g., 'development', 'design', 'testing', 'documentation'
  rate: number; // Rate per task or per hour
  rateType: 'per_task' | 'per_hour' | 'per_project'; // How the rate is calculated
  description?: string;
}

/**
 * Interface for attendance requirement configuration
 */
export interface IAttendanceRequirement {
  minimumHoursPerDay?: number; // Minimum hours required per day
  minimumHoursPerWeek?: number; // Minimum hours required per week
  minimumHoursPerMonth?: number; // Minimum hours required per month
  overtimeThreshold?: number; // Hours after which overtime applies
  lateDeductionRate?: number; // Deduction rate for late arrivals
  absenceDeductionRate?: number; // Deduction rate for absences
}

export interface IEmployeeSalary extends Document {
  employeeId: mongoose.Types.ObjectId;
  salaryStructureId: mongoose.Types.ObjectId;
  basicSalary: number;
  currency: string;
  effectiveDate: Date;
  endDate?: Date;
  isActive: boolean;

  // New fields for variable salary calculation
  calculationType: 'fixed' | 'hourly' | 'task-based' | 'hybrid'; // How salary is calculated
  hourlyRate?: number; // For hourly employees
  taskRates?: ITaskRate[]; // For task-based employees
  attendanceRequirement?: IAttendanceRequirement; // Attendance requirements

  allowances: IEmployeeAllowance[];
  deductions: IEmployeeDeduction[];
  bankName?: string;
  bankAccountNumber?: string;
  bankBranchCode?: string;
  paymentMethod: 'bank_transfer' | 'cash' | 'check' | 'mobile_money';
  taxId?: string;
  pensionScheme?: string;
  pensionNumber?: string;
  notes?: string;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for employee allowance
 */
const EmployeeAllowanceSchema: Schema = new Schema({
  allowanceId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Allowance',
    required: true,
  },
  name: {
    type: String,
    required: true,
    trim: true,
  },
  amount: {
    type: Number,
  },
  percentage: {
    type: Number,
  },
  isTaxable: {
    type: Boolean,
    required: true,
    default: true,
  },
  isPensionable: {
    type: Boolean,
    required: true,
    default: false,
  },
});

/**
 * Schema for employee deduction
 */
const EmployeeDeductionSchema: Schema = new Schema({
  deductionId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Deduction',
    required: true,
  },
  name: {
    type: String,
    required: true,
    trim: true,
  },
  amount: {
    type: Number,
  },
  percentage: {
    type: Number,
  },
  isStatutory: {
    type: Boolean,
    required: true,
    default: false,
  },
});

/**
 * Schema for task rate
 */
const TaskRateSchema: Schema = new Schema({
  taskType: {
    type: String,
    required: true,
    trim: true,
  },
  rate: {
    type: Number,
    required: true,
    min: 0,
  },
  rateType: {
    type: String,
    required: true,
    enum: ['per_task', 'per_hour', 'per_project'],
    default: 'per_task',
  },
  description: {
    type: String,
    trim: true,
  },
});

/**
 * Schema for attendance requirement
 */
const AttendanceRequirementSchema: Schema = new Schema({
  minimumHoursPerDay: {
    type: Number,
    min: 0,
    max: 24,
  },
  minimumHoursPerWeek: {
    type: Number,
    min: 0,
    max: 168, // 24 * 7
  },
  minimumHoursPerMonth: {
    type: Number,
    min: 0,
    max: 744, // 24 * 31
  },
  overtimeThreshold: {
    type: Number,
    min: 0,
    default: 8, // Standard 8-hour workday
  },
  lateDeductionRate: {
    type: Number,
    min: 0,
    max: 1, // Percentage (0-1)
    default: 0, // No deduction by default
  },
  absenceDeductionRate: {
    type: Number,
    min: 0,
    max: 1, // Percentage (0-1)
    default: 1, // Full day deduction by default
  },
});

/**
 * Schema for employee salary
 */
const EmployeeSalarySchema: Schema = new Schema(
  {
    employeeId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Employee',
      required: true,
    },
    salaryStructureId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SalaryStructure',
      required: true,
    },
    basicSalary: {
      type: Number,
      required: true,
    },
    currency: {
      type: String,
      required: true,
      default: 'MWK',
      trim: true,
    },
    calculationType: {
      type: String,
      required: true,
      enum: ['fixed', 'hourly', 'task-based', 'hybrid'],
      default: 'fixed',
    },
    hourlyRate: {
      type: Number,
      min: 0,
      validate: {
        validator: function(this: IEmployeeSalary, value: number) {
          // Require hourly rate for hourly and hybrid calculation types
          if (['hourly', 'hybrid'].includes(this.calculationType)) {
            return value != null && value > 0;
          }
          return true;
        },
        message: 'Hourly rate is required for hourly and hybrid calculation types'
      }
    },
    taskRates: [TaskRateSchema],
    attendanceRequirement: AttendanceRequirementSchema,
    effectiveDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
    },
    isActive: {
      type: Boolean,
      required: true,
      default: true,
    },
    allowances: [EmployeeAllowanceSchema],
    deductions: [EmployeeDeductionSchema],
    bankName: {
      type: String,
      trim: true,
    },
    bankAccountNumber: {
      type: String,
      trim: true,
    },
    bankBranchCode: {
      type: String,
      trim: true,
    },
    paymentMethod: {
      type: String,
      required: true,
      enum: ['bank_transfer', 'cash', 'check', 'mobile_money'],
      default: 'bank_transfer',
    },
    taxId: {
      type: String,
      trim: true,
    },
    pensionScheme: {
      type: String,
      trim: true,
    },
    pensionNumber: {
      type: String,
      trim: true,
    },
    notes: {
      type: String,
      trim: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes for faster queries
EmployeeSalarySchema.index({ employeeId: 1, isActive: 1 });
EmployeeSalarySchema.index({ salaryStructureId: 1 });
EmployeeSalarySchema.index({ effectiveDate: -1 });

// Create and export the model
const EmployeeSalary = mongoose.models.EmployeeSalary || mongoose.model<IEmployeeSalary>('EmployeeSalary', EmployeeSalarySchema);

export default EmployeeSalary;

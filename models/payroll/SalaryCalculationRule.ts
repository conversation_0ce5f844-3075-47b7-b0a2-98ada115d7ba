import mongoose, { Schema, Document } from 'mongoose';

/**
 * Interface for condition in salary calculation rule
 */
export interface ICondition {
  field: string; // Field to check (e.g., 'attendanceRate', 'completionRate', 'qualityScore')
  operator: 'gt' | 'gte' | 'lt' | 'lte' | 'eq' | 'ne' | 'in' | 'nin'; // Comparison operator
  value: any; // Value to compare against
  logicalOperator?: 'and' | 'or'; // How to combine with next condition
}

/**
 * Interface for action in salary calculation rule
 */
export interface IAction {
  type: 'add_bonus' | 'apply_deduction' | 'multiply_rate' | 'set_minimum' | 'set_maximum';
  amount?: number; // Fixed amount for add_bonus/apply_deduction
  percentage?: number; // Percentage for multiply_rate
  description: string; // Description of the action
}

/**
 * Interface for salary calculation rule document
 * Flexible rules engine for different employee types and performance metrics
 */
export interface ISalaryCalculationRule extends Document {
  name: string;
  description?: string;
  isActive: boolean;
  
  // Rule applicability
  employmentTypes: string[]; // Which employment types this rule applies to
  employmentSubTypes: string[]; // Which employment sub-types this rule applies to
  departments?: mongoose.Types.ObjectId[]; // Specific departments (optional)
  roles?: mongoose.Types.ObjectId[]; // Specific roles (optional)
  
  // Rule priority and execution
  priority: number; // Higher number = higher priority
  executionOrder: number; // Order of execution within same priority
  
  // Rule conditions
  conditions: ICondition[]; // Conditions that must be met
  conditionsLogic: 'all' | 'any'; // Whether ALL or ANY conditions must be met
  
  // Rule actions
  actions: IAction[]; // Actions to take when conditions are met
  
  // Rule metadata
  effectiveDate: Date; // When this rule becomes effective
  expiryDate?: Date; // When this rule expires (optional)
  
  // Audit fields
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for condition
 */
const ConditionSchema: Schema = new Schema({
  field: {
    type: String,
    required: true,
    trim: true,
  },
  operator: {
    type: String,
    required: true,
    enum: ['gt', 'gte', 'lt', 'lte', 'eq', 'ne', 'in', 'nin'],
  },
  value: {
    type: Schema.Types.Mixed,
    required: true,
  },
  logicalOperator: {
    type: String,
    enum: ['and', 'or'],
  },
});

/**
 * Schema for action
 */
const ActionSchema: Schema = new Schema({
  type: {
    type: String,
    required: true,
    enum: ['add_bonus', 'apply_deduction', 'multiply_rate', 'set_minimum', 'set_maximum'],
  },
  amount: {
    type: Number,
    min: 0,
  },
  percentage: {
    type: Number,
    min: 0,
    max: 1000, // Allow up to 1000% for extreme cases
  },
  description: {
    type: String,
    required: true,
    trim: true,
  },
});

/**
 * Schema for salary calculation rule
 */
const SalaryCalculationRuleSchema: Schema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    description: {
      type: String,
      trim: true,
    },
    isActive: {
      type: Boolean,
      required: true,
      default: true,
    },
    employmentTypes: [{
      type: String,
      required: true,
      enum: ['full-time', 'part-time', 'contract', 'intern', 'temporary', 'volunteer'],
    }],
    employmentSubTypes: [{
      type: String,
      required: true,
      enum: ['standard', 'attendance-based', 'task-based', 'hybrid'],
    }],
    departments: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Department',
    }],
    roles: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Role',
    }],
    priority: {
      type: Number,
      required: true,
      min: 1,
      max: 100,
      default: 50,
    },
    executionOrder: {
      type: Number,
      required: true,
      min: 1,
      default: 1,
    },
    conditions: [ConditionSchema],
    conditionsLogic: {
      type: String,
      required: true,
      enum: ['all', 'any'],
      default: 'all',
    },
    actions: [ActionSchema],
    effectiveDate: {
      type: Date,
      required: true,
      default: Date.now,
    },
    expiryDate: {
      type: Date,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes for efficient queries
SalaryCalculationRuleSchema.index({ isActive: 1, priority: -1, executionOrder: 1 });
SalaryCalculationRuleSchema.index({ employmentTypes: 1 });
SalaryCalculationRuleSchema.index({ employmentSubTypes: 1 });
SalaryCalculationRuleSchema.index({ effectiveDate: 1, expiryDate: 1 });
SalaryCalculationRuleSchema.index({ departments: 1 });
SalaryCalculationRuleSchema.index({ roles: 1 });

// Add pre-save validation
SalaryCalculationRuleSchema.pre('save', function(next) {
  const rule = this as ISalaryCalculationRule;
  
  // Ensure expiry date is after effective date
  if (rule.expiryDate && rule.expiryDate <= rule.effectiveDate) {
    return next(new Error('Expiry date must be after effective date'));
  }
  
  // Validate that conditions array is not empty
  if (rule.conditions.length === 0) {
    return next(new Error('At least one condition is required'));
  }
  
  // Validate that actions array is not empty
  if (rule.actions.length === 0) {
    return next(new Error('At least one action is required'));
  }
  
  // Validate action amounts/percentages
  for (const action of rule.actions) {
    if (['add_bonus', 'apply_deduction', 'set_minimum', 'set_maximum'].includes(action.type)) {
      if (action.amount == null || action.amount < 0) {
        return next(new Error(`Action ${action.type} requires a valid amount`));
      }
    }
    if (action.type === 'multiply_rate') {
      if (action.percentage == null || action.percentage < 0) {
        return next(new Error('multiply_rate action requires a valid percentage'));
      }
    }
  }
  
  next();
});

// Add static method to find applicable rules
SalaryCalculationRuleSchema.statics.findApplicableRules = function(
  employmentType: string,
  employmentSubType: string,
  departmentId?: string,
  roleId?: string
) {
  const query: any = {
    isActive: true,
    employmentTypes: employmentType,
    employmentSubTypes: employmentSubType,
    effectiveDate: { $lte: new Date() },
    $or: [
      { expiryDate: { $exists: false } },
      { expiryDate: { $gte: new Date() } }
    ]
  };
  
  // Add department filter if specified
  if (departmentId) {
    query.$and = query.$and || [];
    query.$and.push({
      $or: [
        { departments: { $size: 0 } }, // No department restriction
        { departments: departmentId }
      ]
    });
  }
  
  // Add role filter if specified
  if (roleId) {
    query.$and = query.$and || [];
    query.$and.push({
      $or: [
        { roles: { $size: 0 } }, // No role restriction
        { roles: roleId }
      ]
    });
  }
  
  return this.find(query).sort({ priority: -1, executionOrder: 1 });
};

// Create and export the model
const SalaryCalculationRule = mongoose.models.SalaryCalculationRule || 
  mongoose.model<ISalaryCalculationRule>('SalaryCalculationRule', SalaryCalculationRuleSchema);

export default SalaryCalculationRule;

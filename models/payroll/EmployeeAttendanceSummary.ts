import mongoose, { Schema, Document } from 'mongoose';

/**
 * Interface for employee attendance summary document
 * Monthly aggregation of attendance data for payroll calculation
 */
export interface IEmployeeAttendanceSummary extends Document {
  employeeId: mongoose.Types.ObjectId;
  year: number;
  month: number; // 1-12
  
  // Attendance statistics
  totalWorkingDays: number; // Total working days in the month
  daysPresent: number; // Days employee was present
  daysAbsent: number; // Days employee was absent
  daysLate: number; // Days employee was late
  daysHalfDay: number; // Half-day attendances
  daysOnLeave: number; // Days on approved leave
  
  // Hours statistics
  totalHours: number; // Total hours worked
  regularHours: number; // Regular working hours
  overtimeHours: number; // Overtime hours
  lateHours: number; // Hours lost due to late arrivals
  
  // Performance metrics
  attendanceRate: number; // Percentage of days present (0-100)
  punctualityRate: number; // Percentage of on-time arrivals (0-100)
  averageHoursPerDay: number; // Average hours worked per day
  
  // Deductions and bonuses
  lateDeductions: number; // Amount deducted for late arrivals
  absenceDeductions: number; // Amount deducted for absences
  overtimeBonus: number; // Bonus for overtime work
  perfectAttendanceBonus: number; // Bonus for perfect attendance
  
  // Calculation metadata
  calculatedAt: Date; // When this summary was calculated
  calculatedBy: mongoose.Types.ObjectId; // User who triggered calculation
  isFinalized: boolean; // Whether this summary is finalized for payroll
  finalizedAt?: Date; // When this summary was finalized
  finalizedBy?: mongoose.Types.ObjectId; // User who finalized
  
  // Audit fields
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for employee attendance summary
 */
const EmployeeAttendanceSummarySchema: Schema = new Schema(
  {
    employeeId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Employee',
      required: true,
    },
    year: {
      type: Number,
      required: true,
      min: 2020,
      max: 2100,
    },
    month: {
      type: Number,
      required: true,
      min: 1,
      max: 12,
    },
    totalWorkingDays: {
      type: Number,
      required: true,
      min: 0,
      max: 31,
    },
    daysPresent: {
      type: Number,
      required: true,
      min: 0,
      max: 31,
    },
    daysAbsent: {
      type: Number,
      required: true,
      min: 0,
      max: 31,
    },
    daysLate: {
      type: Number,
      required: true,
      min: 0,
      max: 31,
    },
    daysHalfDay: {
      type: Number,
      required: true,
      min: 0,
      max: 31,
    },
    daysOnLeave: {
      type: Number,
      required: true,
      min: 0,
      max: 31,
    },
    totalHours: {
      type: Number,
      required: true,
      min: 0,
    },
    regularHours: {
      type: Number,
      required: true,
      min: 0,
    },
    overtimeHours: {
      type: Number,
      required: true,
      min: 0,
    },
    lateHours: {
      type: Number,
      required: true,
      min: 0,
    },
    attendanceRate: {
      type: Number,
      required: true,
      min: 0,
      max: 100,
    },
    punctualityRate: {
      type: Number,
      required: true,
      min: 0,
      max: 100,
    },
    averageHoursPerDay: {
      type: Number,
      required: true,
      min: 0,
      max: 24,
    },
    lateDeductions: {
      type: Number,
      required: true,
      min: 0,
      default: 0,
    },
    absenceDeductions: {
      type: Number,
      required: true,
      min: 0,
      default: 0,
    },
    overtimeBonus: {
      type: Number,
      required: true,
      min: 0,
      default: 0,
    },
    perfectAttendanceBonus: {
      type: Number,
      required: true,
      min: 0,
      default: 0,
    },
    calculatedAt: {
      type: Date,
      required: true,
      default: Date.now,
    },
    calculatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    isFinalized: {
      type: Boolean,
      required: true,
      default: false,
    },
    finalizedAt: {
      type: Date,
    },
    finalizedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

// Add compound indexes for efficient queries
EmployeeAttendanceSummarySchema.index({ employeeId: 1, year: 1, month: 1 }, { unique: true });
EmployeeAttendanceSummarySchema.index({ year: 1, month: 1 });
EmployeeAttendanceSummarySchema.index({ isFinalized: 1 });
EmployeeAttendanceSummarySchema.index({ calculatedAt: -1 });

// Add pre-save validation
EmployeeAttendanceSummarySchema.pre('save', function(next) {
  const summary = this as IEmployeeAttendanceSummary;
  
  // Validate that days don't exceed total working days
  const totalDays = summary.daysPresent + summary.daysAbsent;
  if (totalDays > summary.totalWorkingDays) {
    return next(new Error('Total present and absent days cannot exceed total working days'));
  }
  
  // Calculate attendance rate
  if (summary.totalWorkingDays > 0) {
    summary.attendanceRate = (summary.daysPresent / summary.totalWorkingDays) * 100;
  } else {
    summary.attendanceRate = 0;
  }
  
  // Calculate punctuality rate
  if (summary.daysPresent > 0) {
    const onTimeDays = summary.daysPresent - summary.daysLate;
    summary.punctualityRate = (onTimeDays / summary.daysPresent) * 100;
  } else {
    summary.punctualityRate = 0;
  }
  
  // Calculate average hours per day
  if (summary.daysPresent > 0) {
    summary.averageHoursPerDay = summary.totalHours / summary.daysPresent;
  } else {
    summary.averageHoursPerDay = 0;
  }
  
  next();
});

// Create and export the model
const EmployeeAttendanceSummary = mongoose.models.EmployeeAttendanceSummary || 
  mongoose.model<IEmployeeAttendanceSummary>('EmployeeAttendanceSummary', EmployeeAttendanceSummarySchema);

export default EmployeeAttendanceSummary;

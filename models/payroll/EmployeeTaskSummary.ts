import mongoose, { Schema, Document } from 'mongoose';

/**
 * Interface for task completion detail
 */
export interface ITaskCompletion {
  taskId: mongoose.Types.ObjectId;
  taskTitle: string;
  taskType: string; // e.g., 'development', 'design', 'testing'
  completedDate: Date;
  estimatedHours: number;
  actualHours: number;
  qualityScore?: number; // 1-10 quality rating
  difficultyLevel?: 'easy' | 'medium' | 'hard' | 'expert';
  paymentAmount: number; // Amount to be paid for this task
  paymentType: 'per_task' | 'per_hour' | 'per_project';
}

/**
 * Interface for employee task summary document
 * Monthly aggregation of task completion data for payroll calculation
 */
export interface IEmployeeTaskSummary extends Document {
  employeeId: mongoose.Types.ObjectId;
  year: number;
  month: number; // 1-12
  
  // Task statistics
  totalTasksAssigned: number; // Total tasks assigned in the month
  totalTasksCompleted: number; // Total tasks completed
  totalTasksInProgress: number; // Tasks still in progress
  totalTasksOverdue: number; // Tasks that are overdue
  
  // Time statistics
  totalEstimatedHours: number; // Total estimated hours for all tasks
  totalActualHours: number; // Total actual hours spent
  averageTaskCompletionTime: number; // Average hours per completed task
  efficiencyRatio: number; // Actual hours / Estimated hours (lower is better)
  
  // Performance metrics
  completionRate: number; // Percentage of tasks completed (0-100)
  onTimeCompletionRate: number; // Percentage completed on time (0-100)
  averageQualityScore: number; // Average quality score (1-10)
  
  // Payment calculations
  totalTaskPayment: number; // Total payment for completed tasks
  bonusPayment: number; // Performance-based bonus
  penaltyDeduction: number; // Deductions for poor performance
  
  // Task breakdown by type
  taskCompletions: ITaskCompletion[]; // Detailed task completion records
  
  // Calculation metadata
  calculatedAt: Date; // When this summary was calculated
  calculatedBy: mongoose.Types.ObjectId; // User who triggered calculation
  isFinalized: boolean; // Whether this summary is finalized for payroll
  finalizedAt?: Date; // When this summary was finalized
  finalizedBy?: mongoose.Types.ObjectId; // User who finalized
  
  // Audit fields
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for task completion
 */
const TaskCompletionSchema: Schema = new Schema({
  taskId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Task',
    required: true,
  },
  taskTitle: {
    type: String,
    required: true,
    trim: true,
  },
  taskType: {
    type: String,
    required: true,
    trim: true,
  },
  completedDate: {
    type: Date,
    required: true,
  },
  estimatedHours: {
    type: Number,
    required: true,
    min: 0,
  },
  actualHours: {
    type: Number,
    required: true,
    min: 0,
  },
  qualityScore: {
    type: Number,
    min: 1,
    max: 10,
  },
  difficultyLevel: {
    type: String,
    enum: ['easy', 'medium', 'hard', 'expert'],
  },
  paymentAmount: {
    type: Number,
    required: true,
    min: 0,
  },
  paymentType: {
    type: String,
    required: true,
    enum: ['per_task', 'per_hour', 'per_project'],
  },
});

/**
 * Schema for employee task summary
 */
const EmployeeTaskSummarySchema: Schema = new Schema(
  {
    employeeId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Employee',
      required: true,
    },
    year: {
      type: Number,
      required: true,
      min: 2020,
      max: 2100,
    },
    month: {
      type: Number,
      required: true,
      min: 1,
      max: 12,
    },
    totalTasksAssigned: {
      type: Number,
      required: true,
      min: 0,
    },
    totalTasksCompleted: {
      type: Number,
      required: true,
      min: 0,
    },
    totalTasksInProgress: {
      type: Number,
      required: true,
      min: 0,
    },
    totalTasksOverdue: {
      type: Number,
      required: true,
      min: 0,
    },
    totalEstimatedHours: {
      type: Number,
      required: true,
      min: 0,
    },
    totalActualHours: {
      type: Number,
      required: true,
      min: 0,
    },
    averageTaskCompletionTime: {
      type: Number,
      required: true,
      min: 0,
    },
    efficiencyRatio: {
      type: Number,
      required: true,
      min: 0,
    },
    completionRate: {
      type: Number,
      required: true,
      min: 0,
      max: 100,
    },
    onTimeCompletionRate: {
      type: Number,
      required: true,
      min: 0,
      max: 100,
    },
    averageQualityScore: {
      type: Number,
      required: true,
      min: 0,
      max: 10,
    },
    totalTaskPayment: {
      type: Number,
      required: true,
      min: 0,
      default: 0,
    },
    bonusPayment: {
      type: Number,
      required: true,
      min: 0,
      default: 0,
    },
    penaltyDeduction: {
      type: Number,
      required: true,
      min: 0,
      default: 0,
    },
    taskCompletions: [TaskCompletionSchema],
    calculatedAt: {
      type: Date,
      required: true,
      default: Date.now,
    },
    calculatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    isFinalized: {
      type: Boolean,
      required: true,
      default: false,
    },
    finalizedAt: {
      type: Date,
    },
    finalizedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

// Add compound indexes for efficient queries
EmployeeTaskSummarySchema.index({ employeeId: 1, year: 1, month: 1 }, { unique: true });
EmployeeTaskSummarySchema.index({ year: 1, month: 1 });
EmployeeTaskSummarySchema.index({ isFinalized: 1 });
EmployeeTaskSummarySchema.index({ calculatedAt: -1 });

// Add pre-save validation and calculations
EmployeeTaskSummarySchema.pre('save', function(next) {
  const summary = this as IEmployeeTaskSummary;
  
  // Calculate completion rate
  if (summary.totalTasksAssigned > 0) {
    summary.completionRate = (summary.totalTasksCompleted / summary.totalTasksAssigned) * 100;
  } else {
    summary.completionRate = 0;
  }
  
  // Calculate efficiency ratio
  if (summary.totalEstimatedHours > 0) {
    summary.efficiencyRatio = summary.totalActualHours / summary.totalEstimatedHours;
  } else {
    summary.efficiencyRatio = 0;
  }
  
  // Calculate average task completion time
  if (summary.totalTasksCompleted > 0) {
    summary.averageTaskCompletionTime = summary.totalActualHours / summary.totalTasksCompleted;
  } else {
    summary.averageTaskCompletionTime = 0;
  }
  
  // Calculate average quality score
  const qualityScores = summary.taskCompletions
    .filter(task => task.qualityScore != null)
    .map(task => task.qualityScore!);
  
  if (qualityScores.length > 0) {
    summary.averageQualityScore = qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length;
  } else {
    summary.averageQualityScore = 0;
  }
  
  next();
});

// Create and export the model
const EmployeeTaskSummary = mongoose.models.EmployeeTaskSummary || 
  mongoose.model<IEmployeeTaskSummary>('EmployeeTaskSummary', EmployeeTaskSummarySchema);

export default EmployeeTaskSummary;

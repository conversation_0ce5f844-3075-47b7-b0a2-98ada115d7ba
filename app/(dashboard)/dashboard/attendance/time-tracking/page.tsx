import { Metada<PERSON> } from "next"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Clock, Timer } from "lucide-react"

export const metadata: Metadata = {
  title: "Time Tracking | Kawandama HR System",
  description: "Track work hours and time spent on tasks",
}

export default function TimeTrackingPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Time Tracking"
        text="Track work hours and time spent on tasks"
      />
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Timer className="h-5 w-5" />
            Time Tracking
          </CardTitle>
          <CardDescription>
            Monitor work hours and productivity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium">Time Tracking Coming Soon</h3>
              <p className="text-muted-foreground">
                Advanced time tracking features will be available in the next update
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </DashboardShell>
  )
}

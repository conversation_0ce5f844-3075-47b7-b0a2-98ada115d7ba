import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { AttendancePage } from '@/components/attendance/attendance-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Attendance Dashboard | Kawandama HR System',
  description: 'Track and manage employee attendance',
};

export default async function AttendanceDashboardPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions - Allow employees to view their own attendance
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN,
    UserRole.SYSTEM_ADMIN,
    UserRole.HR_DIRECTOR,
    UserRole.HR_MANAGER,
    UserRole.HR_SPECIALIST,
    UserRole.DEPARTMENT_HEAD,
    UserRole.TEAM_LEADER,
    UserRole.EMPLOYEE
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell
      heading="Attendance Dashboard"
      text="Track and manage employee attendance"
    >
      <AttendancePage userId={session.user.id} />
    </DashboardShell>
  );
}

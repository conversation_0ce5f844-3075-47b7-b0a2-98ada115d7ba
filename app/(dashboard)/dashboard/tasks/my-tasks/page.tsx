import { Metada<PERSON> } from "next"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { MyTasksPage } from "@/components/task-management/my-tasks-page"

export const metadata: Metadata = {
  title: "My Tasks | Kawandama HR System",
  description: "View and manage your assigned tasks",
}

export default function MyTasksPageRoute() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="My Tasks"
        text="View and manage your assigned tasks"
      />
      <MyTasksPage />
    </DashboardShell>
  )
}

import { <PERSON>ada<PERSON> } from "next"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BarChart3, ListTodo } from "lucide-react"

export const metadata: Metadata = {
  title: "Task Reports | Kawandama HR System",
  description: "View task analytics and reports",
}

export default function TaskReportsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Task Reports"
        text="View task analytics and performance reports"
      />
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Task Reports & Analytics
          </CardTitle>
          <CardDescription>
            Comprehensive task performance and productivity reports
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium">Reports Coming Soon</h3>
              <p className="text-muted-foreground">
                Task analytics and reporting features will be available in the next update
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </DashboardShell>
  )
}

import { Metada<PERSON> } from "next"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar, ListTodo } from "lucide-react"

export const metadata: Metadata = {
  title: "Task Calendar | Kawandama HR System",
  description: "View tasks in calendar format",
}

export default function TaskCalendarPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Task Calendar"
        text="View and manage tasks in calendar format"
      />
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Task Calendar
          </CardTitle>
          <CardDescription>
            Calendar view of all tasks and deadlines
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <ListTodo className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium">Calendar View Coming Soon</h3>
              <p className="text-muted-foreground">
                Task calendar integration will be available in the next update
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </DashboardShell>
  )
}

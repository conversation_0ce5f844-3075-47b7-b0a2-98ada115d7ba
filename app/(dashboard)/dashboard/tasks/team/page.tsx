import { <PERSON>ada<PERSON> } from "next"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { TeamTasksPage } from "@/components/task-management/team-tasks-page"

export const metadata: Metadata = {
  title: "Team Tasks | Kawandama HR System",
  description: "View and manage team tasks and assignments",
}

export default function TeamTasksPageRoute() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Team Tasks"
        text="View and manage team tasks and assignments"
      />
      <TeamTasksPage />
    </DashboardShell>
  )
}

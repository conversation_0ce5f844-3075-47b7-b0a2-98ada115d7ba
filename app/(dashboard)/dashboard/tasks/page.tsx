import { <PERSON>ada<PERSON> } from "next"
import { DashboardShell } from "@/components/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard-header"
import { TaskManagementDashboard } from "@/components/task-management/task-management-dashboard"

export const metadata: Metadata = {
  title: "Task Management | Kawandama HR System",
  description: "Manage tasks, projects, and team productivity",
}

export default function TaskManagementPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Task Management"
        text="Manage tasks, track progress, and collaborate with your team"
      />
      <TaskManagementDashboard />
    </DashboardShell>
  )
}

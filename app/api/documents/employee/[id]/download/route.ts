import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { documentService } from '@/services/document/DocumentService';
import { documentUploadService } from '@/services/document/DocumentUploadService';

export const runtime = 'nodejs';

// app/api/documents/employee/[id]/download/route.ts
// Custom auth system doesn't require authOptions;
/**
 * GET /api/documents/employee/[id]/download
 * Download an employee document
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Resolve the params promise
    const { id } = await params;
    // Get document
    const document = await documentService.getEmployeeDocumentById(id);
    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }
    // Check if user has permission to download this document
    const isOwnDocument = document.employeeId._id.toString() === user.id;
    const isPublicDocument = document.isPublic;
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);
    if (!isOwnDocument && !isPublicDocument && !hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Get file
    const { file, contentType } = await documentUploadService.getDocument(document.filePath);
    // Create response with file
    // Convert Buffer to Uint8Array which is acceptable for NextResponse
    const uint8Array = new Uint8Array(file);
    const response = new NextResponse(uint8Array, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${document.fileName}"`,
        'Content-Length': file.length.toString()
      }
    });
    return response;
  } catch (error: unknown) {
    logger.error('Error downloading employee document', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
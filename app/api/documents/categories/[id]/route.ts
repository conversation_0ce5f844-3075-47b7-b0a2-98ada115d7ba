import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { documentCategoryService } from '@/services/document/DocumentCategoryService';

export const runtime = 'nodejs';

// app/api/documents/categories/[id]/route.ts
// Custom auth system doesn't require authOptions;
/**
 * GET /api/documents/categories/[id]
 * Get a document category by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Resolve the params promise
    const { id } = await params;
    // Get category
    const category = await documentCategoryService.getCategoryById(id);
    if (!category) {
      return NextResponse.json(
        { error: 'Document category not found' },
        { status: 404 }
      );
    }
    return NextResponse.json(category);
  } catch (error: unknown) {
    logger.error('Error getting document category', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
/**
 * PUT /api/documents/categories/[id]
 * Update a document category
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Resolve the params promise
    const { id } = await params;
    // Get request body
    const body = await req.json();
    // Set updated by
    body.updatedBy = user.id;
    // Update category
    const category = await documentCategoryService.updateCategory(id, body);
    if (!category) {
      return NextResponse.json(
        { error: 'Document category not found' },
        { status: 404 }
      );
    }
    return NextResponse.json({
      success: true,
      message: 'Document category updated successfully',
      data: category
    });
  } catch (error: unknown) {
    logger.error('Error updating document category', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
/**
 * DELETE /api/documents/categories/[id]
 * Delete a document category
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Resolve the params promise
    const { id } = await params;
    // Delete category
    const category = await documentCategoryService.deleteCategory(id);
    if (!category) {
      return NextResponse.json(
        { error: 'Document category not found' },
        { status: 404 }
      );
    }
    return NextResponse.json({
      success: true,
      message: 'Document category deleted successfully'
    });
  } catch (error: unknown) {
    logger.error('Error deleting document category', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
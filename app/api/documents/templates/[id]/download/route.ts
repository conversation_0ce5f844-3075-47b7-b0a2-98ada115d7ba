import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { documentTemplateService } from '@/services/document/DocumentTemplateService';
import { documentUploadService } from '@/services/document/DocumentUploadService';

export const runtime = 'nodejs';

/**
 * GET /api/documents/templates/[id]/download
 * Download a document template
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Resolve the params promise
    const { id } = await params;
    // Get template
    const template = await documentTemplateService.getTemplateById(id);
    if (!template) {
      return NextResponse.json(
        { error: 'Document template not found' },
        { status: 404 }
      );
    }
    // Get file
    const { file, contentType } = await documentUploadService.getDocument(template.filePath);
    // Create response with file
    // Convert Buffer to Uint8Array which is acceptable for NextResponse
    const uint8Array = new Uint8Array(file);
    const response = new NextResponse(uint8Array, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${template.fileName}"`,
        'Content-Length': file.length.toString()
      }
    });
    return response;
  } catch (error: unknown) {
    logger.error('Error downloading document template', LogCategory.API, error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
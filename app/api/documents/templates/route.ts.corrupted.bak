import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { documentTemplateService } from '@/services/document/DocumentTemplateService';
import { documentUploadService } from '@/services/document/DocumentUploadService';
import { parseFormData } from '@/lib/backend/utils/form-parser';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * GET /api/documents/templates
 * Get document templates
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const categoryId = searchParams.get('categoryId') || undefined;
    const isActive = searchParams.get('isActive') === 'true';
    const search = searchParams.get('search') || undefined;
    const sortField = searchParams.get('sortField') || 'name';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    // Get templates
    const result = await documentTemplateService.getTemplates({
      categoryId,
      isActive,
      search,
      page,
      limit,
      sortField,
      sortOrder: sortOrder as 'asc' | 'desc'
    });

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error getting document templates', LogCategory.API, error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

/**
 * POST /api/documents/templates
 * Upload a new document template
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Parse form data
    const formData = await req.formData();
    const { fields, files } = await parseFormData(formData);

    // Get file
    const file = files.file;
    if (!file) {
      return NextResponse.json(
        { error: 'No file uploaded' },
        { status: 400 }
      );
    }

    // Validate required fields
    if (!fields.name || !fields.code || !fields.categoryId) {
      return NextResponse.json(
        { error: 'Missing required fields: name, code, categoryId' },
        { status: 400 }
      );
    }

    // Upload file
    const uploadResult = await documentUploadService.uploadDocument(
      file.data,
      file.name,
      fields.categoryId
    );

    // Parse variables
    const variables = fields.variables ? fields.variables.split(',').map(v => v.trim()) : [];

    // Create template
    const template = await documentTemplateService.createTemplate({
      name: fields.name,
      code: fields.code,
      description: fields.description,
      categoryId: new mongoose.Types.ObjectId(fields.categoryId),
      filePath: uploadResult.filePath,
      fileName: uploadResult.fileName,
      fileType: uploadResult.fileType,
      fileSize: uploadResult.fileSize,
      variables,
      isActive: fields.isActive !== 'false',
      createdBy: new mongoose.Types.ObjectId(user.id)
    });

    return NextResponse.json(
      {
        success: true,
        message: 'Document template created successfully',
        data: template
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error creating document template', LogCategory.API, error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

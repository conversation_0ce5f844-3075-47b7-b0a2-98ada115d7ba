import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import * as XLSX from 'xlsx';
import type {
  ConversionApiResponse,
  ConversionResult,
  ConversionOptions,
  ConversionError,
  ConversionWarning
} from '@/types/document-conversion';
import { EmployeeConversionService } from '@/lib/backend/services/document-conversion/employee-conversion-service';

export const runtime = 'nodejs';

// Import conversion services
export async function POST(request: NextRequest): Promise<NextResponse<ConversionApiResponse>> {
  const startTime = Date.now();
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const documentType = formData.get('documentType') as string;
    const optionsString = formData.get('options') as string;
    if (!file || !documentType) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields: file and documentType'
        },
        { status: 400 }
      );
    }
    let options: ConversionOptions = {
      skipValidation: false,
      includeHeaders: true,
      dateFormat: 'auto',
      encoding: 'utf-8',
      delimiter: ',',
      startRow: 1
    };
    if (optionsString) {
      try {
        options = { ...options, ...JSON.parse(optionsString) };
      } catch (error) {
        console.error('Error parsing options:', error);
      }
    }
    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      return NextResponse.json(
        {
          success: false,
          error: 'File size exceeds 10MB limit'
        },
        { status: 400 }
      );
    }
    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'public', 'converted-documents');
    if (!existsSync(uploadsDir)) {
      await mkdir(uploadsDir, { recursive: true });
    }
    // Read file content
    const buffer = Buffer.from(await file.arrayBuffer());
    let workbook: XLSX.WorkBook;
    try {
      if (file.name.endsWith('.csv')) {
        const csvContent = buffer.toString(options.encoding || 'utf-8');
        workbook = XLSX.read(csvContent, {
          type: 'string',
          delimiter: options.delimiter,
          raw: false
        });
      } else {
        workbook = XLSX.read(buffer, { type: 'buffer' });
      }
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to read file. Please ensure it\'s a valid Excel or CSV file.'
        },
        { status: 400 }
      );
    }
    // Get the worksheet
    const sheetName = options.sheetName || workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    if (!worksheet) {
      return NextResponse.json(
        {
          success: false,
          error: `Sheet "${sheetName}" not found in the file`
        },
        { status: 400 }
      );
    }
    // Convert worksheet to JSON with proper header handling
    let rawData: Record<string, unknown>[];
    // Always try to detect headers first
    const firstRow = XLSX.utils.sheet_to_json(worksheet, {
      header: 1,
      range: 0,
      raw: false
    })[0] as string[];
    console.log('First row detected:', firstRow);
    // Check if first row looks like headers (contains text, not just numbers)
    const looksLikeHeaders = firstRow && firstRow.some(cell =>
      typeof cell === 'string' &&
      cell.trim().length > 0 &&
      isNaN(Number(cell)) &&
      /[a-zA-Z]/.test(cell)
    );
    console.log('First row looks like headers:', looksLikeHeaders);
    if (looksLikeHeaders && options.includeHeaders !== false) {
      // Use first row as headers
      console.log('Using first row as headers:', firstRow);
      rawData = XLSX.utils.sheet_to_json(worksheet, {
        header: firstRow.map(h => String(h).trim()),
        range: 1, // Skip header row
        raw: false,
        defval: ''
      });
    } else {
      // No headers detected or explicitly disabled
      console.log('No headers detected, using numeric headers');
      rawData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        range: options.startRow ? options.startRow - 1 : 0,
        raw: false,
        defval: ''
      });
    }
    if (rawData.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'No data found in the file'
        },
        { status: 400 }
      );
    }
    // Process data based on document type
    let conversionResult: {
      convertedData: Record<string, unknown>[];
      errors: ConversionError[];
      warnings: ConversionWarning[];
    };
    switch (documentType) {
      case 'employee-excel':
        conversionResult = await EmployeeConversionService.convertData(rawData, options);
        break;
      default:
        return NextResponse.json(
          {
            success: false,
            error: `Document type "${documentType}" is not yet implemented. Currently only employee-excel is supported.`
          },
          { status: 400 }
        );
    }
    // Generate output filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const originalName = file.name.replace(/\.[^/.]+$/, '');
    const convertedFileName = `${originalName}_converted_${timestamp}.xlsx`;
    const outputPath = path.join(uploadsDir, convertedFileName);
    // Create new workbook with converted data
    const newWorkbook = XLSX.utils.book_new();
    const newWorksheet = XLSX.utils.json_to_sheet(conversionResult.convertedData);
    XLSX.utils.book_append_sheet(newWorkbook, newWorksheet, 'Converted Data');
    // Write the converted file
    const outputBuffer = XLSX.write(newWorkbook, { type: 'buffer', bookType: 'xlsx' });
    await writeFile(outputPath, outputBuffer);
    const processingTime = Date.now() - startTime;
    // Determine if conversion was actually successful
    const hasErrors = conversionResult.errors.length > 0;
    const hasConvertedData = conversionResult.convertedData.length > 0;
    const conversionSuccess = hasConvertedData && !hasErrors;
    // Prepare result
    const result: ConversionResult = {
      success: conversionSuccess,
      originalFileName: file.name,
      convertedFileName,
      downloadUrl: conversionSuccess ? `/converted-documents/${convertedFileName}` : undefined,
      totalRows: rawData.length,
      convertedRows: conversionResult.convertedData.length,
      errors: conversionResult.errors,
      warnings: conversionResult.warnings,
      previewData: conversionResult.convertedData.slice(0, 5),
      metadata: {
        processedAt: new Date().toISOString(),
        processingTime,
        fileSize: file.size,
        originalFormat: file.name.split('.').pop()?.toUpperCase() || 'UNKNOWN',
        targetFormat: 'XLSX',
        conversionRules: [] // This would be populated by the conversion service
      }
    };
    // Return appropriate status based on conversion success
    const statusCode = conversionSuccess ? 200 : 400;
    return NextResponse.json({
      success: conversionSuccess,
      data: result
    }, { status: statusCode });
  } catch (error) {
    console.error('Conversion error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred during conversion'
      },
      { status: 500 }
    );
  }
}
export async function GET(request: NextRequest): Promise<NextResponse> {
  return NextResponse.json(
    {
      success: false,
      error: 'Method not allowed. Use POST to convert documents.'
    },
    { status: 405 }
  );
}
import { NextRequest, NextResponse } from 'next/server';
import type { DocumentType, DocumentTypeApiResponse } from '@/types/document-conversion';

// Define available document types for conversion
const documentTypes: DocumentType[] = [
  {
    id: 'employee-excel',
    name: 'Employee Data',
    description: 'Convert employee records from existing Excel/CSV format to system-compatible format',
    icon: 'Users',
    category: 'hr',
    supportedFormats: [
      { extension: 'xlsx', mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', description: 'Excel Workbook' },
      { extension: 'xls', mimeType: 'application/vnd.ms-excel', description: 'Excel 97-2003' },
      { extension: 'csv', mimeType: 'text/csv', description: 'Comma Separated Values' }
    ],
    requiredFields: [
      'firstName',
      'lastName', 
      'email',
      'position',
      'employmentType',
      'employmentStatus',
      'hireDate'
    ],
    optionalFields: [
      'phone',
      'dateOfBirth',
      'gender',
      'maritalStatus',
      'numberOfChildren',
      'department',
      'salary',
      'address',
      'city',
      'state',
      'country',
      'village',
      'traditionalAuthority',
      'district',
      'nationalId',
      'nextOfKinName',
      'nextOfKinRelationship',
      'nextOfKinPhone',
      'nextOfKinAddress',
      'emergencyContactName',
      'emergencyContactPhone',
      'emergencyContactRelationship',
      'notes'
    ]
  },
  {
    id: 'department-excel',
    name: 'Department Data',
    description: 'Convert department information from existing format to system-compatible format',
    icon: 'Building2',
    category: 'hr',
    supportedFormats: [
      { extension: 'xlsx', mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', description: 'Excel Workbook' },
      { extension: 'xls', mimeType: 'application/vnd.ms-excel', description: 'Excel 97-2003' },
      { extension: 'csv', mimeType: 'text/csv', description: 'Comma Separated Values' }
    ],
    requiredFields: [
      'name',
      'description'
    ],
    optionalFields: [
      'head',
      'budget',
      'location',
      'isActive'
    ]
  },
  {
    id: 'budget-excel',
    name: 'Budget Data',
    description: 'Convert budget records from existing format to system-compatible format',
    icon: 'Calculator',
    category: 'finance',
    supportedFormats: [
      { extension: 'xlsx', mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', description: 'Excel Workbook' },
      { extension: 'xls', mimeType: 'application/vnd.ms-excel', description: 'Excel 97-2003' },
      { extension: 'csv', mimeType: 'text/csv', description: 'Comma Separated Values' }
    ],
    requiredFields: [
      'name',
      'category',
      'amount',
      'startDate',
      'endDate',
      'status',
      'fiscalYear'
    ],
    optionalFields: [
      'description',
      'department'
    ]
  },
  {
    id: 'salary-excel',
    name: 'Salary Data',
    description: 'Convert salary and payroll information from existing format to system-compatible format',
    icon: 'Wallet',
    category: 'payroll',
    supportedFormats: [
      { extension: 'xlsx', mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', description: 'Excel Workbook' },
      { extension: 'xls', mimeType: 'application/vnd.ms-excel', description: 'Excel 97-2003' },
      { extension: 'csv', mimeType: 'text/csv', description: 'Comma Separated Values' }
    ],
    requiredFields: [
      'employeeId',
      'basicSalary',
      'currency',
      'effectiveDate',
      'paymentMethod'
    ],
    optionalFields: [
      'endDate',
      'allowances',
      'deductions',
      'bankName',
      'bankAccountNumber'
    ]
  },
  {
    id: 'vendor-excel',
    name: 'Vendor Data',
    description: 'Convert vendor/supplier information from existing format to system-compatible format',
    icon: 'ShoppingCart',
    category: 'procurement',
    supportedFormats: [
      { extension: 'xlsx', mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', description: 'Excel Workbook' },
      { extension: 'xls', mimeType: 'application/vnd.ms-excel', description: 'Excel 97-2003' },
      { extension: 'csv', mimeType: 'text/csv', description: 'Comma Separated Values' }
    ],
    requiredFields: [
      'name',
      'email',
      'phone',
      'category'
    ],
    optionalFields: [
      'address',
      'city',
      'country',
      'taxNumber',
      'bankDetails',
      'contactPerson',
      'notes'
    ]
  },
  {
    id: 'inventory-excel',
    name: 'Inventory Data',
    description: 'Convert inventory/stock information from existing format to system-compatible format',
    icon: 'Package',
    category: 'inventory',
    supportedFormats: [
      { extension: 'xlsx', mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', description: 'Excel Workbook' },
      { extension: 'xls', mimeType: 'application/vnd.ms-excel', description: 'Excel 97-2003' },
      { extension: 'csv', mimeType: 'text/csv', description: 'Comma Separated Values' }
    ],
    requiredFields: [
      'itemName',
      'itemCode',
      'category',
      'quantity',
      'unitPrice'
    ],
    optionalFields: [
      'description',
      'supplier',
      'location',
      'minimumStock',
      'maximumStock',
      'reorderLevel',
      'unit'
    ]
  },
  {
    id: 'account-excel',
    name: 'Chart of Accounts',
    description: 'Convert chart of accounts from existing format to system-compatible format',
    icon: 'Calculator',
    category: 'accounting',
    supportedFormats: [
      { extension: 'xlsx', mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', description: 'Excel Workbook' },
      { extension: 'xls', mimeType: 'application/vnd.ms-excel', description: 'Excel 97-2003' },
      { extension: 'csv', mimeType: 'text/csv', description: 'Comma Separated Values' }
    ],
    requiredFields: [
      'accountCode',
      'accountName',
      'accountType',
      'category'
    ],
    optionalFields: [
      'description',
      'parentAccount',
      'isActive',
      'openingBalance'
    ]
  }
];

export async function GET(request: NextRequest): Promise<NextResponse<DocumentTypeApiResponse>> {
  try {
    // In a real application, you might want to filter document types based on user permissions
    // const session = await getServerSession(authOptions);
    // if (!session) {
    //   return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    // }

    return NextResponse.json({
      success: true,
      data: documentTypes
    });
  } catch (error) {
    console.error('Error fetching document types:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch document types' 
      },
      { status: 500 }
    );
  }
}

// Optional: Add POST method to allow dynamic document type registration
export async function POST(request: NextRequest): Promise<NextResponse<DocumentTypeApiResponse>> {
  try {
    const newDocumentType: DocumentType = await request.json();
    
    // Validate the document type structure
    if (!newDocumentType.id || !newDocumentType.name || !newDocumentType.category) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid document type structure' 
        },
        { status: 400 }
      );
    }

    // In a real application, you would save this to a database
    // For now, we'll just return success
    return NextResponse.json({
      success: true,
      data: [...documentTypes, newDocumentType]
    });
  } catch (error) {
    console.error('Error creating document type:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create document type' 
      },
      { status: 500 }
    );
  }
}

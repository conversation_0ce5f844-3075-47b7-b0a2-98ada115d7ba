import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import mongoose from 'mongoose';

/**
 * GET /api/debug/database
 * Test database connection and return status
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only admin roles can access debug endpoints
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    logger.info('Testing database connection', LogCategory.DEBUG, {
      userId: user.id,
      userRole: user.role
    });

    // Test database connection
    await connectToDatabase();

    // Get connection details
    const connection = mongoose.connection;
    const dbName = connection.db?.databaseName || 'Unknown';
    const readyState = connection.readyState;
    const host = connection.host || 'Unknown';
    const port = connection.port || 'Unknown';

    // Map ready state to human readable status
    const statusMap = {
      0: 'Disconnected',
      1: 'Connected',
      2: 'Connecting',
      3: 'Disconnecting'
    };

    const status = statusMap[readyState as keyof typeof statusMap] || 'Unknown';

    // Test a simple query to ensure the connection is working
    const collections = await connection.db?.listCollections().toArray();
    const collectionCount = collections?.length || 0;

    // Get some basic stats
    const stats = await connection.db?.stats();

    const result = {
      success: true,
      database: dbName,
      status: status,
      readyState: readyState,
      host: host,
      port: port,
      collections: collectionCount,
      stats: {
        dataSize: stats?.dataSize || 0,
        storageSize: stats?.storageSize || 0,
        indexes: stats?.indexes || 0,
        objects: stats?.objects || 0
      },
      timestamp: new Date().toISOString()
    };

    logger.info('Database connection test successful', LogCategory.DEBUG, result);

    return NextResponse.json(result);

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown database error';
    
    logger.error('Database connection test failed', LogCategory.DEBUG, {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json({
      success: false,
      error: errorMessage,
      database: 'Connection Failed',
      status: 'Error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

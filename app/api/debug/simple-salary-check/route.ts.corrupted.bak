import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';

export const runtime = 'nodejs';

/**
 * GET /api/debug/simple-salary-check
 * Simple debug endpoint to check isActive values without population
 */
export async function GET(req: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();
    
    console.log('🔍 SIMPLE EMPLOYEE SALARIES isActive CHECK');
    console.log('=' .repeat(80));
    
    // Get all employee salaries WITHOUT population to avoid model issues
    const employeeSalaries = await EmployeeSalary.find({})
      .sort({ effectiveDate: -1 })
      .lean();
    
    console.log(`📊 Total Employee Salary Records Found: ${employeeSalaries.length}`);
    
    // Count active vs inactive
    let activeCount = 0;
    let inactiveCount = 0;
    
    const salaryData: any[] = [];
    
    employeeSalaries.forEach((salary, index) => {
      if (salary.isActive) {
        activeCount++;
      } else {
        inactiveCount++;
      }
      
      // Add to debug data (first 20 records for detailed view)
      if (index < 20) {
        salaryData.push({
          salaryId: salary._id,
          employeeId: salary.employeeId,
          isActive: salary.isActive,
          basicSalary: salary.basicSalary,
          effectiveDate: salary.effectiveDate,
          endDate: salary.endDate,
          currency: salary.currency || 'MWK',
          createdAt: salary.createdAt,
          updatedAt: salary.updatedAt,
          notes: salary.notes
        });
      }
    });
    
    console.log(`✅ Active Salary Records: ${activeCount}`);
    console.log(`❌ Inactive Salary Records: ${inactiveCount}`);
    
    // Group by employeeId to find employees with no active salaries
    const employeeGroups: { [key: string]: any[] } = {};
    
    employeeSalaries.forEach(salary => {
      const empId = salary.employeeId?.toString() || 'unknown';
      if (!employeeGroups[empId]) {
        employeeGroups[empId] = [];
      }
      employeeGroups[empId].push(salary);
    });
    
    // Find employees with no active salaries
    const employeesWithNoActiveSalary: string[] = [];
    const employeesWithMultipleActiveSalaries: string[] = [];
    
    Object.entries(employeeGroups).forEach(([employeeId, salaries]) => {
      const activeSalaries = salaries.filter(s => s.isActive);
      
      if (activeSalaries.length === 0) {
        employeesWithNoActiveSalary.push(employeeId);
      } else if (activeSalaries.length > 1) {
        employeesWithMultipleActiveSalaries.push(employeeId);
      }
    });
    
    // Check for salaries with end dates in the past but still marked as active
    const activeWithPastEndDate = employeeSalaries.filter(salary => {
      return salary.isActive && salary.endDate && new Date(salary.endDate) < new Date();
    });
    
    console.log('\n🔍 ANALYSIS RESULTS:');
    console.log(`Unique Employees: ${Object.keys(employeeGroups).length}`);
    console.log(`Employees with No Active Salary: ${employeesWithNoActiveSalary.length}`);
    console.log(`Employees with Multiple Active Salaries: ${employeesWithMultipleActiveSalaries.length}`);
    console.log(`Active Salaries with Past End Dates: ${activeWithPastEndDate.length}`);
    
    // Log employees with no active salaries
    if (employeesWithNoActiveSalary.length > 0) {
      console.log('\n❌ EMPLOYEE IDs WITH NO ACTIVE SALARIES:');
      employeesWithNoActiveSalary.forEach(employeeId => {
        console.log(`   - Employee ID: ${employeeId}`);
        const salaries = employeeGroups[employeeId];
        const mostRecent = salaries.sort((a, b) => new Date(b.effectiveDate).getTime() - new Date(a.effectiveDate).getTime())[0];
        if (mostRecent) {
          console.log(`     Most Recent Salary: MWK ${mostRecent.basicSalary?.toLocaleString()} (${new Date(mostRecent.effectiveDate).toLocaleDateString()})`);
          console.log(`     Is Active: ${mostRecent.isActive}`);
          if (mostRecent.endDate) {
            console.log(`     End Date: ${new Date(mostRecent.endDate).toLocaleDateString()}`);
          }
        }
      });
    }
    
    // Log employees with multiple active salaries
    if (employeesWithMultipleActiveSalaries.length > 0) {
      console.log('\n⚠️ EMPLOYEE IDs WITH MULTIPLE ACTIVE SALARIES:');
      employeesWithMultipleActiveSalaries.forEach(employeeId => {
        const activeSalaries = employeeGroups[employeeId].filter(s => s.isActive);
        console.log(`   - Employee ID: ${employeeId} (${activeSalaries.length} active salaries)`);
      });
    }
    
    const summary = {
      totalRecords: employeeSalaries.length,
      activeRecords: activeCount,
      inactiveRecords: inactiveCount,
      activePercentage: ((activeCount / employeeSalaries.length) * 100).toFixed(1),
      inactivePercentage: ((inactiveCount / employeeSalaries.length) * 100).toFixed(1),
      uniqueEmployees: Object.keys(employeeGroups).length,
      employeesWithNoActiveSalary: employeesWithNoActiveSalary.length,
      employeesWithMultipleActiveSalaries: employeesWithMultipleActiveSalaries.length,
      activeSalariesWithPastEndDate: activeWithPastEndDate.length,
      employeesWithNoActiveSalaryIds: employeesWithNoActiveSalary,
      employeesWithMultipleActiveSalariesIds: employeesWithMultipleActiveSalaries,
      sampleSalaryData: salaryData,
      issueFound: employeesWithNoActiveSalary.length > 0,
      recommendation: employeesWithNoActiveSalary.length > 0 
        ? "Use the Employee Salaries UI to reactivate salaries for employees with no active salary records"
        : "All employees have active salary records"
    };
    
    console.log('\n🎯 SUMMARY:');
    console.log(`Total Records: ${summary.totalRecords}`);
    console.log(`Active Records: ${summary.activeRecords} (${summary.activePercentage}%)`);
    console.log(`Inactive Records: ${summary.inactiveRecords} (${summary.inactivePercentage}%)`);
    console.log(`Issue Found: ${summary.issueFound}`);
    
    if (summary.issueFound) {
      console.log('\n🚨 PAYROLL CALCULATION ISSUE IDENTIFIED:');
      console.log(`${summary.employeesWithNoActiveSalary} employees have NO active salary records.`);
      console.log('This explains why payroll calculations fail with "No active salary found" errors.');
      console.log('\n💡 SOLUTION:');
      console.log('Navigate to: http://localhost:3000/dashboard/payroll/employee-salaries');
      console.log('Look for red X icons and click them to reactivate salaries.');
    }
    
    return NextResponse.json({
      success: true,
      message: 'Simple employee salaries isActive check completed',
      data: summary
    });
    
  } catch (error: unknown) {
    console.error('❌ Simple debug failed:', error);
    return NextResponse.json(
      { 
        error: 'Failed to debug employee salaries', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

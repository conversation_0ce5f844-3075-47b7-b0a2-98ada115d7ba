import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { seedSampleLeaveData, clearSampleLeaveData } from '@/lib/backend/seeders/sample-leave-seeder';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

/**
 * POST /api/debug/seed-sample-leaves
 * Seed sample leave data for testing reports
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only super admin and system admin can seed data
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Only system administrators can seed sample data' },
        { status: 403 }
      );
    }

    logger.info('Starting sample leave data seeding', LogCategory.SYSTEM, {
      userId: user.id,
      userRole: user.role
    });

    // Seed sample leave data
    const result = await seedSampleLeaveData();

    logger.info('Sample leave data seeding completed', LogCategory.SYSTEM, result);

    return NextResponse.json({
      success: true,
      message: 'Sample leave data seeded successfully',
      data: result
    });

  } catch (error) {
    logger.error('Error seeding sample leave data', LogCategory.SYSTEM, error);
    return NextResponse.json(
      { 
        error: 'Failed to seed sample leave data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/debug/seed-sample-leaves
 * Clear sample leave data
 */
export async function DELETE(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only super admin and system admin can clear data
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Only system administrators can clear sample data' },
        { status: 403 }
      );
    }

    logger.info('Starting sample leave data clearing', LogCategory.SYSTEM, {
      userId: user.id,
      userRole: user.role
    });

    // Clear sample leave data
    const deletedCount = await clearSampleLeaveData();

    logger.info('Sample leave data clearing completed', LogCategory.SYSTEM, {
      deletedCount
    });

    return NextResponse.json({
      success: true,
      message: 'Sample leave data cleared successfully',
      data: { deletedCount }
    });

  } catch (error) {
    logger.error('Error clearing sample leave data', LogCategory.SYSTEM, error);
    return NextResponse.json(
      { 
        error: 'Failed to clear sample leave data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

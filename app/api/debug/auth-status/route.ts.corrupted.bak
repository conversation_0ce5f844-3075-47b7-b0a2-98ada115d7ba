import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';

export const runtime = 'nodejs';

/**
 * GET /api/debug/auth-status
 * Debug endpoint to check authentication status
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 AUTH STATUS DEBUG');
    console.log('=' .repeat(50));
    
    // Check if we have a token cookie
    const token = request.cookies.get('token')?.value;
    console.log(`🍪 Token cookie present: ${!!token}`);
    if (token) {
      console.log(`🍪 Token length: ${token.length}`);
      console.log(`🍪 Token preview: ${token.substring(0, 20)}...`);
    }
    
    // Try to get current user
    console.log('👤 Attempting to get current user...');
    const user = await getCurrentUser(request);
    
    if (user) {
      console.log('✅ User authenticated successfully');
      console.log(`👤 User ID: ${user._id}`);
      console.log(`📧 Email: ${user.email}`);
      console.log(`🎭 Role: ${user.role}`);
      console.log(`📊 Status: ${user.status}`);
    } else {
      console.log('❌ User not authenticated');
    }
    
    // Check database connection
    try {
      await connectToDatabase();
      console.log('✅ Database connected successfully');
    } catch (dbError) {
      console.log('❌ Database connection failed:', dbError);
    }
    
    const authStatus = {
      hasToken: !!token,
      tokenLength: token?.length || 0,
      isAuthenticated: !!user,
      user: user ? {
        id: user._id,
        email: user.email,
        role: user.role,
        status: user.status,
        firstName: user.firstName,
        lastName: user.lastName
      } : null,
      timestamp: new Date().toISOString(),
      requestPath: request.nextUrl.pathname,
      userAgent: request.headers.get('user-agent'),
      cookies: Object.fromEntries(
        Array.from(request.cookies.entries()).map(([name, cookie]) => [
          name, 
          name === 'token' ? `${cookie.value.substring(0, 10)}...` : cookie.value
        ])
      )
    };
    
    return NextResponse.json({
      success: true,
      message: 'Authentication status check completed',
      data: authStatus
    });
    
  } catch (error: unknown) {
    console.error('❌ Auth status check failed:', error);
    return NextResponse.json(
      { 
        error: 'Failed to check authentication status', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

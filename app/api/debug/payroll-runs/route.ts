import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import PayrollRun from '@/models/payroll/PayrollRun';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

// app/api/debug/payroll-runs/route.ts
/**
 * GET /api/debug/payroll-runs
 * Debug endpoint to check payroll runs in database
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    logger.info('Debug: Checking payroll runs in database', LogCategory.API);
    await connectToDatabase();
    // Get all payroll runs
    const allRuns = await PayrollRun.find({})
      .select('name status payPeriod totalEmployees createdAt')
      .sort({ createdAt: -1 })
      .limit(20)
      .lean();
    // Get counts by status
    const statusCounts = await PayrollRun.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);
    // Get runs with specific statuses
    const targetStatuses = ['approved', 'completed', 'paid'];
    const targetRuns = await PayrollRun.find({
      status: { $in: targetStatuses }
    })
      .select('name status payPeriod totalEmployees createdAt')
      .sort({ createdAt: -1 })
      .limit(10)
      .lean();
    logger.info('Debug: Payroll runs retrieved', LogCategory.API, {
      totalRuns: allRuns.length,
      targetRuns: targetRuns.length,
      statusCounts
    });
    return NextResponse.json({
      success: true,
      data: {
        totalRuns: allRuns.length,
        allRuns: allRuns.map(run => ({
          id: run._id.toString(),
          name: run.name,
          status: run.status,
          payPeriod: `${run.payPeriod.month}/${run.payPeriod.year}`,
          totalEmployees: run.totalEmployees,
          createdAt: run.createdAt
        })),
        statusCounts,
        targetStatuses,
        targetRuns: targetRuns.map(run => ({
          id: run._id.toString(),
          name: run.name,
          status: run.status,
          payPeriod: `${run.payPeriod.month}/${run.payPeriod.year}`,
          totalEmployees: run.totalEmployees,
          createdAt: run.createdAt
        }))
      }
    });
  } catch (error: unknown) {
    logger.error('Debug: Error checking payroll runs', LogCategory.API, error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Failed to check payroll runs'
    }, { status: 500 });
  }
}
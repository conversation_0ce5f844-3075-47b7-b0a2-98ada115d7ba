import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

/**
 * GET /api/debug/health
 * Comprehensive health check for all system components
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }
    const healthChecks = {
      timestamp: new Date().toISOString(),
      overall: 'healthy',
      checks: {} as Record<string, any>
    };
    // 1. Database Health Check
    try {
      await connectToDatabase();
      const connection = mongoose.connection;
      const dbName = connection.db?.databaseName || 'Unknown';
      const readyState = connection.readyState;
      healthChecks.checks.database = {
        status: readyState === 1 ? 'healthy' : 'unhealthy',
        name: dbName,
        readyState: readyState,
        message: readyState === 1 ? 'Connected' : 'Not connected'
      };
    } catch (error) {
      healthChecks.checks.database = {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Database connection failed'
      };
      healthChecks.overall = 'unhealthy';
    }
    // 2. Authentication Health Check
    try {
      healthChecks.checks.authentication = {
        status: 'healthy',
        user: {
          id: user.id,
          email: user.email,
          role: user.role
        },
        message: 'User authenticated successfully'
      };
    } catch (error) {
      healthChecks.checks.authentication = {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Authentication failed'
      };
      healthChecks.overall = 'unhealthy';
    }
    // 3. API Endpoints Health Check
    const apiChecks = [];
    const endpoints = [
      '/api/leave/types',
      '/api/leave/requests',
      '/api/employees'
    ];
    for (const endpoint of endpoints) {
      try {
        const baseUrl = req.nextUrl.origin;
        const response = await fetch(`${baseUrl}${endpoint}`, {
          headers: {
            'Cookie': req.headers.get('cookie') || ''
          }
        });
        apiChecks.push({
          endpoint,
          status: response.ok ? 'healthy' : 'unhealthy',
          statusCode: response.status,
          message: response.ok ? 'OK' : `HTTP ${response.status}`
        });
      } catch (error) {
        apiChecks.push({
          endpoint,
          status: 'unhealthy',
          error: error instanceof Error ? error.message : 'Request failed'
        });
        healthChecks.overall = 'unhealthy';
      }
    }
    healthChecks.checks.apiEndpoints = {
      status: apiChecks.every(check => check.status === 'healthy') ? 'healthy' : 'unhealthy',
      endpoints: apiChecks
    };
    // 4. Environment Health Check
    healthChecks.checks.environment = {
      status: 'healthy',
      nodeVersion: process.version,
      platform: process.platform,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      env: process.env.NODE_ENV || 'development'
    };
    logger.info('Health check completed', LogCategory.DEBUG, {
      overall: healthChecks.overall,
      userId: user.id
    });
    return NextResponse.json(healthChecks);
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Health check failed';
    logger.error('Health check failed', LogCategory.DEBUG, {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined
    });
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      overall: 'unhealthy',
      error: errorMessage,
      checks: {
        system: {
          status: 'unhealthy',
          error: errorMessage
        }
      }
    }, { status: 500 });
  }
}
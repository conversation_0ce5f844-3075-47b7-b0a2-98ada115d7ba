// app/api/debug/payroll-check/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { Employee, IEmployee } from '@/models/Employee';
import EmployeeSalary, { IEmployeeSalary } from '@/models/payroll/EmployeeSalary';
// import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * Debug endpoint to check if employees and salary structures exist
 */
export async function GET(req: NextRequest) {
  try {
    // Get current user
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Check if Employee model exists
    const employeeModelExists = !!Employee;

    // Check if EmployeeSalary model exists
    const employeeSalaryModelExists = !!EmployeeSalary;

    // Count active employees
    let activeEmployeeCount = 0;
    let employeeSample: Partial<IEmployee>[] = [];

    try {
      activeEmployeeCount = await Employee.countDocuments({ employmentStatus: 'active' });
      employeeSample = await Employee.find({ employmentStatus: 'active' })
        .select('_id firstName lastName email employmentStatus')
        .limit(5)
        .lean();
    } catch (employeeError) {
      logger.error('Error counting employees', LogCategory.API, employeeError);
    }

    // Count active salary structures
    let activeSalaryCount = 0;
    let salarySample: Partial<IEmployeeSalary>[] = [];

    try {
      activeSalaryCount = await EmployeeSalary.countDocuments({ isActive: true });
      salarySample = await EmployeeSalary.find({ isActive: true })
        .select('_id employeeId basicSalary currency')
        .limit(5)
        .lean();
    } catch (salaryError) {
      logger.error('Error counting salary structures', LogCategory.API, salaryError);
    }

    // Return the results
    return NextResponse.json({
      success: true,
      data: {
        models: {
          employeeModelExists,
          employeeSalaryModelExists
        },
        counts: {
          activeEmployeeCount,
          activeSalaryCount
        },
        samples: {
          employeeSample,
          salarySample
        }
      }
    });
  } catch (error: unknown) {
    logger.error('Error in payroll check', LogCategory.API, error);
    return NextResponse.json(
      {
        error: 'Failed to check payroll data',
        details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

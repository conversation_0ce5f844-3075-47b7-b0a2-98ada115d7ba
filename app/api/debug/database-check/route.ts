import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

/**
 * GET /api/debug/database-check
 * Basic database connection and collection check
 */
export async function GET(req: NextRequest) {
  try {
    console.log('🔍 BASIC DATABASE CONNECTION CHECK');
    console.log('=' .repeat(80));
    // Connect to database
    await connectToDatabase();
    console.log('✅ Database connected successfully');
    console.log(`📊 Connection state: ${mongoose.connection.readyState}`);
    console.log(`🗄️ Database name: ${mongoose.connection.db?.databaseName}`);
    // List all collections
    const collections = await mongoose.connection.db?.listCollections().toArray();
    console.log(`📁 Total collections: ${collections?.length || 0}`);
    if (collections) {
      console.log('\n📋 Available collections:');
      collections.forEach((collection, index) => {
        console.log(`   ${index + 1}. ${collection.name}`);
      });
    }
    // Check for employee salary related collections
    const salaryCollections = collections?.filter(col => 
      col.name.toLowerCase().includes('salary') || 
      col.name.toLowerCase().includes('employee')
    ) || [];
    console.log(`\n💰 Salary-related collections: ${salaryCollections.length}`);
    salaryCollections.forEach(col => {
      console.log(`   - ${col.name}`);
    });
    // Try to get document counts for salary-related collections
    const collectionStats: any = {};
    for (const collection of salaryCollections) {
      try {
        const count = await mongoose.connection.db?.collection(collection.name).countDocuments();
        collectionStats[collection.name] = count;
        console.log(`   📊 ${collection.name}: ${count} documents`);
      } catch (error) {
        console.log(`   ❌ Error counting ${collection.name}: ${error}`);
        collectionStats[collection.name] = 'error';
      }
    }
    // Check mongoose models
    const registeredModels = Object.keys(mongoose.models);
    console.log(`\n🏗️ Registered Mongoose models: ${registeredModels.length}`);
    registeredModels.forEach((model, index) => {
      console.log(`   ${index + 1}. ${model}`);
    });
    // Try direct collection query
    let directQueryResult = null;
    try {
      const employeeSalariesCollection = mongoose.connection.db?.collection('employeesalaries');
      const directCount = await employeeSalariesCollection?.countDocuments();
      console.log(`\n🔍 Direct query on 'employeesalaries' collection: ${directCount} documents`);
      directQueryResult = directCount;
      // Get a sample document
      if (directCount && directCount > 0) {
        const sampleDoc = await employeeSalariesCollection?.findOne();
        console.log('📄 Sample document structure:');
        console.log(JSON.stringify(sampleDoc, null, 2));
      }
    } catch (error) {
      console.log(`❌ Direct query failed: ${error}`);
    }
    const summary = {
      databaseConnected: mongoose.connection.readyState === 1,
      databaseName: mongoose.connection.db?.databaseName,
      totalCollections: collections?.length || 0,
      salaryRelatedCollections: salaryCollections.length,
      collectionNames: collections?.map(c => c.name) || [],
      salaryCollectionNames: salaryCollections.map(c => c.name),
      collectionStats,
      registeredModels,
      directQueryResult,
      connectionState: mongoose.connection.readyState,
      connectionStates: {
        0: 'disconnected',
        1: 'connected',
        2: 'connecting',
        3: 'disconnecting'
      }
    };
    return NextResponse.json({
      success: true,
      message: 'Database connection check completed',
      data: summary
    });
  } catch (error: unknown) {
    console.error('❌ Database check failed:', error);
    return NextResponse.json(
      { 
        error: 'Failed to check database connection', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
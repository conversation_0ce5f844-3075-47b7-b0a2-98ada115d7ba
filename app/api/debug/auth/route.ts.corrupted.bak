import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';

/**
 * GET /api/debug/auth
 * Test authentication and return user information
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ 
        success: false,
        error: 'No user session found',
        authenticated: false 
      }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions for debug access',
        authenticated: true,
        user: {
          id: user.id,
          email: user.email,
          role: user.role
        },
        hasDebugAccess: false
      }, { status: 403 });
    }

    logger.info('Authentication debug check successful', LogCategory.DEBUG, {
      userId: user.id,
      userRole: user.role
    });

    const result = {
      success: true,
      authenticated: true,
      hasDebugAccess: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        firstName: user.firstName || 'N/A',
        lastName: user.lastName || 'N/A'
      },
      session: {
        timestamp: new Date().toISOString(),
        userAgent: req.headers.get('user-agent') || 'Unknown',
        ip: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'Unknown'
      },
      permissions: {
        canAccessDebug: true,
        canSeedData: hasRequiredPermissions(user, [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN]),
        canManageLeave: hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.HR_DIRECTOR,
          UserRole.HR_MANAGER,
          UserRole.HR_SPECIALIST
        ])
      }
    };

    return NextResponse.json(result);

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Authentication check failed';
    
    logger.error('Authentication debug check failed', LogCategory.DEBUG, {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json({
      success: false,
      authenticated: false,
      error: errorMessage,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

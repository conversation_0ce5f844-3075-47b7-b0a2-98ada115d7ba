import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';
import { Employee } from '@/models/Employee';

export const runtime = 'nodejs';

/**
 * GET /api/debug/employee-salaries-isactive
 * Debug endpoint to check isActive values in employee salaries
 */
export async function GET(req: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();
    console.log('🔍 DEBUGGING EMPLOYEE SALARIES isActive VALUES');
    console.log('=' .repeat(80));
    // Get all employee salaries with populated employee data
    const employeeSalaries = await EmployeeSalary.find({})
      .populate('employeeId', 'firstName lastName employeeId employeeNumber')
      .sort({ 'employeeId.lastName': 1 })
      .lean();
    console.log(`📊 Total Employee Salary Records Found: ${employeeSalaries.length}`);
    // Count active vs inactive
    let activeCount = 0;
    let inactiveCount = 0;
    // Group by employee to see multiple salary records
    const employeeGroups: any = {};
    const debugData: any[] = [];
    employeeSalaries.forEach(salary => {
      const employeeId = salary.employeeId?._id?.toString() || 'unknown';
      const employeeName = salary.employeeId 
        ? `${salary.employeeId.firstName || ''} ${salary.employeeId.lastName || ''}`.trim()
        : 'Unknown Employee';
      if (!employeeGroups[employeeId]) {
        employeeGroups[employeeId] = {
          name: employeeName,
          employeeNumber: salary.employeeId?.employeeNumber || salary.employeeId?.employeeId || 'N/A',
          salaries: []
        };
      }
      employeeGroups[employeeId].salaries.push(salary);
      if (salary.isActive) {
        activeCount++;
      } else {
        inactiveCount++;
      }
      // Add to debug data
      debugData.push({
        salaryId: salary._id,
        employeeName,
        employeeNumber: salary.employeeId?.employeeNumber || salary.employeeId?.employeeId || 'N/A',
        isActive: salary.isActive,
        basicSalary: salary.basicSalary,
        effectiveDate: salary.effectiveDate,
        endDate: salary.endDate,
        currency: salary.currency || 'MWK',
        createdAt: salary.createdAt,
        updatedAt: salary.updatedAt,
        notes: salary.notes
      });
    });
    console.log(`✅ Active Salary Records: ${activeCount}`);
    console.log(`❌ Inactive Salary Records: ${inactiveCount}`);
    console.log(`👥 Unique Employees: ${Object.keys(employeeGroups).length}`);
    // Find employees with no active salaries
    const noActiveSalaries = Object.entries(employeeGroups).filter(([_, group]: [string, any]) => {
      const activeSalaries = group.salaries.filter((s: any) => s.isActive);
      return activeSalaries.length === 0;
    });
    // Find employees with multiple active salaries
    const multipleActiveSalaries = Object.entries(employeeGroups).filter(([_, group]: [string, any]) => {
      const activeSalaries = group.salaries.filter((s: any) => s.isActive);
      return activeSalaries.length > 1;
    });
    // Check for salaries with end dates in the past but still marked as active
    const activeWithPastEndDate = employeeSalaries.filter(salary => {
      return salary.isActive && salary.endDate && new Date(salary.endDate) < new Date();
    });
    console.log('\n🔍 ANALYSIS RESULTS:');
    console.log(`Employees with No Active Salary: ${noActiveSalaries.length}`);
    console.log(`Employees with Multiple Active Salaries: ${multipleActiveSalaries.length}`);
    console.log(`Active Salaries with Past End Dates: ${activeWithPastEndDate.length}`);
    // Log employees with no active salaries
    if (noActiveSalaries.length > 0) {
      console.log('\n❌ EMPLOYEES WITH NO ACTIVE SALARIES:');
      noActiveSalaries.forEach(([employeeId, group]: [string, any]) => {
        console.log(`   - ${group.name} (${group.employeeNumber})`);
        const mostRecent = group.salaries.sort((a: any, b: any) => new Date(b.effectiveDate).getTime() - new Date(a.effectiveDate).getTime())[0];
        if (mostRecent) {
          console.log(`     Most Recent: MWK ${mostRecent.basicSalary?.toLocaleString()} (${new Date(mostRecent.effectiveDate).toLocaleDateString()})`);
          if (mostRecent.endDate) {
            console.log(`     End Date: ${new Date(mostRecent.endDate).toLocaleDateString()}`);
          }
        }
      });
    }
    const summary = {
      totalRecords: employeeSalaries.length,
      activeRecords: activeCount,
      inactiveRecords: inactiveCount,
      uniqueEmployees: Object.keys(employeeGroups).length,
      employeesWithNoActiveSalary: noActiveSalaries.length,
      employeesWithMultipleActiveSalaries: multipleActiveSalaries.length,
      activeSalariesWithPastEndDate: activeWithPastEndDate.length,
      employeesWithNoActiveSalaryList: noActiveSalaries.map(([_, group]: [string, any]) => ({
        name: group.name,
        employeeNumber: group.employeeNumber,
        salaryCount: group.salaries.length
      })),
      debugData: debugData.slice(0, 50) // Limit to first 50 for response size
    };
    console.log('\n🎯 SUMMARY:');
    console.log(`Total Records: ${summary.totalRecords}`);
    console.log(`Active Records: ${summary.activeRecords} (${((summary.activeRecords / summary.totalRecords) * 100).toFixed(1)}%)`);
    console.log(`Inactive Records: ${summary.inactiveRecords} (${((summary.inactiveRecords / summary.totalRecords) * 100).toFixed(1)}%)`);
    return NextResponse.json({
      success: true,
      message: 'Employee salaries isActive debug completed',
      data: summary
    });
  } catch (error: unknown) {
    console.error('❌ Debug failed:', error);
    return NextResponse.json(
      { 
        error: 'Failed to debug employee salaries', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
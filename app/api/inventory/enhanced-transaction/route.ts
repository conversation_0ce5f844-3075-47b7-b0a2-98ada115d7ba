import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { enhancedInventoryTransactionService } from '@/services/inventory/EnhancedInventoryTransactionService';

export const runtime = 'nodejs';

// Custom auth system doesn't require authOptions;
/**
 * POST /api/inventory/enhanced-transaction
 * Create a new inventory transaction with accounting integration
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.INVENTORY_MANAGER,
      UserRole.INVENTORY_CLERK,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get request body
    const body = await req.json();
    // Determine transaction type and call appropriate method
    switch (body.transactionType) {
      case 'purchase':
        // Validate required fields
        if (!body.stockId || !body.stockName || body.quantity === undefined || body.unitCost === undefined) {
          return NextResponse.json(
            { error: 'Missing required fields for purchase transaction' },
            { status: 400 }
          );
        }
        // Record stock purchase
        const purchaseTransaction = await enhancedInventoryTransactionService.recordStockPurchase(
          body.stockId,
          body.stockName,
          body.quantity,
          body.unitCost,
          body.purchaseOrderId,
          body.purchaseOrderNumber,
          body.location,
          body.notes,
          user.id
        );
        return NextResponse.json({
          success: true,
          message: 'Stock purchase recorded successfully',
          data: purchaseTransaction
        });
      case 'sale':
        // Validate required fields
        if (!body.stockId || !body.stockName || body.quantity === undefined || body.unitCost === undefined) {
          return NextResponse.json(
            { error: 'Missing required fields for sale transaction' },
            { status: 400 }
          );
        }
        // Record stock sale
        const saleTransaction = await enhancedInventoryTransactionService.recordStockSale(
          body.stockId,
          body.stockName,
          body.quantity,
          body.unitCost,
          body.salesOrderId,
          body.salesOrderNumber,
          body.location,
          body.notes,
          user.id
        );
        return NextResponse.json({
          success: true,
          message: 'Stock sale recorded successfully',
          data: saleTransaction
        });
      case 'adjustment':
        // Validate required fields
        if (!body.stockId || !body.stockName || body.previousQuantity === undefined ||
            body.newQuantity === undefined || body.unitCost === undefined || !body.reason) {
          return NextResponse.json(
            { error: 'Missing required fields for adjustment transaction' },
            { status: 400 }
          );
        }
        // Record stock adjustment
        const adjustmentTransaction = await enhancedInventoryTransactionService.recordStockAdjustment(
          body.stockId,
          body.stockName,
          body.previousQuantity,
          body.newQuantity,
          body.unitCost,
          body.reason,
          body.notes,
          user.id
        );
        return NextResponse.json({
          success: true,
          message: 'Stock adjustment recorded successfully',
          data: adjustmentTransaction
        });
      case 'transfer':
        // Validate required fields
        if (!body.stockId || !body.stockName || body.quantity === undefined ||
            !body.fromLocation || !body.toLocation) {
          return NextResponse.json(
            { error: 'Missing required fields for transfer transaction' },
            { status: 400 }
          );
        }
        // Record stock transfer
        const transferTransaction = await enhancedInventoryTransactionService.recordStockTransfer(
          body.stockId,
          body.stockName,
          body.quantity,
          body.fromLocation,
          body.toLocation,
          body.notes,
          user.id
        );
        return NextResponse.json({
          success: true,
          message: 'Stock transfer recorded successfully',
          data: transferTransaction
        });
      case 'return':
        // Validate required fields
        if (!body.stockId || !body.stockName || body.quantity === undefined ||
            body.unitCost === undefined || !body.reason) {
          return NextResponse.json(
            { error: 'Missing required fields for return transaction' },
            { status: 400 }
          );
        }
        // Record stock return
        const returnTransaction = await enhancedInventoryTransactionService.recordStockReturn(
          body.stockId,
          body.stockName,
          body.quantity,
          body.unitCost,
          body.reason,
          body.notes,
          user.id
        );
        return NextResponse.json({
          success: true,
          message: 'Stock return recorded successfully',
          data: returnTransaction
        });
      case 'write_off':
        // Validate required fields
        if (!body.stockId || !body.stockName || body.quantity === undefined ||
            body.unitCost === undefined || !body.reason) {
          return NextResponse.json(
            { error: 'Missing required fields for write-off transaction' },
            { status: 400 }
          );
        }
        // Record stock write-off
        const writeOffTransaction = await enhancedInventoryTransactionService.recordStockWriteOff(
          body.stockId,
          body.stockName,
          body.quantity,
          body.unitCost,
          body.reason,
          body.notes,
          user.id
        );
        return NextResponse.json({
          success: true,
          message: 'Stock write-off recorded successfully',
          data: writeOffTransaction
        });
      default:
        return NextResponse.json(
          { error: 'Invalid transaction type' },
          { status: 400 }
        );
    }
  } catch (error: unknown) {
    logger.error('Error creating inventory transaction', LogCategory.INVENTORY, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { PurchaseOrderService } from '@/lib/backend/services/inventory/PurchaseOrderService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const purchaseOrderService = PurchaseOrderService;

/**
 * GET /api/inventory/purchase-orders/pending-approval
 * Get purchase orders pending approval
 */
export async function GET(req: NextRequest) {
  try {
    // Get session
    const user = await getCurrentUser(req);
    
    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.PROCUREMENT_MANAGER
    ]);
    
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const supplierId = searchParams.get('supplierId') || undefined;
    const sortField = searchParams.get('sortField') || 'orderDate';
    const sortOrder = searchParams.get('sortOrder') || 'asc';
    
    // Build sort object
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'desc' ? -1 : 1
    };
    
    // Get purchase orders pending approval
    const result = await purchaseOrderService.getPendingApproval({
      supplierId,
      sort,
      page,
      limit
    });
    
    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error getting purchase orders pending approval', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

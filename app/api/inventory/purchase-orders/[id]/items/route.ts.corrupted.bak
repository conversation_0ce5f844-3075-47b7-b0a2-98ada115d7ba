import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { PurchaseOrderService } from '@/lib/backend/services/inventory/PurchaseOrderService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const purchaseOrderService = PurchaseOrderService;

/**
 * POST /api/inventory/purchase-orders/[id]/items
 * Add item to purchase order
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Get session
    const user = await getCurrentUser(req);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await req.json();

    // Validate item
    if (!body.itemId || !body.name || !body.quantity || !body.unitPrice) {
      return NextResponse.json({
        error: 'Item must have itemId, name, quantity, and unitPrice'
      }, { status: 400 });
    }

    // Resolve the params promise
    const { id } = await params;

    // Add item to purchase order
    const updatedPurchaseOrder = await purchaseOrderService.addItem(
      id,
      body,
      user.id
    );

    if (!updatedPurchaseOrder) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    return NextResponse.json(updatedPurchaseOrder);
  } catch (error: unknown) {
    logger.error('Error adding item to purchase order', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

// app/api/inventory/transactions/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { inventoryTransactionService, IInventoryTransaction } from '@/lib/backend/services/inventory/InventoryTransactionService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Define transaction types
type TransactionType = IInventoryTransaction['transactionType'];
type ItemType = IInventoryTransaction['itemType'];

// Define MongoDB filter interface
interface MongoFilter {
  [key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | any[] | { $in?: any[] } | { $or?: any[] } | null | undefined;
  date?: { $gte?: Date; $lte?: Date };
  amount?: { $gte?: number; $lte?: number };
  createdAt?: { $gte?: Date; $lte?: Date };
  itemType?: string;
  transactionType?: string;
  $or?: any[];
}

/**
 * GET /api/inventory/transactions
 * Get all inventory transactions with pagination and filtering
 */
export async function GET(req: NextRequest) {
  try {
    // Get session
    const user = await getCurrentUser(req);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.INVENTORY_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const itemType = searchParams.get('itemType') || undefined;
    const transactionType = searchParams.get('transactionType') || undefined;
    const itemId = searchParams.get('itemId') || undefined;
    const location = searchParams.get('location') || undefined;
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined;
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined;
    const sortField = searchParams.get('sortField') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build sort object
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'desc' ? -1 : 1
    };

    // Get transactions
    let result;

    if (search) {
      // Search transactions
      result = await inventoryTransactionService.searchTransactions(search, {
        itemType: itemType as ItemType,
        transactionType: transactionType as TransactionType,
        startDate,
        endDate,
        sort,
        page,
        limit
      });
    } else if (itemId && itemType) {
      // Get transactions by item
      result = await inventoryTransactionService.getByItem(
        itemType as ItemType,
        itemId,
        {
          transactionType: transactionType as TransactionType,
          startDate,
          endDate,
          sort,
          page,
          limit
        }
      );
    } else if (location) {
      // Get transactions by location
      result = await inventoryTransactionService.getByLocation(
        location,
        {
          itemType: itemType as ItemType,
          transactionType: transactionType as TransactionType,
          startDate,
          endDate,
          sort,
          page,
          limit
        }
      );
    } else {
      // Get all transactions
      // Build filter
      const filter: MongoFilter = {};

      // Add item type filter
      if (itemType) {
        filter.itemType = itemType;
      }

      // Add transaction type filter
      if (transactionType) {
        filter.transactionType = transactionType;
      }

      // Add location filter
      if (location) {
        filter.$or = [
          { location },
          { fromLocation: location },
          { toLocation: location }
        ];
      }

      // Add date range filter
      if (startDate || endDate) {
        filter.createdAt = {};

        if (startDate) {
          filter.createdAt.$gte = startDate;
        }

        if (endDate) {
          filter.createdAt.$lte = endDate;
        }
      }

      result = await inventoryTransactionService.paginate(filter, page, limit, sort, ['createdBy']);
    }

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error getting inventory transactions', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

/**
 * POST /api/inventory/transactions
 * Create a new inventory transaction
 */
export async function POST(req: NextRequest) {
  try {
    // Get session
    const user = await getCurrentUser(req);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.INVENTORY_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.transactionType) {
      return NextResponse.json({ error: 'Transaction type is required' }, { status: 400 });
    }

    if (!body.itemType) {
      return NextResponse.json({ error: 'Item type is required' }, { status: 400 });
    }

    if (!body.itemId) {
      return NextResponse.json({ error: 'Item ID is required' }, { status: 400 });
    }

    if (!body.itemName) {
      return NextResponse.json({ error: 'Item name is required' }, { status: 400 });
    }

    if (body.quantity === undefined) {
      return NextResponse.json({ error: 'Quantity is required' }, { status: 400 });
    }

    // Add created by
    body.createdBy = user.id;

    // Create transaction
    const transaction = await inventoryTransactionService.createTransaction(body);

    return NextResponse.json(transaction, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error creating inventory transaction', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

// app/api/inventory/transactions/stock/purchase/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { inventoryTransactionService } from '@/lib/backend/services/inventory/InventoryTransactionService';
import { StockService } from '@/lib/backend/services/inventory/StockService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize stock service
const stockService = new StockService();

/**
 * POST /api/inventory/transactions/stock/purchase
 * Record stock purchase and update stock quantity
 */
export async function POST(req: NextRequest) {
  try {
    // Get session
    const user = await getCurrentUser(req);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.INVENTORY_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.stockId) {
      return NextResponse.json({ error: 'Stock ID is required' }, { status: 400 });
    }

    if (!body.stockName) {
      return NextResponse.json({ error: 'Stock name is required' }, { status: 400 });
    }

    if (!body.quantity || body.quantity <= 0) {
      return NextResponse.json({ error: 'Quantity must be positive' }, { status: 400 });
    }

    if (!body.unitCost || body.unitCost <= 0) {
      return NextResponse.json({ error: 'Unit cost must be positive' }, { status: 400 });
    }

    // Update stock quantity
    const updatedStock = await stockService.addStock(
      body.stockId,
      body.quantity,
      user.id
    );

    if (!updatedStock) {
      return NextResponse.json({ error: 'Stock not found' }, { status: 404 });
    }

    // Record transaction
    const transaction = await inventoryTransactionService.recordStockPurchase(
      body.stockId,
      body.stockName,
      body.quantity,
      body.unitCost,
      body.purchaseOrderId,
      body.purchaseOrderNumber,
      body.location,
      body.notes,
      user.id
    );

    return NextResponse.json({
      transaction,
      stock: updatedStock
    }, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error recording stock purchase', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

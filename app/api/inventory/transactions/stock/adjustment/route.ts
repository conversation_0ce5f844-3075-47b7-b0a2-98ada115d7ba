import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { inventoryTransactionService } from '@/lib/backend/services/inventory/InventoryTransactionService';
import { StockService } from '@/lib/backend/services/inventory/StockService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

// app/api/inventory/transactions/stock/adjustment/route.ts
// Initialize stock service
const stockService = new StockService();
/**
 * POST /api/inventory/transactions/stock/adjustment
 * Record stock adjustment and update stock quantity
 */
export async function POST(req: NextRequest) {
  try {
    // Get session
    const user = await getCurrentUser(req);
    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.INVENTORY_MANAGER
    ]);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    // Get request body
    const body = await req.json();
    // Validate required fields
    if (!body.stockId) {
      return NextResponse.json({ error: 'Stock ID is required' }, { status: 400 });
    }
    if (!body.stockName) {
      return NextResponse.json({ error: 'Stock name is required' }, { status: 400 });
    }
    if (body.quantity === undefined) {
      return NextResponse.json({ error: 'Quantity is required' }, { status: 400 });
    }
    if (!body.reason) {
      return NextResponse.json({ error: 'Reason is required' }, { status: 400 });
    }
    // Get current stock
    const stock = await stockService.findById(body.stockId);
    if (!stock) {
      return NextResponse.json({ error: 'Stock not found' }, { status: 404 });
    }
    const previousQuantity = stock.quantity;
    // Update stock quantity
    let updatedStock;
    if (body.quantity > 0) {
      // Add stock
      updatedStock = await stockService.addStock(
        body.stockId,
        body.quantity,
        user.id
      );
    } else if (body.quantity < 0) {
      // Remove stock
      try {
        updatedStock = await stockService.removeStock(
          body.stockId,
          Math.abs(body.quantity),
          user.id
        );
      } catch (error: unknown) {
        return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 400 });
      }
    } else {
      // No change in quantity
      updatedStock = stock;
    }
    // Record transaction
    if (!updatedStock) {
      return NextResponse.json({ error: 'Failed to update stock' }, { status: 500 });
    }
    const transaction = await inventoryTransactionService.recordStockAdjustment(
      body.stockId,
      body.stockName,
      body.quantity,
      previousQuantity,
      updatedStock.quantity,
      body.reason,
      body.location,
      body.notes,
      user.id
    );
    return NextResponse.json({
      transaction,
      stock: updatedStock
    }, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error recording stock adjustment', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}
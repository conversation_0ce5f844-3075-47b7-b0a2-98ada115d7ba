// app/api/inventory/transactions/stock/transfer/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { inventoryTransactionService } from '@/lib/backend/services/inventory/InventoryTransactionService';
import { StockService } from '@/lib/backend/services/inventory/StockService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize stock service
const stockService = new StockService();

/**
 * POST /api/inventory/transactions/stock/transfer
 * Record stock transfer between locations
 */
export async function POST(req: NextRequest) {
  try {
    // Get session
    const user = await getCurrentUser(req);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.INVENTORY_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.stockId) {
      return NextResponse.json({ error: 'Stock ID is required' }, { status: 400 });
    }

    if (!body.stockName) {
      return NextResponse.json({ error: 'Stock name is required' }, { status: 400 });
    }

    if (!body.quantity || body.quantity <= 0) {
      return NextResponse.json({ error: 'Quantity must be positive' }, { status: 400 });
    }

    if (!body.fromLocation) {
      return NextResponse.json({ error: 'From location is required' }, { status: 400 });
    }

    if (!body.toLocation) {
      return NextResponse.json({ error: 'To location is required' }, { status: 400 });
    }

    // Get current stock
    const stock = await stockService.findById(body.stockId);

    if (!stock) {
      return NextResponse.json({ error: 'Stock not found' }, { status: 404 });
    }

    // Check if there's enough stock
    if (stock.quantity < body.quantity) {
      return NextResponse.json({
        error: `Not enough stock. Current quantity: ${stock.quantity}, Requested: ${body.quantity}`
      }, { status: 400 });
    }

    // Record transaction
    const transaction = await inventoryTransactionService.recordStockTransfer(
      body.stockId,
      body.stockName,
      body.quantity,
      body.fromLocation,
      body.toLocation,
      body.notes,
      user.id
    );

    // Update stock location
    const updatedStock = await stockService.updateById(body.stockId, {
      location: body.toLocation
    });

    return NextResponse.json({
      transaction,
      stock: updatedStock
    }, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error recording stock transfer', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

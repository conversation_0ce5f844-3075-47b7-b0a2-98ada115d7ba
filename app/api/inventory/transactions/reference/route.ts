import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { InventoryTransactionService } from '@/lib/backend/services/inventory/InventoryTransactionService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

// Initialize services
const inventoryTransactionService = InventoryTransactionService;
/**
 * GET /api/inventory/transactions/reference
 * Get transactions by reference
 */
export async function GET(req: NextRequest) {
  try {
    // Get session
    const user = await getCurrentUser(req);
    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.INVENTORY_MANAGER
    ]);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const referenceType = searchParams.get('referenceType');
    const referenceId = searchParams.get('referenceId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const sortField = searchParams.get('sortField') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    // Validate required parameters
    if (!referenceType) {
      return NextResponse.json({ error: 'Reference type is required' }, { status: 400 });
    }
    if (!referenceId) {
      return NextResponse.json({ error: 'Reference ID is required' }, { status: 400 });
    }
    // Build sort object
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'desc' ? -1 : 1
    };
    // Get transactions by reference
    const result = await inventoryTransactionService.getByReference(
      referenceType as any,
      referenceId,
      {
        sort,
        page,
        limit
      }
    );
    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error getting transactions by reference', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}
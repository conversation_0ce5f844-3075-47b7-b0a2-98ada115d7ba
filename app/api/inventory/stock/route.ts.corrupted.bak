// app/api/inventory/stock/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { StockService } from '@/lib/backend/services/inventory/StockService';
import { StockImportExportService } from '@/lib/backend/services/inventory/StockImportExportService';
import { StockReportingService } from '@/lib/backend/services/inventory/StockReportingService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

// Define MongoDB filter interface
interface MongoFilter {
  [key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | string[] | { $in?: string[] } | null | undefined;
  date?: { $gte?: Date; $lte?: Date };
  amount?: { $gte?: number; $lte?: number };
  status?: string | { $in: string[] };
  category?: string;
}

// Initialize services
const stockService = new StockService();
const importExportService = new StockImportExportService();
const reportingService = new StockReportingService();

/**
 * GET handler for stock items
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(request: NextRequest) {
  try {
    // Get user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.INVENTORY_MANAGER,
      UserRole.INVENTORY_CLERK
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const category = searchParams.get('category');
    const status = searchParams.get('status');
    const query = searchParams.get('query');
    const format = searchParams.get('format');
    const report = searchParams.get('report');

    // Handle different request types
    if (id) {
      // Get stock item by ID
      const stockItem = await stockService.getStockDetails(id);

      if (!stockItem) {
        return NextResponse.json({ error: 'Stock item not found' }, { status: 404 });
      }

      return NextResponse.json(stockItem);
    } else if (format) {
      // Export stock items
      const filter: MongoFilter = {};

      // Add filters
      if (category) {
        filter.category = category;
      }

      if (status) {
        filter.status = status;
      }

      // Generate export
      let buffer: Buffer;
      let contentType: string;
      let filename: string;

      if (format === 'excel') {
        buffer = await importExportService.exportToExcel(filter);
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        filename = 'stock-items.xlsx';
      } else if (format === 'csv') {
        buffer = await importExportService.exportToCsv(filter);
        contentType = 'text/csv';
        filename = 'stock-items.csv';
      } else {
        return NextResponse.json({ error: 'Invalid format' }, { status: 400 });
      }

      // For Excel/CSV exports, create a Blob from the buffer
      const blob = new Blob([new Uint8Array(buffer)], { type: contentType });

      return new Response(blob, {
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${filename}"`
        }
      });
    } else if (report) {
      // Generate report
      if (report === 'inventory') {
        // Generate inventory report
        const includeValue = searchParams.get('includeValue') === 'true';

        const pdfBuffer = await reportingService.generateInventoryReport({
          category: category || undefined,
          status: status ? status.split(',') as any : undefined,
          includeValue
        });

        // For PDF exports, create a Blob from the buffer
        const blob = new Blob([new Uint8Array(pdfBuffer)], { type: 'application/pdf' });

        return new Response(blob, {
          headers: {
            'Content-Type': 'application/pdf',
            'Content-Disposition': 'attachment; filename="stock-inventory-report.pdf"'
          }
        });
      } else if (report === 'low-stock') {
        // Generate low stock report
        const reportData = await reportingService.generateLowStockReport({
          category: category || undefined
        });

        return NextResponse.json(reportData);
      } else if (report === 'value') {
        // Generate stock value report
        const reportData = await reportingService.generateStockValueReport({
          category: category || undefined
        });

        return NextResponse.json(reportData);
      } else {
        return NextResponse.json({ error: 'Invalid report type' }, { status: 400 });
      }
    } else if (query) {
      // Search stock items
      const result = await stockService.searchStock(query, {
        category: category || undefined,
        status: status ? status.split(',') as any : undefined,
        page,
        limit
      });

      return NextResponse.json(result);
    } else if (status === 'low' || status === 'out-of-stock') {
      // Get low stock items
      const result = await stockService.getLowStock({
        category: category || undefined,
        page,
        limit
      });

      return NextResponse.json(result);
    } else if (category) {
      // Get stock items by category
      const result = await stockService.getByCategory(category, {
        status: status ? status.split(',') as any : undefined,
        page,
        limit
      });

      return NextResponse.json(result);
    } else {
      // Get all stock items with pagination
      const filter: MongoFilter = {};

      if (status) {
        filter.status = status;
      }

      const result = await stockService.paginate(filter, page, limit, { name: 1 }, ['createdBy']);

      return NextResponse.json(result);
    }
  } catch (error: unknown) {
    logger.error('Error in stock items GET handler', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

/**
 * POST handler for stock items
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Get user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.INVENTORY_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await request.json();

    // Check if import request
    if (body.import) {
      // Check if user has import permissions
      const hasImportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.FINANCE_DIRECTOR,
        UserRole.INVENTORY_MANAGER
      ]);

      if (!hasImportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Get file buffer
      const fileBuffer = Buffer.from(body.fileData, 'base64');

      // Import data
      const importResult = body.format === 'csv'
        ? await importExportService.importFromExcel(fileBuffer, {
            ...body.options,
            userId: user.id
          })
        : await importExportService.importFromExcelWithCustomProcessing(fileBuffer, {
            ...body.options,
            userId: user.id
          });

      return NextResponse.json(importResult);
    }

    // Set created by
    body.createdBy = user.id;

    // Create stock item
    const stockItem = await stockService.createStock(body);

    return NextResponse.json(stockItem, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in stock items POST handler', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

/**
 * PATCH handler for stock items
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function PATCH(request: NextRequest) {
  try {
    // Get user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.INVENTORY_MANAGER,
      UserRole.INVENTORY_CLERK
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await request.json();

    // Check operation type
    if (body.operation === 'add' && body.id && body.quantity) {
      // Add stock
      const stockItem = await stockService.addStock(
        body.id,
        body.quantity,
        user.id
      );

      if (!stockItem) {
        return NextResponse.json({ error: 'Stock item not found' }, { status: 404 });
      }

      return NextResponse.json(stockItem);
    } else if (body.operation === 'remove' && body.id && body.quantity) {
      // Remove stock
      const stockItem = await stockService.removeStock(
        body.id,
        body.quantity,
        user.id
      );

      if (!stockItem) {
        return NextResponse.json({ error: 'Stock item not found' }, { status: 404 });
      }

      return NextResponse.json(stockItem);
    } else if (body.id) {
      // Update stock item
      const stockItem = await stockService.updateById(body.id, body);

      if (!stockItem) {
        return NextResponse.json({ error: 'Stock item not found' }, { status: 404 });
      }

      return NextResponse.json(stockItem);
    } else {
      return NextResponse.json({ error: 'Missing ID or invalid operation' }, { status: 400 });
    }
  } catch (error: unknown) {
    logger.error('Error in stock items PATCH handler', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}


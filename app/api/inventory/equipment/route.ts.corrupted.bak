// app/api/inventory/equipment/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { EquipmentService } from '@/lib/backend/services/inventory/EquipmentService';
import { EquipmentImportExportService } from '@/lib/backend/services/inventory/EquipmentImportExportService';
import { EquipmentReportingService } from '@/lib/backend/services/inventory/EquipmentReportingService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

// Define MongoDB filter interface
interface MongoFilter {
  [key: string]: any;
  category?: string;
  status?: string;
  departmentId?: string;
  $or?: Array<Record<string, any>>;
}

// Initialize services
const equipmentService = new EquipmentService();
const importExportService = new EquipmentImportExportService();
const reportingService = new EquipmentReportingService();

/**
 * GET handler for equipment
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(request: NextRequest) {
  try {
    // Get user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.INVENTORY_MANAGER,
      UserRole.INVENTORY_CLERK,
      UserRole.DEPARTMENT_HEAD,
      UserRole.EMPLOYEE
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const category = searchParams.get('category');
    const status = searchParams.get('status');
    const departmentId = searchParams.get('departmentId');
    const employeeId = searchParams.get('employeeId');
    const search = searchParams.get('search');
    const format = searchParams.get('format');
    const report = searchParams.get('report');

    // Handle different request types
    if (id) {
      // Get equipment by ID
      const equipment = await equipmentService.getEquipmentDetails(id);

      if (!equipment) {
        return NextResponse.json({ error: 'Equipment not found' }, { status: 404 });
      }

      return NextResponse.json(equipment);
    } else if (format) {
      // Export equipment
      // Check if user has export permissions
      const hasExportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.INVENTORY_MANAGER,
        UserRole.INVENTORY_CLERK,
        UserRole.DEPARTMENT_HEAD
      ]);

      if (!hasExportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Build filter
      const filter: MongoFilter = {};

      // Add filters
      if (category) {
        filter.category = category;
      }

      if (status) {
        filter.status = status;
      }

      if (departmentId) {
        filter.departmentId = departmentId;
      }

      // Generate export
      let buffer: Buffer;
      let contentType: string;
      let filename: string;

      if (format === 'excel') {
        buffer = await importExportService.exportToExcel(filter);
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        filename = 'equipment.xlsx';
      } else if (format === 'csv') {
        buffer = await importExportService.exportToCsv(filter);
        contentType = 'text/csv';
        filename = 'equipment.csv';
      } else if (format === 'pdf' && id) {
        buffer = await reportingService.generateEquipmentPdf(id);
        contentType = 'application/pdf';
        filename = `equipment-${id}.pdf`;
      } else {
        return NextResponse.json({ error: 'Invalid format' }, { status: 400 });
      }

      // Convert Buffer to Uint8Array which is acceptable for NextResponse
      const uint8Array = new Uint8Array(buffer);

      return new NextResponse(uint8Array, {
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${filename}"`
        }
      });
    } else if (report) {
      // Generate report
      // Check if user has report permissions
      const hasReportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.INVENTORY_MANAGER,
        UserRole.INVENTORY_CLERK,
        UserRole.DEPARTMENT_HEAD
      ]);

      if (!hasReportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      if (report === 'summary') {
        // Generate summary report
        const reportData = await reportingService.generateSummaryReport({
          departmentId: departmentId || undefined,
          category: category || undefined
        });

        return NextResponse.json(reportData);
      } else if (report === 'department' && departmentId) {
        // Generate department report
        const reportData = await reportingService.generateDepartmentReport(departmentId);

        return NextResponse.json(reportData);
      } else if (report === 'maintenance') {
        // Generate maintenance report
        const reportData = await reportingService.generateMaintenanceReport({
          departmentId: departmentId || undefined,
          category: category || undefined
        });

        return NextResponse.json(reportData);
      } else {
        return NextResponse.json({ error: 'Invalid report type or missing parameters' }, { status: 400 });
      }
    } else if (searchParams.get('maintenance') === 'due') {
      // Get equipment due for maintenance
      // Build options
      const options: Record<string, any> = {
        page,
        limit,
        sort: { nextMaintenanceDate: 1 }
      };

      if (departmentId) {
        options.department = departmentId;
      }

      const daysAhead = parseInt(searchParams.get('daysAhead') || '30');
      options.daysAhead = daysAhead;

      // Get equipment
      const result = await equipmentService.getDueForMaintenance(options);

      return NextResponse.json(result);
    } else if (category) {
      // Get equipment by category
      // Build options
      const options: Record<string, any> = {
        page,
        limit,
        sort: { name: 1 }
      };

      if (status) {
        options.status = status.split(',');
      }

      if (departmentId) {
        options.department = departmentId;
      }

      // Get equipment
      const result = await equipmentService.getByCategory(category, options);

      return NextResponse.json(result);
    } else if (departmentId) {
      // Get equipment by department
      // Build options
      const options: Record<string, any> = {
        page,
        limit,
        sort: { name: 1 }
      };

      if (status) {
        options.status = status.split(',');
      }

      if (category) {
        options.category = category;
      }

      // Get equipment
      const result = await equipmentService.getByDepartment(departmentId, options);

      return NextResponse.json(result);
    } else if (employeeId) {
      // Get equipment by employee
      // Check if user has permission to view employee's equipment
      const isInventoryStaff = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.INVENTORY_MANAGER,
        UserRole.INVENTORY_CLERK
      ]);

      const isDepartmentHead = hasRequiredPermissions(user, [
        UserRole.DEPARTMENT_HEAD
      ]);

      const isOwnEquipment = employeeId === user.id;

      if (!isInventoryStaff && !isOwnEquipment && !isDepartmentHead) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Build options
      const options: Record<string, any> = {
        page,
        limit,
        sort: { assignedDate: -1 }
      };

      if (category) {
        options.category = category;
      }

      // Get equipment
      const result = await equipmentService.getByEmployee(employeeId, options);

      return NextResponse.json(result);
    } else if (search) {
      // Search equipment
      // Build options
      const options: Record<string, any> = {
        page,
        limit,
        sort: { name: 1 }
      };

      if (status) {
        options.status = status.split(',');
      }

      if (category) {
        options.category = category;
      }

      if (departmentId) {
        options.department = departmentId;
      }

      // Search equipment
      const result = await equipmentService.searchEquipment(search, options);

      return NextResponse.json(result);
    } else {
      // Get all equipment with pagination
      // Build filter
      const filter: MongoFilter = {};

      if (status) {
        filter.status = status;
      }

      // Get equipment
      const result = await equipmentService.paginate(filter, page, limit, { name: 1 }, ['department', 'assignedTo', 'createdBy']);

      return NextResponse.json(result);
    }
  } catch (error: unknown) {
    logger.error('Error in equipment GET handler', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while processing equipment request' }, { status: 500 });
  }
}

/**
 * POST handler for equipment
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Get user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.INVENTORY_MANAGER,
      UserRole.INVENTORY_CLERK
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await request.json();

    // Check if import request
    if (body.import) {
      // Check if user has import permissions
      const hasImportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.INVENTORY_MANAGER
      ]);

      if (!hasImportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Get file buffer
      const fileBuffer = Buffer.from(body.fileData, 'base64');

      // Import data
      const importResult = body.format === 'csv'
        ? await importExportService.importFromCsv(fileBuffer, body.options)
        : await importExportService.importFromExcel(fileBuffer, body.options);

      return NextResponse.json(importResult);
    }

    // Set created by
    body.createdBy = user.id;

    // Create equipment
    const equipment = await equipmentService.createEquipment(body);

    return NextResponse.json(equipment, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in equipment POST handler', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while creating equipment' }, { status: 500 });
  }
}

/**
 * PATCH handler for equipment
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function PATCH(request: NextRequest) {
  try {
    // Get user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await request.json();

    // Check if status update
    if (body.id && body.status) {
      // Check permissions for status update
      const hasUpdatePermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.INVENTORY_MANAGER,
        UserRole.INVENTORY_CLERK
      ]);

      if (!hasUpdatePermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Update status
      const updatedEquipment = await equipmentService.updateStatus(
        body.id,
        body.status,
        user.id
      );

      return NextResponse.json(updatedEquipment);
    } else if (body.id && body.assignTo) {
      // Assign equipment
      // Check permissions for assignment
      const hasAssignPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.INVENTORY_MANAGER,
        UserRole.INVENTORY_CLERK
      ]);

      if (!hasAssignPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Assign equipment
      const updatedEquipment = await equipmentService.assignEquipment(
        body.id,
        body.assignTo,
        user.id
      );

      return NextResponse.json(updatedEquipment);
    } else if (body.id && body.unassign) {
      // Unassign equipment
      // Check permissions for unassignment
      const hasUnassignPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.INVENTORY_MANAGER,
        UserRole.INVENTORY_CLERK
      ]);

      if (!hasUnassignPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Unassign equipment
      const updatedEquipment = await equipmentService.unassignEquipment(
        body.id,
        user.id
      );

      return NextResponse.json(updatedEquipment);
    } else if (body.id && body.maintenance) {
      // Record maintenance
      // Check permissions for maintenance
      const hasMaintenancePermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.INVENTORY_MANAGER,
        UserRole.INVENTORY_CLERK
      ]);

      if (!hasMaintenancePermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Record maintenance
      const updatedEquipment = await equipmentService.recordMaintenance(
        body.id,
        {
          maintenanceDate: new Date(body.maintenance.date),
          notes: body.maintenance.notes,
          nextMaintenanceDate: body.maintenance.nextDate ? new Date(body.maintenance.nextDate) : undefined
        },
        user.id
      );

      return NextResponse.json(updatedEquipment);
    } else if (body.id) {
      // Update equipment
      // Check permissions for update
      const hasUpdatePermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.INVENTORY_MANAGER,
        UserRole.INVENTORY_CLERK
      ]);

      if (!hasUpdatePermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Update equipment
      const updatedEquipment = await equipmentService.updateById(body.id, body);

      return NextResponse.json(updatedEquipment);
    } else {
      return NextResponse.json({ error: 'Missing ID' }, { status: 400 });
    }
  } catch (error: unknown) {
    logger.error('Error in equipment PATCH handler', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while updating equipment' }, { status: 500 });
  }
}

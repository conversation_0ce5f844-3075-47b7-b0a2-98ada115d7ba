// app/api/inventory/suppliers/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { SupplierService } from '@/lib/backend/services/inventory/SupplierService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Define MongoDB filter interface
interface MongoFilter {
  [key: string]: string | number | boolean | Date | RegExp | { $in?: any[] } | { $gte?: Date | number; $lte?: Date | number } | null | undefined;
  status?: string | { $in: string[] };
}

// Initialize services
const supplierService = SupplierService;

/**
 * GET /api/inventory/suppliers
 * Get all suppliers with pagination and filtering
 */
export async function GET(req: NextRequest) {
  try {
    // Get session
    const user = await getCurrentUser(req);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.INVENTORY_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || undefined;
    const category = searchParams.get('category') || undefined;
    const sortField = searchParams.get('sortField') || 'name';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    // Build sort object
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'desc' ? -1 : 1
    };

    // Get suppliers
    let result;

    if (search) {
      // Search suppliers
      result = await supplierService.searchSuppliers(search, {
        status: status as any,
        category,
        sort,
        page,
        limit
      });
    } else if (category) {
      // Get suppliers by category
      result = await supplierService.getByCategory(category, {
        status: status as any,
        sort,
        page,
        limit
      });
    } else {
      // Get all suppliers
      const filter: MongoFilter = {};

      if (status) {
        filter.status = status;
      }

      result = await supplierService.paginate(filter, page, limit, sort, ['createdBy']);
    }

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error getting suppliers', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

/**
 * POST /api/inventory/suppliers
 * Create a new supplier
 */
export async function POST(req: NextRequest) {
  try {
    // Get session
    const user = await getCurrentUser(req);

    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has required permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.PROCUREMENT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.name) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }

    // Add created by
    body.createdBy = user.id;

    // Create supplier
    const supplier = await supplierService.createSupplier(body);

    return NextResponse.json(supplier, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error creating supplier', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

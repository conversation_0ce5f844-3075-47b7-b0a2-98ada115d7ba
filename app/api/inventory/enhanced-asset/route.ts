import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { enhancedAssetService } from '@/services/inventory/EnhancedAssetService';

export const runtime = 'nodejs';

// app/api/inventory/enhanced-asset/route.ts
// Custom auth system doesn't require authOptions;
/**
 * GET /api/inventory/enhanced-asset
 * Get assets with accounting integration
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const assetId = searchParams.get('assetId');
    const query = searchParams.get('query');
    const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1;
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 10;
    const status = searchParams.get('status');
    const category = searchParams.get('category');
    const location = searchParams.get('location');
    const sortBy = searchParams.get('sortBy');
    const sortOrder = searchParams.get('sortOrder');
    const calculateDepreciation = searchParams.get('calculateDepreciation') === 'true';
    const depreciationDate = searchParams.get('depreciationDate');
    // If assetId is provided, get specific asset
    if (assetId) {
      const asset = await enhancedAssetService.getById(assetId);
      if (!asset) {
        return NextResponse.json(
          { error: 'Asset not found' },
          { status: 404 }
        );
      }
      // If calculateDepreciation is true, calculate depreciation
      if (calculateDepreciation) {
        const depreciationAmount = await enhancedAssetService.calculateDepreciation(
          assetId,
          depreciationDate ? new Date(depreciationDate) : undefined
        );
        return NextResponse.json({
          asset,
          depreciation: {
            amount: depreciationAmount,
            date: depreciationDate ? new Date(depreciationDate) : new Date(),
            currentValue: (asset.currentValue || asset.purchasePrice) - depreciationAmount
          }
        });
      }
      return NextResponse.json(asset);
    }
    // If query is provided, search assets
    if (query) {
      const options: Record<string, any> = {
        page,
        limit,
        sort: {}
      };
      if (status) {
        options.status = status;
      }
      if (category) {
        options.category = category;
      }
      if (location) {
        options.location = location;
      }
      if (sortBy) {
        options.sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
      } else {
        options.sort.name = 1;
      }
      const result = await enhancedAssetService.search(query, options);
      return NextResponse.json(result);
    }
    // Otherwise, get all assets with pagination
    const options: Record<string, any> = {
      page,
      limit,
      sort: {}
    };
    if (status) {
      options.status = status;
    }
    if (category) {
      options.category = category;
    }
    if (location) {
      options.location = location;
    }
    if (sortBy) {
      options.sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    } else {
      options.sort.name = 1;
    }
    const result = await enhancedAssetService.getAll(options);
    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error getting assets', LogCategory.INVENTORY, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred while getting assets' },
      { status: 500 }
    );
  }
}
/**
 * POST /api/inventory/enhanced-asset
 * Create a new asset with accounting integration
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.INVENTORY_MANAGER,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get request body
    const body = await req.json();
    // Check operation type
    if (body.operation === 'depreciate') {
      // Validate required fields
      if (!body.assetId || !body.depreciationAmount) {
        return NextResponse.json(
          { error: 'Missing required fields: assetId, depreciationAmount' },
          { status: 400 }
        );
      }
      // Record depreciation
      const asset = await enhancedAssetService.recordDepreciation(
        body.assetId,
        body.depreciationAmount,
        body.depreciationDate ? new Date(body.depreciationDate) : new Date(),
        user.id
      );
      return NextResponse.json({
        success: true,
        message: 'Asset depreciation recorded successfully',
        data: asset
      });
    } else if (body.operation === 'dispose') {
      // Validate required fields
      if (!body.assetId || body.disposalAmount === undefined) {
        return NextResponse.json(
          { error: 'Missing required fields: assetId, disposalAmount' },
          { status: 400 }
        );
      }
      // Dispose asset
      const asset = await enhancedAssetService.disposeAsset(
        body.assetId,
        body.disposalAmount,
        body.disposalDate ? new Date(body.disposalDate) : new Date(),
        body.disposalMethod || 'sale',
        body.notes,
        user.id
      );
      return NextResponse.json({
        success: true,
        message: 'Asset disposed successfully',
        data: asset
      });
    } else {
      // Validate required fields for asset creation
      if (!body.name || !body.category || !body.purchaseDate || body.purchasePrice === undefined) {
        return NextResponse.json(
          { error: 'Missing required fields: name, category, purchaseDate, purchasePrice' },
          { status: 400 }
        );
      }
      // Create asset
      const asset = await enhancedAssetService.createAsset({
        ...body,
        purchaseDate: new Date(body.purchaseDate),
        status: body.status || 'available'
      }, user.id);
      return NextResponse.json({
        success: true,
        message: 'Asset created successfully',
        data: asset
      });
    }
  } catch (error: unknown) {
    logger.error('Error creating or updating asset', LogCategory.INVENTORY, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred while creating or updating asset' },
      { status: 500 }
    );
  }
}
/**
 * PATCH /api/inventory/enhanced-asset
 * Update an asset with accounting integration
 */
export async function PATCH(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.INVENTORY_MANAGER,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get request body
    const body = await req.json();
    // Validate required fields
    if (!body.assetId) {
      return NextResponse.json(
        { error: 'Missing required field: assetId' },
        { status: 400 }
      );
    }
    // Update asset
    const asset = await enhancedAssetService.updateAsset(
      body.assetId,
      {
        ...body,
        purchaseDate: body.purchaseDate ? new Date(body.purchaseDate) : undefined
      },
      user.id
    );
    return NextResponse.json({
      success: true,
      message: 'Asset updated successfully',
      data: asset
    });
  } catch (error: unknown) {
    logger.error('Error updating asset', LogCategory.INVENTORY, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred while updating asset' },
      { status: 500 }
    );
  }
}
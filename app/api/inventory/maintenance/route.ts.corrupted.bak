// app/api/inventory/maintenance/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { MaintenanceService } from '@/lib/backend/services/inventory/MaintenanceService';
import { MaintenanceImportExportService } from '@/lib/backend/services/inventory/MaintenanceImportExportService';
import { MaintenanceReportingService } from '@/lib/backend/services/inventory/MaintenanceReportingService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

// Define MongoDB filter interface
interface MongoFilter {
  [key: string]: any;
  equipmentId?: string;
  status?: string;
  type?: string;
  startDate?: Date;
  endDate?: Date;
}

// Initialize services
const maintenanceService = new MaintenanceService();
const importExportService = new MaintenanceImportExportService();
const reportingService = new MaintenanceReportingService();

/**
 * GET handler for maintenance records
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(request: NextRequest) {
  try {
    // Get user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.INVENTORY_MANAGER,
      UserRole.INVENTORY_CLERK,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const equipmentId = searchParams.get('equipmentId');
    const status = searchParams.get('status');
    const type = searchParams.get('type');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const format = searchParams.get('format');
    const report = searchParams.get('report');

    // Handle different request types
    if (id) {
      // Get maintenance record by ID
      const record = await maintenanceService.getMaintenanceDetails(id);

      if (!record) {
        return NextResponse.json({ error: 'Maintenance record not found' }, { status: 404 });
      }

      return NextResponse.json(record);
    } else if (format) {
      // Export maintenance records
      // Check if user has export permissions
      const hasExportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.INVENTORY_MANAGER,
        UserRole.INVENTORY_CLERK,
        UserRole.DEPARTMENT_HEAD
      ]);

      if (!hasExportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Build filter
      const filter: MongoFilter = {};

      // Add filters
      if (equipmentId) {
        filter.equipmentId = equipmentId;
      }

      if (status) {
        filter.status = status;
      }

      if (type) {
        filter.type = type;
      }

      if (startDate && endDate) {
        filter.startDate = new Date(startDate);
        filter.endDate = new Date(endDate);
      }

      // Generate export
      let buffer: Buffer;
      let contentType: string;
      let filename: string;

      if (format === 'excel') {
        buffer = await importExportService.exportToExcel(filter);
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        filename = 'maintenance-records.xlsx';
      } else if (format === 'csv') {
        buffer = await importExportService.exportToCsv(filter);
        contentType = 'text/csv';
        filename = 'maintenance-records.csv';
      } else if (format === 'pdf' && id) {
        buffer = await reportingService.generateMaintenancePdf(id);
        contentType = 'application/pdf';
        filename = `maintenance-record-${id}.pdf`;
      } else {
        return NextResponse.json({ error: 'Invalid format' }, { status: 400 });
      }

      // Convert Buffer to Uint8Array which is acceptable for NextResponse
      const uint8Array = new Uint8Array(buffer);

      return new NextResponse(uint8Array, {
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${filename}"`
        }
      });
    } else if (report) {
      // Generate report
      // Check if user has report permissions
      const hasReportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.INVENTORY_MANAGER,
        UserRole.INVENTORY_CLERK,
        UserRole.DEPARTMENT_HEAD
      ]);

      if (!hasReportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      if (report === 'summary') {
        // Generate summary report
        const reportData = await reportingService.generateSummaryReport({
          startDate: startDate ? new Date(startDate) : undefined,
          endDate: endDate ? new Date(endDate) : undefined,
          type: type || undefined
        });

        return NextResponse.json(reportData);
      } else if (report === 'equipment' && equipmentId) {
        // Generate equipment report
        // Note: The API only requires equipmentId, date parameters are handled internally
        const reportData = await reportingService.generateEquipmentReport(equipmentId);

        return NextResponse.json(reportData);
      } else {
        return NextResponse.json({ error: 'Invalid report type or missing parameters' }, { status: 400 });
      }
    } else if (searchParams.get('scheduled') === 'true') {
      // Get scheduled maintenance
      // Build options
      const options: Record<string, any> = {
        page,
        limit,
        sort: { date: 1 }
      };

      if (startDate && endDate) {
        options.startDate = new Date(startDate);
        options.endDate = new Date(endDate);
      }

      const priority = searchParams.get('priority');
      if (priority) {
        options.priority = priority.split(',');
      }

      // Get scheduled maintenance
      const result = await maintenanceService.getScheduledMaintenance(options);

      return NextResponse.json(result);
    } else if (equipmentId) {
      // Get maintenance records by equipment
      // Build options
      const options: Record<string, any> = {
        page,
        limit,
        sort: { date: -1 }
      };

      if (status) {
        options.status = status.split(',');
      }

      if (type) {
        options.type = type.split(',');
      }

      if (startDate && endDate) {
        options.startDate = new Date(startDate);
        options.endDate = new Date(endDate);
      }

      // Get maintenance records
      const result = await maintenanceService.getByEquipment(equipmentId, options);

      return NextResponse.json(result);
    } else if (startDate && endDate) {
      // Get maintenance records by date range
      // Build options
      const options: Record<string, any> = {
        page,
        limit,
        sort: { date: 1 }
      };

      if (status) {
        options.status = status.split(',');
      }

      if (type) {
        options.type = type.split(',');
      }

      // Get maintenance records
      const result = await maintenanceService.getByDateRange(
        new Date(startDate),
        new Date(endDate),
        options
      );

      return NextResponse.json(result);
    } else {
      // Get all maintenance records with pagination
      // Build filter
      const filter: MongoFilter = {};

      if (status) {
        filter.status = status;
      }

      if (type) {
        filter.type = type;
      }

      // Get maintenance records
      const result = await maintenanceService.paginate(filter, page, limit, { date: -1 }, ['equipmentId', 'performedBy', 'approvedBy', 'createdBy']);

      return NextResponse.json(result);
    }
  } catch (error: unknown) {
    logger.error('Error in maintenance records GET handler', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while processing maintenance records' }, { status: 500 });
  }
}

/**
 * POST handler for maintenance records
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Get user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.INVENTORY_MANAGER,
      UserRole.INVENTORY_CLERK
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await request.json();

    // Check if import request
    if (body.import) {
      // Check if user has import permissions
      const hasImportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.INVENTORY_MANAGER
      ]);

      if (!hasImportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Get file buffer
      const fileBuffer = Buffer.from(body.fileData, 'base64');

      // Import data
      const importResult = body.format === 'csv'
        ? await importExportService.importFromCsv(fileBuffer, body.options)
        : await importExportService.importFromExcel(fileBuffer, body.options);

      return NextResponse.json(importResult);
    }

    // Set created by
    body.createdBy = user.id;

    // Create maintenance record
    const record = await maintenanceService.createMaintenanceRecord(body);

    return NextResponse.json(record, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in maintenance records POST handler', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while creating maintenance record' }, { status: 500 });
  }
}

/**
 * PATCH handler for maintenance records
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function PATCH(request: NextRequest) {
  try {
    // Get user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.INVENTORY_MANAGER,
      UserRole.INVENTORY_CLERK
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await request.json();

    // Check if status update
    if (body.id && body.status) {
      // Update status
      const updatedRecord = await maintenanceService.updateStatus(
        body.id,
        body.status,
        user.id,
        {
          actions: body.actions,
          parts: body.parts,
          cost: body.cost,
          duration: body.duration,
          performedBy: body.performedBy,
          nextMaintenanceDate: body.nextMaintenanceDate ? new Date(body.nextMaintenanceDate) : undefined,
          notes: body.notes
        }
      );

      return NextResponse.json(updatedRecord);
    } else if (body.id) {
      // Update maintenance record
      const updatedRecord = await maintenanceService.updateById(body.id, body);

      return NextResponse.json(updatedRecord);
    } else {
      return NextResponse.json({ error: 'Missing ID' }, { status: 400 });
    }
  } catch (error: unknown) {
    logger.error('Error in maintenance records PATCH handler', LogCategory.INVENTORY, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while updating maintenance record' }, { status: 500 });
  }
}
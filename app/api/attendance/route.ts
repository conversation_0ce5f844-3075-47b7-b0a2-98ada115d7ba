import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database/connection';
import { AttendanceService } from '@/lib/backend/services/hr/AttendanceService';
import { authMiddleware } from '@/lib/backend/middleware/authMiddleware';
import { errorHandler } from '@/lib/backend/utils/errorHandler';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/attendance - Get attendance records with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    // Authenticate user
    const authResult = await authMiddleware(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const employeeId = searchParams.get('employeeId');
    const department = searchParams.get('department');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Build filter object
    const filter: any = {};
    
    if (startDate || endDate) {
      filter.date = {};
      if (startDate) filter.date.$gte = new Date(startDate);
      if (endDate) filter.date.$lte = new Date(endDate);
    }
    
    if (employeeId) {
      filter.employeeId = employeeId;
    }
    
    if (status) {
      filter.status = status;
    }

    // Get attendance records
    const attendanceService = new AttendanceService();
    const result = await attendanceService.findWithPagination(
      filter,
      {
        page,
        limit,
        populate: [
          {
            path: 'employeeId',
            select: 'firstName lastName email department avatar',
            populate: {
              path: 'department',
              select: 'name'
            }
          }
        ],
        sort: { date: -1 }
      }
    );

    // Transform data for frontend
    const transformedRecords = result.data.map((record: any) => ({
      id: record._id,
      employeeId: record.employeeId._id,
      employeeName: `${record.employeeId.firstName} ${record.employeeId.lastName}`,
      employee: {
        id: record.employeeId._id,
        name: `${record.employeeId.firstName} ${record.employeeId.lastName}`,
        department: record.employeeId.department?.name || 'Unknown',
        avatar: record.employeeId.avatar || '/placeholder-user.jpg'
      },
      date: record.date,
      checkIn: record.checkIn,
      checkOut: record.checkOut,
      status: record.status,
      workHours: record.workHours,
      hoursWorked: record.workHours,
      overtime: record.overtime,
      location: record.location,
      notes: record.notes,
      createdAt: record.createdAt,
      updatedAt: record.updatedAt
    }));

    logger.info('Attendance records retrieved successfully', LogCategory.API, {
      userId: authResult.user.id,
      count: transformedRecords.length,
      total: result.total,
      filters: { startDate, endDate, employeeId, department, status }
    });

    return NextResponse.json({
      success: true,
      message: 'Attendance records retrieved successfully',
      data: {
        records: transformedRecords,
        total: result.total,
        page: result.page,
        totalPages: result.totalPages,
        hasMore: result.hasMore
      }
    });

  } catch (error) {
    logger.error('Error retrieving attendance records', LogCategory.API, { error });
    return errorHandler.handleError(error);
  }
}

/**
 * POST /api/attendance - Create new attendance record
 */
export async function POST(request: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    // Authenticate user
    const authResult = await authMiddleware(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      employeeId,
      date,
      checkIn,
      checkOut,
      status,
      workHours,
      overtime,
      location,
      notes
    } = body;

    // Validate required fields
    if (!employeeId || !date || !status) {
      return NextResponse.json(
        { success: false, message: 'Employee ID, date, and status are required' },
        { status: 400 }
      );
    }

    // Create attendance record
    const attendanceService = new AttendanceService();
    const attendanceData = {
      employeeId,
      date: new Date(date),
      checkIn: checkIn ? new Date(checkIn) : undefined,
      checkOut: checkOut ? new Date(checkOut) : undefined,
      status,
      workHours,
      overtime,
      location,
      notes,
      createdBy: authResult.user.id
    };

    const newRecord = await attendanceService.create(attendanceData);

    // Populate employee data for response
    const populatedRecord = await attendanceService.findById(newRecord._id, {
      populate: [
        {
          path: 'employeeId',
          select: 'firstName lastName email department avatar',
          populate: {
            path: 'department',
            select: 'name'
          }
        }
      ]
    });

    // Transform data for frontend
    const transformedRecord = {
      id: populatedRecord._id,
      employeeId: populatedRecord.employeeId._id,
      employeeName: `${populatedRecord.employeeId.firstName} ${populatedRecord.employeeId.lastName}`,
      employee: {
        id: populatedRecord.employeeId._id,
        name: `${populatedRecord.employeeId.firstName} ${populatedRecord.employeeId.lastName}`,
        department: populatedRecord.employeeId.department?.name || 'Unknown',
        avatar: populatedRecord.employeeId.avatar || '/placeholder-user.jpg'
      },
      date: populatedRecord.date,
      checkIn: populatedRecord.checkIn,
      checkOut: populatedRecord.checkOut,
      status: populatedRecord.status,
      workHours: populatedRecord.workHours,
      hoursWorked: populatedRecord.workHours,
      overtime: populatedRecord.overtime,
      location: populatedRecord.location,
      notes: populatedRecord.notes,
      createdAt: populatedRecord.createdAt,
      updatedAt: populatedRecord.updatedAt
    };

    logger.info('Attendance record created successfully', LogCategory.API, {
      userId: authResult.user.id,
      recordId: newRecord._id,
      employeeId
    });

    return NextResponse.json({
      success: true,
      message: 'Attendance record created successfully',
      data: transformedRecord
    }, { status: 201 });

  } catch (error) {
    logger.error('Error creating attendance record', LogCategory.API, { error });
    return errorHandler.handleError(error);
  }
}

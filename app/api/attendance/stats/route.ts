import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database/connection';
import { AttendanceService } from '@/lib/backend/services/hr/AttendanceService';
import { EmployeeService } from '@/lib/backend/services/hr/EmployeeService';
import { authMiddleware } from '@/lib/backend/middleware/authMiddleware';
import { errorHandler } from '@/lib/backend/utils/errorHandler';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/attendance/stats - Get attendance statistics
 */
export async function GET(request: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    // Authenticate user
    const authResult = await authMiddleware(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const department = searchParams.get('department');

    // Default to today if no dates provided
    const today = new Date();
    const defaultStartDate = startDate ? new Date(startDate) : new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const defaultEndDate = endDate ? new Date(endDate) : new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);

    const attendanceService = new AttendanceService();
    const employeeService = new EmployeeService();

    // Build filter for attendance records
    const attendanceFilter: any = {
      date: {
        $gte: defaultStartDate,
        $lte: defaultEndDate
      }
    };

    // Build filter for employees
    const employeeFilter: any = {
      status: 'active'
    };

    if (department) {
      // Get department employees
      const departmentEmployees = await employeeService.find({
        ...employeeFilter,
        department: department
      });
      
      const employeeIds = departmentEmployees.map(emp => emp._id);
      attendanceFilter.employeeId = { $in: employeeIds };
      employeeFilter.department = department;
    }

    // Get all active employees (for total count)
    const totalEmployees = await employeeService.countDocuments(employeeFilter);

    // Get attendance records for the period
    const attendanceRecords = await attendanceService.find(attendanceFilter);

    // Calculate statistics
    const stats = {
      totalEmployees,
      presentToday: 0,
      absentToday: 0,
      lateToday: 0,
      averageAttendanceRate: 0,
      totalHoursWorked: 0,
      overtimeHours: 0
    };

    // Process attendance records
    const employeeAttendance = new Map();
    let totalWorkHours = 0;
    let totalOvertimeHours = 0;

    attendanceRecords.forEach(record => {
      const employeeId = record.employeeId.toString();
      
      // Track unique employees for the day
      if (!employeeAttendance.has(employeeId)) {
        employeeAttendance.set(employeeId, record);
        
        // Count by status
        switch (record.status) {
          case 'present':
            stats.presentToday++;
            break;
          case 'late':
            stats.presentToday++;
            stats.lateToday++;
            break;
          case 'absent':
            stats.absentToday++;
            break;
        }
      }
      
      // Sum work hours and overtime
      if (record.workHours) {
        totalWorkHours += record.workHours;
      }
      if (record.overtime) {
        totalOvertimeHours += record.overtime;
      }
    });

    // Calculate derived statistics
    stats.totalHoursWorked = Math.round(totalWorkHours * 100) / 100;
    stats.overtimeHours = Math.round(totalOvertimeHours * 100) / 100;
    
    if (stats.totalEmployees > 0) {
      stats.averageAttendanceRate = Math.round((stats.presentToday / stats.totalEmployees) * 100 * 100) / 100;
    }

    // Handle case where we have more present employees than total (data inconsistency)
    if (stats.presentToday > stats.totalEmployees) {
      stats.totalEmployees = stats.presentToday + stats.absentToday;
    }

    // Calculate absent count if not already calculated
    if (stats.absentToday === 0 && stats.totalEmployees > stats.presentToday) {
      stats.absentToday = stats.totalEmployees - stats.presentToday;
    }

    logger.info('Attendance statistics retrieved successfully', LogCategory.API, {
      userId: authResult.user.id,
      period: { startDate: defaultStartDate, endDate: defaultEndDate },
      department,
      stats
    });

    return NextResponse.json({
      success: true,
      message: 'Attendance statistics retrieved successfully',
      data: stats
    });

  } catch (error) {
    logger.error('Error retrieving attendance statistics', LogCategory.API, { error });
    return errorHandler.handleError(error);
  }
}

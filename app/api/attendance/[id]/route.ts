import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database/connection';
import { AttendanceService } from '@/lib/backend/services/hr/AttendanceService';
import { authMiddleware } from '@/lib/backend/middleware/authMiddleware';
import { errorHandler } from '@/lib/backend/utils/errorHandler';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/attendance/[id] - Get attendance record by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    await connectToDatabase();

    // Authenticate user
    const authResult = await authMiddleware(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;

    // Get attendance record
    const attendanceService = new AttendanceService();
    const record = await attendanceService.findById(id, {
      populate: [
        {
          path: 'employeeId',
          select: 'firstName lastName email department avatar',
          populate: {
            path: 'department',
            select: 'name'
          }
        }
      ]
    });

    if (!record) {
      return NextResponse.json(
        { success: false, message: 'Attendance record not found' },
        { status: 404 }
      );
    }

    // Transform data for frontend
    const transformedRecord = {
      id: record._id,
      employeeId: record.employeeId._id,
      employeeName: `${record.employeeId.firstName} ${record.employeeId.lastName}`,
      employee: {
        id: record.employeeId._id,
        name: `${record.employeeId.firstName} ${record.employeeId.lastName}`,
        department: record.employeeId.department?.name || 'Unknown',
        avatar: record.employeeId.avatar || '/placeholder-user.jpg'
      },
      date: record.date,
      checkIn: record.checkIn,
      checkOut: record.checkOut,
      status: record.status,
      workHours: record.workHours,
      hoursWorked: record.workHours,
      overtime: record.overtime,
      location: record.location,
      notes: record.notes,
      createdAt: record.createdAt,
      updatedAt: record.updatedAt
    };

    logger.info('Attendance record retrieved successfully', LogCategory.API, {
      userId: authResult.user.id,
      recordId: id
    });

    return NextResponse.json({
      success: true,
      message: 'Attendance record retrieved successfully',
      data: transformedRecord
    });

  } catch (error) {
    logger.error('Error retrieving attendance record', LogCategory.API, { error });
    return errorHandler.handleError(error);
  }
}

/**
 * PUT /api/attendance/[id] - Update attendance record
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    await connectToDatabase();

    // Authenticate user
    const authResult = await authMiddleware(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;
    const body = await request.json();
    const {
      checkIn,
      checkOut,
      status,
      workHours,
      overtime,
      location,
      notes
    } = body;

    // Update attendance record
    const attendanceService = new AttendanceService();
    const updateData: any = {
      updatedBy: authResult.user.id
    };

    if (checkIn !== undefined) updateData.checkIn = checkIn ? new Date(checkIn) : null;
    if (checkOut !== undefined) updateData.checkOut = checkOut ? new Date(checkOut) : null;
    if (status !== undefined) updateData.status = status;
    if (workHours !== undefined) updateData.workHours = workHours;
    if (overtime !== undefined) updateData.overtime = overtime;
    if (location !== undefined) updateData.location = location;
    if (notes !== undefined) updateData.notes = notes;

    const updatedRecord = await attendanceService.update(id, updateData);

    if (!updatedRecord) {
      return NextResponse.json(
        { success: false, message: 'Attendance record not found' },
        { status: 404 }
      );
    }

    // Populate employee data for response
    const populatedRecord = await attendanceService.findById(id, {
      populate: [
        {
          path: 'employeeId',
          select: 'firstName lastName email department avatar',
          populate: {
            path: 'department',
            select: 'name'
          }
        }
      ]
    });

    // Transform data for frontend
    const transformedRecord = {
      id: populatedRecord._id,
      employeeId: populatedRecord.employeeId._id,
      employeeName: `${populatedRecord.employeeId.firstName} ${populatedRecord.employeeId.lastName}`,
      employee: {
        id: populatedRecord.employeeId._id,
        name: `${populatedRecord.employeeId.firstName} ${populatedRecord.employeeId.lastName}`,
        department: populatedRecord.employeeId.department?.name || 'Unknown',
        avatar: populatedRecord.employeeId.avatar || '/placeholder-user.jpg'
      },
      date: populatedRecord.date,
      checkIn: populatedRecord.checkIn,
      checkOut: populatedRecord.checkOut,
      status: populatedRecord.status,
      workHours: populatedRecord.workHours,
      hoursWorked: populatedRecord.workHours,
      overtime: populatedRecord.overtime,
      location: populatedRecord.location,
      notes: populatedRecord.notes,
      createdAt: populatedRecord.createdAt,
      updatedAt: populatedRecord.updatedAt
    };

    logger.info('Attendance record updated successfully', LogCategory.API, {
      userId: authResult.user.id,
      recordId: id
    });

    return NextResponse.json({
      success: true,
      message: 'Attendance record updated successfully',
      data: transformedRecord
    });

  } catch (error) {
    logger.error('Error updating attendance record', LogCategory.API, { error });
    return errorHandler.handleError(error);
  }
}

/**
 * DELETE /api/attendance/[id] - Delete attendance record
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    await connectToDatabase();

    // Authenticate user
    const authResult = await authMiddleware(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;

    // Delete attendance record
    const attendanceService = new AttendanceService();
    const deletedRecord = await attendanceService.delete(id);

    if (!deletedRecord) {
      return NextResponse.json(
        { success: false, message: 'Attendance record not found' },
        { status: 404 }
      );
    }

    logger.info('Attendance record deleted successfully', LogCategory.API, {
      userId: authResult.user.id,
      recordId: id
    });

    return NextResponse.json({
      success: true,
      message: 'Attendance record deleted successfully'
    });

  } catch (error) {
    logger.error('Error deleting attendance record', LogCategory.API, { error });
    return errorHandler.handleError(error);
  }
}

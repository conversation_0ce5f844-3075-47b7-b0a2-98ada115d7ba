import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { attendancePayrollIntegrationService } from '@/lib/services/payroll/attendance-payroll-integration-service';
import Employee from '@/models/Employee';
import { errorService } from '@/lib/backend/services/error-service';
import { z } from 'zod';

export const runtime = 'nodejs';

// Schema for attendance summary generation options
const attendanceSummaryOptionsSchema = z.object({
  includeWeekends: z.boolean().optional().default(false),
  includeHolidays: z.boolean().optional().default(false),
  workingHoursPerDay: z.number().min(1).max(24).optional().default(8),
  overtimeThreshold: z.number().min(1).max(24).optional().default(8),
  lateThresholdMinutes: z.number().min(0).max(120).optional().default(15),
  regenerate: z.boolean().optional().default(false)
});

/**
 * Parse period parameter (format: YYYY-MM or YYYY-M)
 */
function parsePeriod(period: string): { year: number; month: number } | null {
  const match = period.match(/^(\d{4})-(\d{1,2})$/);
  if (!match) return null;
  
  const year = parseInt(match[1]);
  const month = parseInt(match[2]);
  
  if (year < 2020 || year > 2100 || month < 1 || month > 12) {
    return null;
  }
  
  return { year, month };
}

/**
 * GET /api/attendance/summary/[employeeId]/[period]
 * Get attendance summary for an employee for a specific period
 * Period format: YYYY-MM (e.g., 2024-01)
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ employeeId: string; period: string }> }
) {
  try {
    // Resolve params
    const { employeeId, period } = await params;

    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.EMPLOYEE // Allow employees to view their own attendance
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user._id.toString(),
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Check if employee exists
    const employee = await Employee.findById(employeeId);
    if (!employee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    // If user is an employee, they can only view their own attendance
    if (user.role === UserRole.EMPLOYEE && user.employeeId?.toString() !== employeeId) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user._id.toString(),
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: {
            requestedEmployeeId: employeeId,
            userEmployeeId: user.employeeId?.toString()
          }
        }
      );
    }

    // Parse period
    const parsedPeriod = parsePeriod(period);
    if (!parsedPeriod) {
      return NextResponse.json(
        { error: 'Invalid period format. Use YYYY-MM (e.g., 2024-01)' },
        { status: 400 }
      );
    }

    const { year, month } = parsedPeriod;

    // Get attendance summary
    const summary = await attendancePayrollIntegrationService.getAttendanceSummary(
      employeeId,
      year,
      month
    );

    if (!summary) {
      return NextResponse.json(
        { 
          error: 'Attendance summary not found',
          message: 'No attendance summary exists for this employee and period. Generate one first.'
        },
        { status: 404 }
      );
    }

    logger.info('Attendance summary retrieved', LogCategory.PAYROLL, {
      userId: user._id.toString(),
      employeeId,
      employeeName: `${employee.firstName} ${employee.lastName}`,
      period: { year, month },
      attendanceRate: summary.attendanceRate,
      totalHours: summary.totalHours
    });

    return NextResponse.json({
      success: true,
      message: 'Attendance summary retrieved successfully',
      data: {
        employeeId,
        employeeName: `${employee.firstName} ${employee.lastName}`,
        period: { year, month },
        summary
      }
    });

  } catch (error) {
    logger.error('Error retrieving attendance summary', LogCategory.API, error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/attendance/summary/[employeeId]/[period]
 * Generate attendance summary for an employee for a specific period
 */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ employeeId: string; period: string }> }
) {
  try {
    // Resolve params
    const { employeeId, period } = await params;

    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions (only HR and Finance can generate summaries)
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user._id.toString(),
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Check if employee exists
    const employee = await Employee.findById(employeeId);
    if (!employee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    // Parse period
    const parsedPeriod = parsePeriod(period);
    if (!parsedPeriod) {
      return NextResponse.json(
        { error: 'Invalid period format. Use YYYY-MM (e.g., 2024-01)' },
        { status: 400 }
      );
    }

    const { year, month } = parsedPeriod;

    // Get and validate request body
    const body = await req.json().catch(() => ({}));
    const validationResult = attendanceSummaryOptionsSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const options = validationResult.data;

    // Generate attendance summary
    const result = await attendancePayrollIntegrationService.generateMonthlySummary(
      employeeId,
      year,
      month,
      user._id.toString(),
      options
    );

    if (!result.success) {
      return NextResponse.json(
        { 
          error: result.error || 'Failed to generate attendance summary',
          message: result.message
        },
        { status: 400 }
      );
    }

    logger.info('Attendance summary generated', LogCategory.PAYROLL, {
      userId: user._id.toString(),
      employeeId,
      employeeName: `${employee.firstName} ${employee.lastName}`,
      period: { year, month },
      attendanceRate: result.summary?.attendanceRate,
      totalHours: result.summary?.totalHours,
      regenerated: options.regenerate
    });

    return NextResponse.json({
      success: true,
      message: result.message,
      data: {
        employeeId,
        employeeName: `${employee.firstName} ${employee.lastName}`,
        period: { year, month },
        summary: result.summary,
        options
      }
    });

  } catch (error) {
    logger.error('Error generating attendance summary', LogCategory.API, error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/attendance/summary/[employeeId]/[period]
 * Finalize attendance summary for payroll processing
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ employeeId: string; period: string }> }
) {
  try {
    // Resolve params
    const { employeeId, period } = await params;

    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions (only senior HR and Finance can finalize)
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user._id.toString(),
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Check if employee exists
    const employee = await Employee.findById(employeeId);
    if (!employee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    // Parse period
    const parsedPeriod = parsePeriod(period);
    if (!parsedPeriod) {
      return NextResponse.json(
        { error: 'Invalid period format. Use YYYY-MM (e.g., 2024-01)' },
        { status: 400 }
      );
    }

    const { year, month } = parsedPeriod;

    // Finalize attendance summary
    const result = await attendancePayrollIntegrationService.finalizeMonthlySummary(
      employeeId,
      year,
      month,
      user._id.toString()
    );

    if (!result.success) {
      return NextResponse.json(
        { 
          error: result.error || 'Failed to finalize attendance summary',
          message: result.message
        },
        { status: 400 }
      );
    }

    logger.info('Attendance summary finalized', LogCategory.PAYROLL, {
      userId: user._id.toString(),
      employeeId,
      employeeName: `${employee.firstName} ${employee.lastName}`,
      period: { year, month }
    });

    return NextResponse.json({
      success: true,
      message: result.message,
      data: {
        employeeId,
        employeeName: `${employee.firstName} ${employee.lastName}`,
        period: { year, month },
        summary: result.summary
      }
    });

  } catch (error) {
    logger.error('Error finalizing attendance summary', LogCategory.API, error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

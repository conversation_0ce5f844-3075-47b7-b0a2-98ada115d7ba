import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { attendancePayrollIntegrationService } from '@/lib/services/payroll/attendance-payroll-integration-service';
import { errorService } from '@/lib/backend/services/error-service';
import { z } from 'zod';

export const runtime = 'nodejs';

// Schema for bulk attendance summary generation
const bulkAttendanceSummarySchema = z.object({
  year: z.number().min(2020).max(2100),
  month: z.number().min(1).max(12),
  options: z.object({
    includeWeekends: z.boolean().optional().default(false),
    includeHolidays: z.boolean().optional().default(false),
    workingHoursPerDay: z.number().min(1).max(24).optional().default(8),
    overtimeThreshold: z.number().min(1).max(24).optional().default(8),
    lateThresholdMinutes: z.number().min(0).max(120).optional().default(15)
  }).optional().default({})
});

/**
 * POST /api/attendance/summary/bulk
 * Generate attendance summaries for all active employees for a specific period
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions (only senior HR and Finance can generate bulk summaries)
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user._id.toString(),
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get and validate request body
    const body = await req.json();
    const validationResult = bulkAttendanceSummarySchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { year, month, options } = validationResult.data;

    logger.info('Starting bulk attendance summary generation', LogCategory.PAYROLL, {
      userId: user._id.toString(),
      period: { year, month },
      options
    });

    // Generate attendance summaries for all employees
    const result = await attendancePayrollIntegrationService.generateAllEmployeeSummaries(
      year,
      month,
      user._id.toString(),
      options
    );

    logger.info('Bulk attendance summary generation completed', LogCategory.PAYROLL, {
      userId: user._id.toString(),
      period: { year, month },
      successful: result.success,
      failed: result.failed,
      totalEmployees: result.success + result.failed
    });

    return NextResponse.json({
      success: true,
      message: `Bulk attendance summary generation completed. ${result.success} successful, ${result.failed} failed.`,
      data: {
        period: { year, month },
        summary: {
          totalEmployees: result.success + result.failed,
          successful: result.success,
          failed: result.failed,
          successRate: result.success + result.failed > 0 ? 
            Math.round((result.success / (result.success + result.failed)) * 100) : 0
        },
        errors: result.errors,
        options
      }
    });

  } catch (error) {
    logger.error('Error in bulk attendance summary generation', LogCategory.API, error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/attendance/summary/bulk?year=2024&month=1
 * Get status of bulk attendance summary generation for a period
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user._id.toString(),
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const year = parseInt(searchParams.get('year') || '');
    const month = parseInt(searchParams.get('month') || '');

    if (!year || !month || month < 1 || month > 12 || year < 2020 || year > 2100) {
      return NextResponse.json(
        { error: 'Valid year (2020-2100) and month (1-12) are required' },
        { status: 400 }
      );
    }

    // Import the model to check summary status
    const EmployeeAttendanceSummary = (await import('@/models/payroll/EmployeeAttendanceSummary')).default;
    const Employee = (await import('@/models/Employee')).default;

    // Get all active employees
    const totalActiveEmployees = await Employee.countDocuments({
      employmentStatus: 'active'
    });

    // Get existing summaries for the period
    const existingSummaries = await EmployeeAttendanceSummary.find({
      year,
      month
    }).populate('employeeId', 'firstName lastName employmentStatus');

    const summariesWithActiveEmployees = existingSummaries.filter(
      summary => summary.employeeId && summary.employeeId.employmentStatus === 'active'
    );

    const finalizedSummaries = summariesWithActiveEmployees.filter(
      summary => summary.isFinalized
    );

    const pendingSummaries = summariesWithActiveEmployees.filter(
      summary => !summary.isFinalized
    );

    const missingSummaries = totalActiveEmployees - summariesWithActiveEmployees.length;

    logger.info('Bulk attendance summary status retrieved', LogCategory.PAYROLL, {
      userId: user._id.toString(),
      period: { year, month },
      totalActiveEmployees,
      existingSummaries: summariesWithActiveEmployees.length,
      finalizedSummaries: finalizedSummaries.length,
      pendingSummaries: pendingSummaries.length,
      missingSummaries
    });

    return NextResponse.json({
      success: true,
      message: 'Bulk attendance summary status retrieved successfully',
      data: {
        period: { year, month },
        status: {
          totalActiveEmployees,
          summariesGenerated: summariesWithActiveEmployees.length,
          summariesFinalized: finalizedSummaries.length,
          summariesPending: pendingSummaries.length,
          summariesMissing: missingSummaries,
          completionRate: totalActiveEmployees > 0 ? 
            Math.round((summariesWithActiveEmployees.length / totalActiveEmployees) * 100) : 0,
          finalizationRate: summariesWithActiveEmployees.length > 0 ? 
            Math.round((finalizedSummaries.length / summariesWithActiveEmployees.length) * 100) : 0
        },
        summaries: {
          finalized: finalizedSummaries.map(summary => ({
            employeeId: summary.employeeId._id,
            employeeName: `${summary.employeeId.firstName} ${summary.employeeId.lastName}`,
            attendanceRate: summary.attendanceRate,
            totalHours: summary.totalHours,
            finalizedAt: summary.finalizedAt,
            finalizedBy: summary.finalizedBy
          })),
          pending: pendingSummaries.map(summary => ({
            employeeId: summary.employeeId._id,
            employeeName: `${summary.employeeId.firstName} ${summary.employeeId.lastName}`,
            attendanceRate: summary.attendanceRate,
            totalHours: summary.totalHours,
            calculatedAt: summary.calculatedAt,
            calculatedBy: summary.calculatedBy
          }))
        }
      }
    });

  } catch (error) {
    logger.error('Error retrieving bulk attendance summary status', LogCategory.API, error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

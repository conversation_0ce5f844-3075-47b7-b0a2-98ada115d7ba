import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { dashboardActivityService, DashboardActivity } from '@/lib/services/dashboard/activity-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

// app/api/dashboard/activities/route.ts
/**
 * GET /api/dashboard/activities
 * Get recent activities for dashboard display
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    // Check permissions - allow all authenticated users to view dashboard activities
    // but restrict sensitive data based on role
    const canViewAllActivities = hasRequiredPermissions(user.role, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_MANAGER,
      UserRole.FINANCE_MANAGER
    ]);
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50); // Max 50 items
    const modules = searchParams.get('modules')?.split(',').filter(Boolean);
    const actions = searchParams.get('actions')?.split(',').filter(Boolean);
    const severity = searchParams.get('severity')?.split(',').filter(Boolean);
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined;
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined;
    // Build filters
    const filters: any = {};
    if (modules && modules.length > 0) {
      filters.modules = modules;
    }
    if (actions && actions.length > 0) {
      filters.actions = actions;
    }
    if (severity && severity.length > 0) {
      filters.severity = severity;
    }
    if (startDate) {
      filters.startDate = startDate;
    }
    if (endDate) {
      filters.endDate = endDate;
    }
    // If user doesn't have permission to view all activities, 
    // filter to only their own activities or department-related activities
    if (!canViewAllActivities) {
      // For regular users, show only activities they can see
      // This could be their own activities or department-related activities
      filters.userId = user._id.toString();
    }
    logger.info('Fetching dashboard activities', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      userRole: user.role,
      limit,
      filters,
      canViewAllActivities
    });
    // Fetch activities
    const activities = await dashboardActivityService.getRecentActivities(limit, filters);
    // Filter sensitive information for non-admin users
    const filteredActivities = activities.map(activity => {
      if (!canViewAllActivities) {
        // Remove sensitive metadata for regular users
        const { metadata, ...safeActivity } = activity;
        return {
          ...safeActivity,
          metadata: metadata ? { 
            // Only include non-sensitive metadata
            module: metadata.module,
            entityType: metadata.entityType
          } : undefined
        };
      }
      return activity;
    });
    logger.info('Successfully fetched dashboard activities', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      count: filteredActivities.length,
      limit
    });
    return NextResponse.json({
      success: true,
      data: {
        activities: filteredActivities,
        count: filteredActivities.length,
        limit,
        hasMore: filteredActivities.length === limit
      }
    });
  } catch (error: any) {
    logger.error('Error fetching dashboard activities', LogCategory.DASHBOARD, error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch dashboard activities',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
/**
 * POST /api/dashboard/activities/stats
 * Get activity statistics for dashboard metrics
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    // Check permissions - only allow managers and admins to view detailed stats
    const canViewStats = hasRequiredPermissions(user.role, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_MANAGER,
      UserRole.FINANCE_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);
    if (!canViewStats) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view activity statistics' },
        { status: 403 }
      );
    }
    // Get request body
    const body = await req.json();
    const period = body.period || 'week';
    // Validate period
    if (!['day', 'week', 'month'].includes(period)) {
      return NextResponse.json(
        { error: 'Invalid period. Must be day, week, or month' },
        { status: 400 }
      );
    }
    logger.info('Fetching activity statistics', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      userRole: user.role,
      period
    });
    // Fetch statistics
    const stats = await dashboardActivityService.getActivityStats(period);
    logger.info('Successfully fetched activity statistics', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      period,
      totalActivities: stats.totalActivities
    });
    return NextResponse.json({
      success: true,
      data: stats
    });
  } catch (error: any) {
    logger.error('Error fetching activity statistics', LogCategory.DASHBOARD, error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch activity statistics',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
/**
 * GET /api/dashboard/activities/filters
 * Get available filter options for activities
 */
export async function PUT(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    logger.info('Fetching activity filter options', LogCategory.DASHBOARD, {
      userId: user._id.toString()
    });
    // Return available filter options
    const filterOptions = {
      modules: [
        { value: 'employees', label: 'Employees' },
        { value: 'payroll', label: 'Payroll' },
        { value: 'accounting', label: 'Accounting' },
        { value: 'income', label: 'Income' },
        { value: 'expenditure', label: 'Expenditure' },
        { value: 'budget', label: 'Budget' },
        { value: 'hr', label: 'Human Resources' },
        { value: 'auth', label: 'Authentication' },
        { value: 'system', label: 'System' }
      ],
      actions: [
        { value: 'CREATE', label: 'Create' },
        { value: 'UPDATE', label: 'Update' },
        { value: 'DELETE', label: 'Delete' },
        { value: 'APPROVE', label: 'Approve' },
        { value: 'REJECT', label: 'Reject' },
        { value: 'SUBMIT', label: 'Submit' },
        { value: 'CANCEL', label: 'Cancel' },
        { value: 'LOGIN', label: 'Login' },
        { value: 'LOGOUT', label: 'Logout' }
      ],
      severity: [
        { value: 'low', label: 'Low' },
        { value: 'medium', label: 'Medium' },
        { value: 'high', label: 'High' },
        { value: 'critical', label: 'Critical' }
      ],
      entityTypes: [
        { value: 'Employee', label: 'Employee' },
        { value: 'Budget', label: 'Budget' },
        { value: 'Income', label: 'Income' },
        { value: 'Expenditure', label: 'Expenditure' },
        { value: 'PayrollRun', label: 'Payroll Run' },
        { value: 'User', label: 'User' },
        { value: 'Department', label: 'Department' }
      ]
    };
    return NextResponse.json({
      success: true,
      data: filterOptions
    });
  } catch (error: any) {
    logger.error('Error fetching activity filter options', LogCategory.DASHBOARD, error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch filter options',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import Employee from '@/models/Employee';

export const runtime = 'nodejs';

// app/api/dashboard/recent-activities/route.ts
// Custom auth system doesn't require authOptions;
/**
 * GET /api/dashboard/recent-activities
 * Get recent activities for the dashboard
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '10');
    // Get recent employee changes
    const recentEmployees = await Employee.find()
      .sort({ updatedAt: -1 })
      .limit(limit)
      .populate('departmentId', 'name')
      .populate('addedBy', 'firstName lastName email')
      .populate('lastModifiedBy', 'firstName lastName email')
      .lean();
    // Format the data for the frontend
    const activities = recentEmployees.map(employee => {
      const isNew = employee.createdAt.toString() === employee.updatedAt.toString();
      const department = employee.departmentId ? (employee.departmentId as any).name : 'No Department';
      // Determine the user who made the change
      const user = isNew
        ? employee.addedBy
        : employee.lastModifiedBy;
      const userName = user
        ? `${(user as any).firstName} ${(user as any).lastName}`
        : 'Unknown User';
      // Determine the action
      const action = isNew
        ? 'added to the system'
        : 'information updated';
      return {
        id: employee._id,
        user: {
          name: userName,
          avatar: '/placeholder-user.jpg',
          initials: `${(user as any)?.firstName?.[0] || 'U'}${(user as any)?.lastName?.[0] || 'U'}`,
        },
        employee: {
          name: `${employee.firstName} ${employee.lastName}`,
          id: employee._id,
        },
        action,
        department,
        time: formatTimeAgo(isNew ? employee.createdAt : employee.updatedAt),
        type: isNew ? 'new-employee' : 'update-employee',
        timestamp: isNew ? employee.createdAt : employee.updatedAt,
      };
    });
    return NextResponse.json(activities);
  } catch (error: unknown) {
    logger.error('Error fetching recent activities', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred while fetching recent activities' },
      { status: 500 }
    );
  }
}
/**
 * Format a date as a time ago string
 * @param date - The date to format
 * @returns A string representing how long ago the date was
 */
function formatTimeAgo(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - new Date(date).getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  if (diffDay > 30) {
    const diffMonth = Math.floor(diffDay / 30);
    return `${diffMonth} month${diffMonth > 1 ? 's' : ''} ago`;
  }
  if (diffDay > 0) {
    return diffDay === 1 ? 'Yesterday' : `${diffDay} days ago`;
  }
  if (diffHour > 0) {
    return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
  }
  if (diffMin > 0) {
    return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
  }
  return 'Just now';
}
// app/api/dashboard/approvals/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { dashboardApprovalService } from '@/lib/services/dashboard/approval-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import {
  ApprovalFilters,
  ApprovalType,
  ApprovalStatus,
  Priority,
  UrgencyLevel
} from '@/lib/types/dashboard-approvals';

export const runtime = 'nodejs';

/**
 * GET /api/dashboard/approvals
 * Get pending approvals for dashboard display
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check permissions - allow managers and above to view approvals
    const canViewApprovals = hasRequiredPermissions(user.role, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.FINANCE_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.HR_SPECIALIST,
      UserRole.FINANCE_OFFICER,
      UserRole.MANAGER
    ]);

    if (!canViewApprovals) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view approvals' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100); // Max 100 items
    
    // Parse filters
    const filters: ApprovalFilters = {};
    
    const types = searchParams.get('types')?.split(',').filter(Boolean);
    if (types && types.length > 0) {
      filters.types = types as ApprovalType[];
    }
    
    const statuses = searchParams.get('statuses')?.split(',').filter(Boolean);
    if (statuses && statuses.length > 0) {
      filters.statuses = statuses as ApprovalStatus[];
    }
    
    const priorities = searchParams.get('priorities')?.split(',').filter(Boolean);
    if (priorities && priorities.length > 0) {
      filters.priorities = priorities as Priority[];
    }
    
    const urgencyLevels = searchParams.get('urgencyLevels')?.split(',').filter(Boolean);
    if (urgencyLevels && urgencyLevels.length > 0) {
      filters.urgencyLevels = urgencyLevels as UrgencyLevel[];
    }
    
    const submittedBy = searchParams.get('submittedBy');
    if (submittedBy) {
      filters.submittedBy = submittedBy;
    }
    
    const currentApprover = searchParams.get('currentApprover');
    if (currentApprover) {
      filters.currentApprover = currentApprover;
    }
    
    const department = searchParams.get('department');
    if (department) {
      filters.department = department;
    }
    
    const search = searchParams.get('search');
    if (search) {
      filters.search = search;
    }
    
    // Amount range
    const minAmount = searchParams.get('minAmount');
    const maxAmount = searchParams.get('maxAmount');
    if (minAmount || maxAmount) {
      filters.amountRange = {};
      if (minAmount) filters.amountRange.min = parseFloat(minAmount);
      if (maxAmount) filters.amountRange.max = parseFloat(maxAmount);
    }
    
    // Date range
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    if (startDate || endDate) {
      filters.dateRange = {};
      if (startDate) filters.dateRange.start = new Date(startDate);
      if (endDate) filters.dateRange.end = new Date(endDate);
    }

    logger.info('Fetching dashboard approvals', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      userRole: user.role,
      limit,
      filters
    });

    // Fetch approvals
    const approvals = await dashboardApprovalService.getPendingApprovals(
      user._id.toString(),
      user.role,
      limit,
      filters
    );

    // Calculate summary statistics
    const summary = {
      total: approvals.length,
      overdue: approvals.filter(a => a.isOverdue).length,
      urgent: approvals.filter(a => a.urgencyLevel === UrgencyLevel.URGENT || a.urgencyLevel === UrgencyLevel.OVERDUE).length,
      highPriority: approvals.filter(a => a.priority === Priority.HIGH || a.priority === Priority.CRITICAL).length,
      byType: approvals.reduce((acc, approval) => {
        acc[approval.type] = (acc[approval.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };

    logger.info('Successfully fetched dashboard approvals', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      count: approvals.length,
      overdue: summary.overdue,
      urgent: summary.urgent
    });

    return NextResponse.json({
      success: true,
      data: {
        approvals,
        summary,
        count: approvals.length,
        limit,
        hasMore: approvals.length === limit
      }
    });

  } catch (error: any) {
    logger.error('Error fetching dashboard approvals', LogCategory.DASHBOARD, error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch dashboard approvals',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/dashboard/approvals/stats
 * Get approval statistics for dashboard metrics
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check permissions - allow managers and admins to view detailed stats
    const canViewStats = hasRequiredPermissions(user.role, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.FINANCE_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.MANAGER
    ]);

    if (!canViewStats) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view approval statistics' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();
    const period = body.period || 'week';

    // Validate period
    if (!['day', 'week', 'month'].includes(period)) {
      return NextResponse.json(
        { error: 'Invalid period. Must be day, week, or month' },
        { status: 400 }
      );
    }

    logger.info('Fetching approval statistics', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      userRole: user.role,
      period
    });

    // Fetch statistics
    const stats = await dashboardApprovalService.getApprovalStats(
      user._id.toString(),
      user.role,
      period
    );

    logger.info('Successfully fetched approval statistics', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      period,
      totalPending: stats.totalPending,
      totalOverdue: stats.totalOverdue
    });

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error: any) {
    logger.error('Error fetching approval statistics', LogCategory.DASHBOARD, error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch approval statistics',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/dashboard/approvals/filters
 * Get available filter options for approvals
 */
export async function PUT(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    logger.info('Fetching approval filter options', LogCategory.DASHBOARD, {
      userId: user._id.toString()
    });

    // Return available filter options
    const filterOptions = {
      types: [
        { value: ApprovalType.LEAVE_REQUEST, label: 'Leave Requests' },
        { value: ApprovalType.BUDGET, label: 'Budgets' },
        { value: ApprovalType.INCOME, label: 'Income' },
        { value: ApprovalType.EXPENDITURE, label: 'Expenditure' },
        { value: ApprovalType.PROCUREMENT_REQUISITION, label: 'Procurement' },
        { value: ApprovalType.PURCHASE_ORDER, label: 'Purchase Orders' },
        { value: ApprovalType.CONTRACT, label: 'Contracts' },
        { value: ApprovalType.VOUCHER, label: 'Vouchers' },
        { value: ApprovalType.EMPLOYEE_REVIEW, label: 'Employee Reviews' },
        { value: ApprovalType.SALARY_ADJUSTMENT, label: 'Salary Adjustments' }
      ],
      statuses: [
        { value: ApprovalStatus.PENDING, label: 'Pending' },
        { value: ApprovalStatus.IN_REVIEW, label: 'In Review' },
        { value: ApprovalStatus.APPROVED, label: 'Approved' },
        { value: ApprovalStatus.REJECTED, label: 'Rejected' },
        { value: ApprovalStatus.CANCELLED, label: 'Cancelled' },
        { value: ApprovalStatus.EXPIRED, label: 'Expired' }
      ],
      priorities: [
        { value: Priority.CRITICAL, label: 'Critical' },
        { value: Priority.URGENT, label: 'Urgent' },
        { value: Priority.HIGH, label: 'High' },
        { value: Priority.MEDIUM, label: 'Medium' },
        { value: Priority.LOW, label: 'Low' }
      ],
      urgencyLevels: [
        { value: UrgencyLevel.OVERDUE, label: 'Overdue' },
        { value: UrgencyLevel.CRITICAL, label: 'Critical' },
        { value: UrgencyLevel.URGENT, label: 'Urgent' },
        { value: UrgencyLevel.NORMAL, label: 'Normal' }
      ],
      amountRanges: [
        { value: { min: 0, max: 100000 }, label: 'Under MWK 100K' },
        { value: { min: 100000, max: 500000 }, label: 'MWK 100K - 500K' },
        { value: { min: 500000, max: 1000000 }, label: 'MWK 500K - 1M' },
        { value: { min: 1000000, max: 5000000 }, label: 'MWK 1M - 5M' },
        { value: { min: 5000000 }, label: 'Over MWK 5M' }
      ]
    };

    return NextResponse.json({
      success: true,
      data: filterOptions
    });

  } catch (error: any) {
    logger.error('Error fetching approval filter options', LogCategory.DASHBOARD, error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch filter options',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

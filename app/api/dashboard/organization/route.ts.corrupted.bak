// app/api/dashboard/organization/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { departmentAnalyticsService } from '@/lib/services/dashboard/department-analytics-service';
import { recruitmentPipelineService } from '@/lib/services/dashboard/recruitment-pipeline-service';
import { performanceMetricsService } from '@/lib/services/dashboard/performance-metrics-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

/**
 * GET /api/dashboard/organization
 * Get organization overview data including department breakdown
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check permissions - allow most authenticated users to view basic organization data
    const canViewOrgData = hasRequiredPermissions(user.role, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER,
      UserRole.EMPLOYEE,
      UserRole.MANAGER
    ]);

    if (!canViewOrgData) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view organization data' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const dataType = searchParams.get('type') || 'all'; // 'departments', 'recruitment', 'performance', or 'all'

    logger.info('Fetching organization overview data', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      userRole: user.role,
      dataType
    });

    const responseData: any = {};

    // Fetch department analytics
    if (dataType === 'all' || dataType === 'departments') {
      try {
        responseData.departments = await departmentAnalyticsService.getDepartmentBreakdown();
      } catch (error) {
        logger.warn('Error fetching department analytics', LogCategory.DASHBOARD, error);
        responseData.departments = null;
      }
    }

    // Fetch recruitment analytics
    if (dataType === 'all' || dataType === 'recruitment') {
      try {
        responseData.recruitment = await recruitmentPipelineService.getRecruitmentAnalytics();
      } catch (error) {
        logger.warn('Error fetching recruitment analytics', LogCategory.DASHBOARD, error);
        responseData.recruitment = null;
      }
    }

    // Fetch performance metrics
    if (dataType === 'all' || dataType === 'performance') {
      try {
        responseData.performance = await performanceMetricsService.getPerformanceMetrics();
      } catch (error) {
        logger.warn('Error fetching performance metrics', LogCategory.DASHBOARD, error);
        responseData.performance = null;
      }
    }

    logger.info('Successfully fetched organization overview data', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      dataType,
      departmentsIncluded: !!responseData.departments,
      recruitmentIncluded: !!responseData.recruitment,
      performanceIncluded: !!responseData.performance
    });

    return NextResponse.json({
      success: true,
      data: responseData,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    logger.error('Error fetching organization overview data', LogCategory.DASHBOARD, error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch organization data',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/dashboard/organization/departments
 * Get detailed department analytics
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check permissions - allow most authenticated users to view department analytics
    const canViewDeptData = hasRequiredPermissions(user.role, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER,
      UserRole.EMPLOYEE,
      UserRole.MANAGER
    ]);

    if (!canViewDeptData) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view department analytics' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();
    const includeHiring = body.includeHiring || false;

    logger.info('Fetching detailed department analytics', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      includeHiring
    });

    // Fetch department breakdown
    const departmentData = await departmentAnalyticsService.getDepartmentBreakdown();
    
    // Optionally include hiring/termination stats
    let hiringStats = null;
    if (includeHiring) {
      try {
        hiringStats = await departmentAnalyticsService.getHiringTerminationStats();
      } catch (error) {
        logger.warn('Error fetching hiring stats', LogCategory.DASHBOARD, error);
      }
    }

    logger.info('Successfully fetched department analytics', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      totalEmployees: departmentData.totalEmployees,
      totalDepartments: departmentData.totalDepartments
    });

    return NextResponse.json({
      success: true,
      data: {
        ...departmentData,
        hiringStats
      }
    });

  } catch (error: any) {
    logger.error('Error fetching department analytics', LogCategory.DASHBOARD, error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch department analytics',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/dashboard/organization/recruitment
 * Get recruitment pipeline data
 */
export async function PUT(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check permissions - recruitment data is more sensitive, limit to HR and management
    const canViewRecruitment = hasRequiredPermissions(user.role, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD,
      UserRole.MANAGER
    ]);

    if (!canViewRecruitment) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view recruitment data' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();
    const dataType = body.type || 'all'; // 'pipeline', 'positions', 'metrics', or 'all'
    const limit = Math.min(body.limit || 10, 50);

    logger.info('Fetching recruitment pipeline data', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      dataType,
      limit
    });

    const responseData: any = {};

    if (dataType === 'all' || dataType === 'analytics') {
      responseData.analytics = await recruitmentPipelineService.getRecruitmentAnalytics();
    }

    if (dataType === 'all' || dataType === 'pipeline') {
      responseData.pipeline = await recruitmentPipelineService.getRecruitmentPipeline();
    }

    if (dataType === 'all' || dataType === 'positions') {
      responseData.positions = await recruitmentPipelineService.getOpenPositions(limit);
    }

    if (dataType === 'all' || dataType === 'metrics') {
      responseData.metrics = await recruitmentPipelineService.getRecruitmentMetrics();
    }

    logger.info('Successfully fetched recruitment data', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      dataType
    });

    return NextResponse.json({
      success: true,
      data: responseData
    });

  } catch (error: any) {
    logger.error('Error fetching recruitment data', LogCategory.DASHBOARD, error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch recruitment data',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/dashboard/organization/performance
 * Get performance metrics
 */
export async function PATCH(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check permissions - performance metrics available to management and HR
    const canViewPerformance = hasRequiredPermissions(user.role, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD,
      UserRole.FINANCE_MANAGER,
      UserRole.MANAGER
    ]);

    if (!canViewPerformance) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view performance metrics' },
        { status: 403 }
      );
    }

    logger.info('Fetching performance metrics', LogCategory.DASHBOARD, {
      userId: user._id.toString()
    });

    // Fetch performance metrics
    const performanceData = await performanceMetricsService.getPerformanceMetrics();

    logger.info('Successfully fetched performance metrics', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      overallScore: performanceData.overallScore.current,
      alertCount: performanceData.alerts.length
    });

    return NextResponse.json({
      success: true,
      data: performanceData
    });

  } catch (error: any) {
    logger.error('Error fetching performance metrics', LogCategory.DASHBOARD, error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch performance metrics',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

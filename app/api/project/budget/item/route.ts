import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { BudgetService } from '@/lib/backend/services/project/BudgetService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

// app/api/project/budget/item/route.ts
// Custom auth system doesn't require authOptions;
// Initialize services
const budgetService = new BudgetService();
/**
 * GET /api/project/budget/item
 * Get budget items with filtering, sorting, and pagination
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const budgetId = searchParams.get('budget');
    const categoryId = searchParams.get('category');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '100', 10);
    const sortField = searchParams.get('sortField') || 'category';
    const sortOrder = searchParams.get('sortOrder') || 'asc';
    // Budget ID is required
    if (!budgetId) {
      return NextResponse.json(
        { error: 'Budget ID is required' },
        { status: 400 }
      );
    }
    // Get budget items
    const result = await budgetService.getBudgetItems(
      budgetId,
      {
        categoryId: categoryId || undefined,
        sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
        page,
        limit
      }
    );
    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/budget/item', LogCategory.PROJECT, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}
/**
 * POST /api/project/budget/item
 * Create a new budget item
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.FINANCE_MANAGER
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get request body
    const body = await request.json();
    // Add created by
    body.createdBy = user.id;
    // Create budget item
    const budgetItem = await budgetService.createBudgetItem(body);
    return NextResponse.json(budgetItem, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in POST /api/project/budget/item', LogCategory.PROJECT, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}
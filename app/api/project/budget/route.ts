import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { BudgetService } from '@/lib/backend/services/project/BudgetService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

// app/api/project/budget/route.ts
// Custom auth system doesn't require authOptions;
// Initialize services
const budgetService = new BudgetService();
/**
 * GET /api/project/budget
 * Get budgets with filtering, sorting, and pagination
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status');
    const projectId = searchParams.get('project');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const sortField = searchParams.get('sortField') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const id = searchParams.get('id');
    // If ID is provided, get a single budget
    if (id) {
      const budget = await budgetService.getBudgetById(id);
      if (!budget) {
        return NextResponse.json(
          { error: 'Budget not found' },
          { status: 404 }
        );
      }
      return NextResponse.json(budget);
    }
    // If projectId is provided, get budgets for a project
    if (projectId) {
      const result = await budgetService.getBudgetsByProject(
        projectId,
        {
          status: status ? (status.includes(',') ? status.split(',') as any : status) : undefined,
          sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
          page,
          limit
        }
      );
      return NextResponse.json(result);
    }
    // Build filter
    // Use a more specific type for MongoDB queries
    interface BudgetFilter {
      status?: string | { $in: string[] };
      startDate?: { $gte: Date };
      endDate?: { $lte: Date };
      [key: string]: any; // Allow additional properties for MongoDB queries
    }
    const filter: BudgetFilter = {};
    // Add status filter
    if (status) {
      (filter as any).status = status.includes(',') ? { $in: status.split(',') } : status;
    }
    // Add date range filter
    if (startDate || endDate) {
      if (startDate) {
        filter.startDate = { $gte: new Date(startDate) };
      }
      if (endDate) {
        filter.endDate = { $lte: new Date(endDate) };
      }
    }
    // Build sort
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'asc' ? 1 : -1
    };
    // Get budgets
    // Cast filter to any to bypass TypeScript's type checking for MongoDB queries
    const result = await budgetService.getBudgets(filter as any, {
      sort,
      page,
      limit,
      populate: ['project', 'items', 'approvedBy', 'createdBy', 'updatedBy']
    });
    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/budget', LogCategory.PROJECT, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}
/**
 * POST /api/project/budget
 * Create a new budget
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.FINANCE_MANAGER
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get request body
    const body = await request.json();
    // Add created by
    body.createdBy = user.id;
    // Create budget
    const budget = await budgetService.createBudget(body);
    return NextResponse.json(budget, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in POST /api/project/budget', LogCategory.PROJECT, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}
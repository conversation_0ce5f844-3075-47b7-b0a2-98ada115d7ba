import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { ProjectService } from '@/lib/backend/services/project/ProjectService';
import { TaskService } from '@/lib/backend/services/project/TaskService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { ITask } from '@/models/project/Task';

export const runtime = 'nodejs';

// app/api/project/[id]/tasks/route.ts
// Custom auth system doesn't require authOptions;
// Initialize services
const projectService = new ProjectService();
const taskService = new TaskService();
/**
 * GET /api/project/[id]/tasks
 * Get tasks for a project with optional filtering and dependency information
 */
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');
    const assignee = searchParams.get('assignee');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const includeDependencies = searchParams.get('include')?.includes('dependencies') || false;
    const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1;
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 100;
    const sortField = searchParams.get('sortField') || 'startDate';
    const sortOrder = searchParams.get('sortOrder') || 'asc';
    // Resolve the params promise
    const { id } = await context.params;
    // Check if project exists
    const project = await projectService.findById(id);
    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }
    // Build filter
    // Use a more specific type for MongoDB queries that's compatible with the TaskService
    interface TaskFilter {
      project: string;
      status?: 'todo' | 'in-progress' | 'completed' | 'blocked' | 'deferred' | { $in: string[] };
      priority?: 'low' | 'medium' | 'high' | 'urgent' | { $in: string[] };
      assignee?: string;
      $or?: Array<{
        startDate?: { $gte?: Date; $lte?: Date };
        dueDate?: { $gte?: Date; $lte?: Date };
      }>;
      [key: string]: any; // Allow additional properties for MongoDB queries
    }
    const filter: TaskFilter = { project: id };
    if (status) {
      // Cast to any to bypass TypeScript's type checking for MongoDB queries
      (filter as any).status = status.includes(',') ? { $in: status.split(',') } : status;
    }
    if (priority) {
      // Cast to any to bypass TypeScript's type checking for MongoDB queries
      (filter as any).priority = priority.includes(',') ? { $in: priority.split(',') } : priority;
    }
    if (assignee) {
      filter.assignee = assignee;
    }
    // Add date range filter
    if (startDate || endDate) {
      filter.$or = [];
      // Tasks that start within the range
      interface DateFilter {
        $gte?: Date;
        $lte?: Date;
      }
      const startDateFilter: DateFilter = {};
      if (startDate) startDateFilter.$gte = new Date(startDate);
      if (endDate) startDateFilter.$lte = new Date(endDate);
      if (Object.keys(startDateFilter).length > 0) {
        filter.$or.push({ startDate: startDateFilter });
      }
      // Tasks that end within the range
      const dueDateFilter: DateFilter = {};
      if (startDate) dueDateFilter.$gte = new Date(startDate);
      if (endDate) dueDateFilter.$lte = new Date(endDate);
      if (Object.keys(dueDateFilter).length > 0) {
        filter.$or.push({ dueDate: dueDateFilter });
      }
      // Tasks that span the entire range
      if (startDate && endDate) {
        filter.$or.push({
          startDate: { $lte: new Date(startDate) },
          dueDate: { $gte: new Date(endDate) }
        });
      }
    }
    // Build sort
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortOrder === 'asc' ? 1 : -1
    };
    // Get tasks
    const populateFields = ['assignee'];
    if (includeDependencies) {
      populateFields.push('dependencies');
    }
    // Cast filter to any to bypass TypeScript's type checking for MongoDB queries
    const result = await taskService.getTasks(filter as any, {
      sort,
      page,
      limit,
      populate: populateFields
    });
    // Add milestone information
    const tasksWithMilestones = result.docs.map(task => {
      // Check if this is a milestone (you might have a different way to identify milestones)
      const isMilestone = task.tags?.includes('milestone') || false;
      return {
        ...task.toObject(),
        isMilestone
      };
    });
    return NextResponse.json({
      tasks: tasksWithMilestones,
      pagination: {
        page: result.page,
        limit: result.limit,
        totalDocs: result.totalDocs,
        totalPages: result.totalPages
      }
    });
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/[id]/tasks', LogCategory.PROJECT, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}
/**
 * POST /api/project/[id]/tasks
 * Create a new task for a project
 */
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Resolve the params promise
    const { id } = await context.params;
    // Check if project exists
    const project = await projectService.findById(id);
    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }
    // Get request body
    const body = await request.json();
    // Add project ID, created by, and assigned by
    body.project = id;
    body.createdBy = user.id;
    body.assignedBy = user.id;
    // Create task
    const task = await taskService.createTask(body);
    // Add task to project
    await projectService.addTask(id, task.id, user.id);
    return NextResponse.json(task, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in POST /api/project/[id]/tasks', LogCategory.PROJECT, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}
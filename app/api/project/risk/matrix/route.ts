import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { RiskService } from '@/lib/backend/services/project/RiskService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

// app/api/project/risk/matrix/route.ts
// Custom auth system doesn't require authOptions;
// Initialize services
const riskService = new RiskService();
/**
 * GET /api/project/risk/matrix
 * Get risk matrix for a project
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get('project');
    // Project ID is required
    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }
    // Generate matrix
    const matrix = await riskService.generateRiskMatrix(projectId);
    return NextResponse.json(matrix);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/risk/matrix', LogCategory.PROJECT, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}
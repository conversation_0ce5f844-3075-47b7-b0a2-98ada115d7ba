import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { ReportingService } from '@/lib/backend/services/project/ReportingService';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

// app/api/project/analytics/portfolio/route.ts
// Custom auth system doesn't require authOptions;
// Initialize services
const reportingService = new ReportingService();
/**
 * GET /api/project/analytics/portfolio
 * Get portfolio dashboard
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROJECT_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status');
    const clientId = searchParams.get('client');
    const managerId = searchParams.get('manager');
    // Build filter
    // Use a more specific type for MongoDB queries
    interface PortfolioFilter {
      status?: string | { $in: string[] };
      client?: string;
      manager?: string;
      [key: string]: any; // Allow additional properties for MongoDB queries
    }
    const filter: PortfolioFilter = {};
    // Add status filter
    if (status) {
      filter.status = status.includes(',') ? { $in: status.split(',') } : status;
    }
    // Add client filter
    if (clientId) {
      filter.client = clientId;
    }
    // Add manager filter
    if (managerId) {
      filter.manager = managerId;
    }
    // Generate portfolio dashboard
    // Cast filter to any to bypass TypeScript's type checking for MongoDB queries
    const dashboard = await reportingService.generatePortfolioDashboard(filter as any);
    return NextResponse.json(dashboard);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/analytics/portfolio', LogCategory.PROJECT, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { dataExportService, ExportEntityType, ExportFormat } from '@/lib/services/accounting/import-export/data-export-service';
import { templateService } from '@/lib/services/accounting/import-export/template-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

export const runtime = 'nodejs';

// app/api/accounting/import-export/export/route.ts
// Custom auth system doesn't require authOptions;
/**
 * POST /api/accounting/import-export/export
 * Export data to a file
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Get request body
    const body = await req.json();
    // Validate required fields
    if (!body.format || !body.entityType) {
      return NextResponse.json(
        { error: 'Missing required fields: format, entityType' },
        { status: 400 }
      );
    }
    // Validate format
    const format = body.format as ExportFormat;
    if (!['csv', 'excel', 'json'].includes(format)) {
      return NextResponse.json(
        { error: 'Invalid format. Must be csv, excel, or json.' },
        { status: 400 }
      );
    }
    // Validate entity type
    const entityType = body.entityType as ExportEntityType;
    if (!['account', 'transaction', 'customer', 'vendor', 'employee', 'budget', 'bank_account', 'bank_transaction'].includes(entityType)) {
      return NextResponse.json(
        { error: 'Invalid entity type.' },
        { status: 400 }
      );
    }
    // Check if using template
    let exportOptions = {
      format,
      entityType,
      filters: body.filters || {},
      fields: body.fields || [],
      includeHeaders: body.includeHeaders !== false,
      dateFormat: body.dateFormat,
      fileName: body.fileName,
      sheetName: body.sheetName
    };
    if (body.templateId) {
      // Get template
      const template = await templateService.getTemplate(body.templateId);
      if (!template) {
        return NextResponse.json(
          { error: 'Template not found' },
          { status: 404 }
        );
      }
      // Apply template options
      exportOptions = {
        ...exportOptions,
        fields: template.fields,
        includeHeaders: template.options?.headerRow !== false,
        dateFormat: template.options?.dateFormat || exportOptions.dateFormat,
        sheetName: template.options?.sheetName || exportOptions.sheetName
      };
    }
    // Export data
    const { buffer, fileName } = await dataExportService.exportData(exportOptions);
    // Log export
    logger.info(`Exported ${entityType} data to ${format}`, LogCategory.EXPORT, {
      userId: user.id,
      entityType,
      format,
      recordCount: buffer.length
    });
    // Return file
    // Cast buffer to BodyInit to satisfy TypeScript
    return new NextResponse(buffer as unknown as BodyInit, {
      headers: {
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Content-Type': getContentType(format)
      }
    });
  } catch (error) {
    logger.error('Error exporting data', LogCategory.EXPORT, error);
    return NextResponse.json(
      { error: 'Failed to export data' },
      { status: 500 }
    );
  }
}
/**
 * Get content type for format
 * @param format - Export format
 * @returns Content type
 */
function getContentType(format: ExportFormat): string {
  switch (format) {
    case 'csv':
      return 'text/csv';
    case 'excel':
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    case 'json':
      return 'application/json';
    default:
      return 'application/octet-stream';
  }
}
// app/api/users/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import User from '@/models/User';
import { connectToDatabase } from '@/lib/backend/database';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';

/**
 * GET handler for users
 */
export async function GET(req: NextRequest) {
  logger.info('Users API request received', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });

  try {
    // Get current user
    const user = await getCurrentUser(req);
    if (!user) {
      // Log the request headers for debugging
      const headers = Object.fromEntries(req.headers.entries());
      logger.warn('Users API: User not authenticated', LogCategory.API, {
        headers: {
          cookie: headers.cookie ? 'Present' : 'Missing',
          authorization: headers.authorization ? 'Present' : 'Missing'
        }
      });

      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'USER_NOT_AUTHENTICATED',
        'User authentication failed',
        'Your session has expired. Please refresh the page and log in again.',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method,
          userAgent: req.headers.get('user-agent') || undefined,
          ip: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || undefined
        },
        401,
        ErrorSeverity.HIGH,
        'Authentication token is missing or invalid',
        [
          'Refresh the page to get a new session',
          'Clear browser cache and cookies if the problem persists',
          'Contact support if you continue to experience authentication issues'
        ],
        [
          {
            label: 'Refresh Page',
            action: 'refresh',
            type: 'button',
            variant: 'primary'
          }
        ]
      );
    }

    // Check if user has permission to view users
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);

    if (!hasPermission) {
      logger.warn('Users API: Unauthorized access attempt', LogCategory.API, {
        userId: user.id,
        role: user.role
      });

      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'INSUFFICIENT_PERMISSIONS',
        'User lacks required permissions to access users',
        'You do not have permission to view user management. Contact your administrator for access.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User role does not have sufficient permissions for user management operations',
        [
          'Contact your system administrator to request user management permissions',
          'Verify you are logged in with the correct account'
        ]
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const limit = parseInt(url.searchParams.get('limit') || '100');
    const skip = parseInt(url.searchParams.get('skip') || '0');
    const search = url.searchParams.get('search') || undefined;

    // Connect to database
    await connectToDatabase();

    // Build query
    const query: Record<string, unknown> = {};

    if (search) {
      const searchRegex = new RegExp(search, 'i');
      query.$or = [
        { email: searchRegex },
        { firstName: searchRegex },
        { lastName: searchRegex }
      ];
    }

    // Get users
    const [users, total] = await Promise.all([
      User.find(query)
        .select('email firstName lastName role status department position lastLogin createdAt')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      User.countDocuments(query)
    ]);

    logger.info(`Retrieved ${users.length} users`, LogCategory.API, {
      total,
      limit,
      skip
    });

    const response = NextResponse.json({
      status: 'success',
      data: {
        users,
        total,
        limit,
        skip
      }
    });

    // Add cache control headers to prevent caching
    response.headers.set('Cache-Control', 'no-store, max-age=0');

    return response;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while retrieving users';
    logger.error('Users API error', LogCategory.API,
      error instanceof Error ? error : new Error(errorMessage),
      { path: req.nextUrl.pathname }
    );

    return errorService.createApiResponse(
      ErrorType.DATABASE,
      'USER_FETCH_FAILED',
      'Failed to retrieve users from database',
      'Unable to load user list. This may be due to a temporary database issue.',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        additionalData: {
          errorMessage,
          timestamp: new Date().toISOString()
        }
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try refreshing the page',
        'Check your internet connection',
        'Contact support if the problem persists'
      ],
      [
        {
          label: 'Retry',
          action: 'retry',
          type: 'retry',
          variant: 'primary'
        },
        {
          label: 'Refresh Page',
          action: 'refresh',
          type: 'button',
          variant: 'secondary'
        }
      ]
    );
  }
}

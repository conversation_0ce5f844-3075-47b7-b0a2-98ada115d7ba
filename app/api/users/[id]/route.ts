import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import User from '@/models/User';
import { connectToDatabase } from '@/lib/backend/database';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole, UserStatus } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';

export const runtime = 'nodejs';

// app/api/users/[id]/route.ts
/**
 * GET handler for individual user
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  logger.info('User detail API request received', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });
  try {
    // Get current user
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'USER_NOT_AUTHENTICATED',
        'User authentication failed',
        'Your session has expired. Please refresh the page and log in again.',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method,
          userAgent: req.headers.get('user-agent') || undefined,
          ip: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || undefined
        },
        401,
        ErrorSeverity.HIGH
      );
    }
    // Check if user has permission to view user details
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);
    const { id } = await params;
    // Allow users to view their own profile
    if (!hasPermission && user.id !== id) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'INSUFFICIENT_PERMISSIONS',
        'User lacks required permissions to view user details',
        'You do not have permission to view this user profile. Contact your administrator for access.',
        {
          userId: user.id,
          userRole: user.role,
          requestedUserId: id,
          endpoint: req.nextUrl.pathname,
          method: req.method
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get user details
    const targetUser = await User.findById(id)
      .select('email firstName lastName role status department position phoneNumber address avatar dateOfBirth dateOfJoining emergencyContact lastLogin createdAt updatedAt statusReason statusChangedAt allowMultipleDevices trustedDevicesOnly singleDeviceLogin')
      .populate('statusChangedBy', 'firstName lastName email');
    if (!targetUser) {
      return errorService.createApiResponse(
        ErrorType.NOT_FOUND,
        'USER_NOT_FOUND',
        'User not found',
        'The requested user could not be found. They may have been deleted or the ID is incorrect.',
        {
          userId: user.id,
          requestedUserId: id,
          endpoint: req.nextUrl.pathname,
          method: req.method
        },
        404,
        ErrorSeverity.MEDIUM,
        'User with the specified ID does not exist in the database',
        [
          'Verify the user ID is correct',
          'Check if the user has been deleted',
          'Refresh the page and try again'
        ]
      );
    }
    logger.info(`Retrieved user details for user ${id}`, LogCategory.API, {
      targetUserId: id,
      requestedBy: user.id
    });
    return NextResponse.json({
      status: 'success',
      data: {
        user: targetUser
      }
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while retrieving user details';
    logger.error('User detail API error', LogCategory.API,
      error instanceof Error ? error : new Error(errorMessage),
      { path: req.nextUrl.pathname }
    );
    return errorService.createApiResponse(
      ErrorType.DATABASE,
      'USER_DETAIL_FETCH_FAILED',
      'Failed to retrieve user details from database',
      'Unable to load user profile. This may be due to a temporary database issue.',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        additionalData: {
          errorMessage,
          timestamp: new Date().toISOString()
        }
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try refreshing the page',
        'Check your internet connection',
        'Contact support if the problem persists'
      ],
      [
        {
          label: 'Retry',
          action: 'retry',
          type: 'retry',
          variant: 'primary'
        },
        {
          label: 'Go Back',
          action: 'back',
          type: 'button',
          variant: 'secondary'
        }
      ]
    );
  }
}
/**
 * PATCH handler for updating user
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  logger.info('User update API request received', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });
  try {
    // Get current user
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'USER_NOT_AUTHENTICATED',
        'User authentication failed',
        'Your session has expired. Please refresh the page and log in again.',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }
    // Check if user has permission to update users
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);
    const { id } = await params;
    // Allow users to update their own basic profile (limited fields)
    const isSelfUpdate = user.id === id;
    if (!hasPermission && !isSelfUpdate) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'INSUFFICIENT_PERMISSIONS',
        'User lacks required permissions to update user details',
        'You do not have permission to update user information. Contact your administrator for access.',
        {
          userId: user.id,
          userRole: user.role,
          requestedUserId: id,
          endpoint: req.nextUrl.pathname,
          method: req.method
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get request body
    const body = await req.json();
    // Define allowed fields for self-update vs admin update
    const selfUpdateFields = ['firstName', 'lastName', 'phoneNumber', 'address', 'avatar'];
    const adminUpdateFields = [
      'firstName', 'lastName', 'email', 'role', 'status', 'department',
      'position', 'phoneNumber', 'address', 'avatar', 'dateOfBirth',
      'dateOfJoining', 'emergencyContact', 'statusReason', 'allowMultipleDevices',
      'trustedDevicesOnly', 'singleDeviceLogin'
    ];
    // Filter allowed fields based on permission level
    const allowedFields = hasPermission ? adminUpdateFields : selfUpdateFields;
    const updateData: Record<string, any> = {};
    // Only include allowed fields in update
    for (const field of allowedFields) {
      if (body[field] !== undefined) {
        updateData[field] = body[field];
      }
    }
    // Add audit fields for admin updates
    if (hasPermission && !isSelfUpdate) {
      if (body.status && body.status !== body.currentStatus) {
        updateData.statusChangedAt = new Date();
        updateData.statusChangedBy = user.id;
      }
    }
    // Validate email uniqueness if email is being updated
    if (updateData.email) {
      const existingUser = await User.findOne({
        email: updateData.email,
        _id: { $ne: id }
      });
      if (existingUser) {
        return errorService.createApiResponse(
          ErrorType.CONFLICT,
          'EMAIL_ALREADY_EXISTS',
          'Email address already in use',
          'This email address is already registered to another user. Please use a different email address.',
          {
            userId: user.id,
            requestedUserId: id,
            email: updateData.email,
            endpoint: req.nextUrl.pathname,
            method: req.method
          },
          409,
          ErrorSeverity.MEDIUM
        );
      }
    }
    // Update user
    const updatedUser = await User.findByIdAndUpdate(
      id,
      updateData,
      {
        new: true,
        runValidators: true,
        select: 'email firstName lastName role status department position phoneNumber address avatar dateOfBirth dateOfJoining emergencyContact lastLogin createdAt updatedAt statusReason statusChangedAt allowMultipleDevices trustedDevicesOnly singleDeviceLogin'
      }
    ).populate('statusChangedBy', 'firstName lastName email');
    if (!updatedUser) {
      return errorService.createApiResponse(
        ErrorType.NOT_FOUND,
        'USER_NOT_FOUND',
        'User not found',
        'The user you are trying to update could not be found.',
        {
          userId: user.id,
          requestedUserId: id,
          endpoint: req.nextUrl.pathname,
          method: req.method
        },
        404,
        ErrorSeverity.MEDIUM
      );
    }
    logger.info(`User ${id} updated successfully`, LogCategory.API, {
      targetUserId: id,
      updatedBy: user.id,
      updatedFields: Object.keys(updateData)
    });
    return NextResponse.json({
      status: 'success',
      data: {
        user: updatedUser
      },
      message: 'User updated successfully'
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while updating user';
    logger.error('User update API error', LogCategory.API,
      error instanceof Error ? error : new Error(errorMessage),
      { path: req.nextUrl.pathname }
    );
    return errorService.createApiResponse(
      ErrorType.DATABASE,
      'USER_UPDATE_FAILED',
      'Failed to update user in database',
      'Unable to update user information. This may be due to a validation error or database issue.',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        additionalData: {
          errorMessage,
          timestamp: new Date().toISOString()
        }
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Check that all required fields are provided',
        'Verify the data format is correct',
        'Try again or contact support if the problem persists'
      ],
      [
        {
          label: 'Retry',
          action: 'retry',
          type: 'retry',
          variant: 'primary'
        }
      ]
    );
  }
}
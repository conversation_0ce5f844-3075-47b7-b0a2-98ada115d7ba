import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { paymentService } from '@/lib/services/banking/payment-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * POST /api/banking/payments/[id]/void
 * Void a payment
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    if (user.role !== 'super_admin' &&
        user.role !== 'system_admin' &&
        user.role !== 'finance_manager') {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.reason) {
      return NextResponse.json(
        { error: 'Missing required field: reason' },
        { status: 400 }
      );
    }

    // Void payment
    const payment = await paymentService.voidPayment(
      id,
      body.reason,
      user.id
    );

    return NextResponse.json(payment);
  } catch (error: unknown) {
    logger.error(`Error voiding payment`, LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to void payment', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

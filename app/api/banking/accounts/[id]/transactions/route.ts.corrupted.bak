// app/api/banking/accounts/[id]/transactions/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions
import { bankingService } from '@/lib/services/banking/banking-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/banking/accounts/[id]/transactions
 * Get transactions for a bank account
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const limit = parseInt(searchParams.get('limit') || '100');
    const skip = parseInt(searchParams.get('skip') || '0');

    // Get transactions
    const transactions = await bankingService.getBankTransactions(
      id,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
      limit,
      skip
    );

    return NextResponse.json(transactions);
  } catch (error) {
    logger.error(`Error getting bank transactions`, LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to get bank transactions' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/banking/accounts/[id]/transactions
 * Create a new transaction for a bank account
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    if (user.role !== 'super_admin' &&
        user.role !== 'system_admin' &&
        user.role !== 'finance_manager' &&
        user.role !== 'accountant') {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.date || !body.description || !body.amount || !body.type) {
      return NextResponse.json(
        { error: 'Missing required fields: date, description, amount, type' },
        { status: 400 }
      );
    }

    // Create transaction
    const transaction = await bankingService.createBankTransaction({
      ...body,
      accountId: id
    });

    return NextResponse.json(transaction, { status: 201 });
  } catch (error: unknown) {
    logger.error(`Error creating bank transaction`, LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to create bank transaction', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

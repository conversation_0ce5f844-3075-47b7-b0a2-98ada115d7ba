// app/api/banking/accounts/[id]/reconciliation/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions
import { reconciliationService } from '@/lib/services/banking/reconciliation-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/banking/accounts/[id]/reconciliation
 * Get reconciliations for a bank account
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = parseInt(searchParams.get('skip') || '0');

    // Get reconciliations
    const reconciliations = await reconciliationService.getReconciliations(
      id,
      limit,
      skip
    );

    return NextResponse.json(reconciliations);
  } catch (error) {
    logger.error(`Error getting reconciliations`, LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to get reconciliations' },
      { status: 500 }
    );
  }
}

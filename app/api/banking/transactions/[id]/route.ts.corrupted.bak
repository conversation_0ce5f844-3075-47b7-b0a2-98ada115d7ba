
// app/api/banking/transactions/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions
import { bankingService } from '@/lib/services/banking/banking-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/banking/transactions/[id]
 * Get a bank transaction by ID
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get transaction
    const transaction = await bankingService.getBankTransactionById(id);

    if (!transaction) {
      return NextResponse.json(
        { error: 'Bank transaction not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(transaction);
  } catch (error) {
    logger.error(`Error getting bank transaction`, LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to get bank transaction' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/banking/transactions/[id]
 * Update a bank transaction
 */
export async function PATCH(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    if (user.role !== 'super_admin' &&
        user.role !== 'system_admin' &&
        user.role !== 'finance_manager') {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get request body
    const body = await req.json();

    // Update transaction
    const transaction = await bankingService.updateBankTransaction(id, body);

    if (!transaction) {
      return NextResponse.json(
        { error: 'Bank transaction not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(transaction);
  } catch (error: unknown) {
    logger.error(`Error updating bank transaction`, LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to update bank transaction', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/banking/transactions/[id]
 * Delete a bank transaction
 */
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    if (user.role !== 'super_admin' &&
        user.role !== 'system_admin') {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Delete transaction
    const result = await bankingService.deleteBankTransaction(id);

    if (!result) {
      return NextResponse.json(
        { error: 'Bank transaction not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    logger.error(`Error deleting bank transaction`, LogCategory.BANKING, error);
    return NextResponse.json(
      { error: 'Failed to delete bank transaction', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

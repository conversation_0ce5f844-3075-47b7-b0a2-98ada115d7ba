// app/api/payroll/payslips/[id]/download/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { payslipGenerationService } from '@/lib/services/payroll/payslip-generation-service';
import PaySlip from '@/models/payroll/PaySlip';
import { errorService } from '@/lib/backend/services/error-service';

/**
 * GET /api/payroll/payslips/[id]/download
 * Download a payslip
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_OFFICER
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: {
            requiredRoles: [
              UserRole.SUPER_ADMIN,
              UserRole.SYSTEM_ADMIN,
              UserRole.FINANCE_DIRECTOR,
              UserRole.FINANCE_MANAGER,
              UserRole.HR_DIRECTOR,
              UserRole.HR_MANAGER,
              UserRole.ACCOUNTANT,
              UserRole.PAYROLL_OFFICER
            ]
          }
        }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Get payslip
    const payslip = await PaySlip.findById(id)
      .populate('employeeId', 'firstName lastName employeeNumber')
      .populate('payrollRunId', 'name payPeriod');

    if (!payslip) {
      return errorService.handlePayrollError(
        'RESOURCE_NOT_FOUND',
        {
          userId: user.id,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: { payslipId: id }
        }
      );
    }

    // Generate PDF payslip with detailed error handling and fallback
    logger.info(`Starting PDF generation for payslip ${id}`, LogCategory.API);

    let pdfBuffer = await payslipGenerationService.generatePayslipPdf(id);

    if (!pdfBuffer) {
      logger.warn(`Main PDF generation failed for payslip ${id}, trying simple fallback`, LogCategory.API);

      // Try simple PDF generation as fallback
      pdfBuffer = await payslipGenerationService.generateSimplePayslipPdf(id);

      if (!pdfBuffer) {
        logger.error(`Both PDF generation methods failed for payslip ${id}`, LogCategory.API);

        return errorService.createApiResponse(
          'SYSTEM',
          'PAYSLIP_GENERATION_ERROR',
          'Failed to generate PDF for payslip',
          'Unable to generate payslip PDF using both main and fallback methods. Please check server logs for details or contact support.',
          {
            userId: user.id,
            endpoint: req.nextUrl.pathname,
            method: req.method,
            additionalData: { payslipId: id }
          },
          500,
          'HIGH'
        );
      } else {
        logger.info(`Fallback PDF generated successfully for payslip ${id}, size: ${pdfBuffer.length} bytes`, LogCategory.API);
      }
    } else {
      logger.info(`Main PDF generated successfully for payslip ${id}, size: ${pdfBuffer.length} bytes`, LogCategory.API);
    }

    // Mark payslip as downloaded
    await payslipGenerationService.markPayslipAsDownloaded(id);

    // Get employee name
    const employee = payslip.employeeId as { firstName: string; lastName: string; employeeNumber: string };
    const employeeName = `${employee.firstName} ${employee.lastName}`;
    const employeeNumber = employee.employeeNumber;

    // Get payroll run
    const payrollRun = payslip.payrollRunId as { payPeriod: { month: number; year: number } };
    const period = `${payrollRun.payPeriod.month}-${payrollRun.payPeriod.year}`;

    // Convert Buffer to Uint8Array for Response
    const uint8Array = new Uint8Array(pdfBuffer);

    // Return PDF file
    return new Response(uint8Array, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="payslip-${employeeNumber}-${employeeName.replace(/\s+/g, '-')}-${period}.pdf"`
      }
    });
  } catch (error: unknown) {
    // Get the ID from params for logging
    const payslipId = await params.then(p => p.id).catch(() => 'unknown');
    logger.error(`Error downloading payslip ${payslipId}`, LogCategory.API, error);
    return errorService.createApiResponse(
      'SYSTEM',
      'PAYSLIP_DOWNLOAD_FAILED',
      error instanceof Error ? error.message : 'Unknown error',
      'Failed to download payslip. Please try again.',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        additionalData: { payslipId }
      },
      500,
      'HIGH'
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import { bulkPayslipService } from '@/lib/services/payroll/bulk-payslip-service';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import PayrollRun from '@/models/payroll/PayrollRun';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

// app/api/payroll/payslips/bulk/route.ts
// Custom auth system doesn't require authOptions;
/**
 * POST handler for bulk payslip operations
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Get session
    const user = await getCurrentUser(request);
    // Check if user is authenticated
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        }
      );
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_OFFICER
    ]);
    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          additionalData: {
            requiredRoles: [
              UserRole.SUPER_ADMIN,
              UserRole.SYSTEM_ADMIN,
              UserRole.FINANCE_DIRECTOR,
              UserRole.FINANCE_MANAGER,
              UserRole.HR_DIRECTOR,
              UserRole.HR_MANAGER,
              UserRole.ACCOUNTANT,
              UserRole.PAYROLL_OFFICER
            ]
          }
        }
      );
    }
    // Parse request body
    const body = await request.json();
    const { payrollRunId, action } = body;
    // Validate request
    if (!payrollRunId) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'MISSING_PAYROLL_RUN_ID',
        'Payroll run ID is required',
        'Please provide a valid payroll run ID.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        400,
        ErrorSeverity.MEDIUM
      );
    }
    if (!action || !['generate', 'email'].includes(action)) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'INVALID_ACTION',
        'Invalid action specified',
        'Action must be either "generate" or "email".',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          additionalData: { providedAction: action }
        },
        400,
        ErrorSeverity.MEDIUM
      );
    }
    // Connect to database
    await connectToDatabase();
    // Check if payroll run exists
    const payrollRun = await PayrollRun.findById(payrollRunId);
    if (!payrollRun) {
      return errorService.handlePayrollError(
        'RESOURCE_NOT_FOUND',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          additionalData: { payrollRunId }
        }
      );
    }
    // Perform action
    if (action === 'generate') {
      // Generate payslips
      const result = await bulkPayslipService.generateAllPayslips(payrollRunId, user.id);
      return NextResponse.json({
        message: `Started generating payslips for payroll run ${payrollRun.name}`,
        count: result.count,
        operationId: result.operationId,
        success: true
      });
    } else if (action === 'email') {
      // Email payslips
      const result = await bulkPayslipService.emailAllPayslips(payrollRunId, user.id);
      return NextResponse.json({
        message: `Started emailing payslips for payroll run ${payrollRun.name}`,
        result,
        operationId: result.operationId,
        success: true
      });
    }
    // This should never happen due to validation above
    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );
  } catch (error: unknown) {
    logger.error('Error in bulk payslip operation', LogCategory.PAYROLL, error);
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'BULK_PAYSLIP_OPERATION_FAILED',
      error instanceof Error ? error.message : 'Unknown error',
      'Failed to perform bulk payslip operation. Please try again.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}
/**
 * GET handler for downloading all payslips as ZIP
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(request: NextRequest) {
  try {
    // Get session
    const user = await getCurrentUser(request);
    // Check if user is authenticated
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        }
      );
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_OFFICER
    ]);
    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        }
      );
    }
    // Get payroll run ID from query
    const url = new URL(request.url);
    const payrollRunId = url.searchParams.get('payrollRunId');
    // Validate request
    if (!payrollRunId) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'MISSING_PAYROLL_RUN_ID',
        'Payroll run ID is required',
        'Please provide a valid payroll run ID in the query parameters.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        400,
        ErrorSeverity.MEDIUM
      );
    }
    // Connect to database
    await connectToDatabase();
    // Check if payroll run exists
    const payrollRun = await PayrollRun.findById(payrollRunId);
    if (!payrollRun) {
      return errorService.handlePayrollError(
        'RESOURCE_NOT_FOUND',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          additionalData: { payrollRunId }
        }
      );
    }
    // Generate ZIP file
    const zipBuffer = await bulkPayslipService.generatePayslipsZip(payrollRunId);
    // Convert Buffer to Uint8Array for Response
    const uint8Array = new Uint8Array(zipBuffer);
    // Return ZIP file directly
    return new Response(uint8Array, {
      headers: {
        'Content-Type': 'application/zip',
        'Content-Disposition': `attachment; filename="payslips-${payrollRun.name.replace(/\s+/g, '-')}.zip"`
      }
    });
  } catch (error: unknown) {
    logger.error('Error downloading payslips ZIP', LogCategory.PAYROLL, error);
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'BULK_PAYSLIP_DOWNLOAD_FAILED',
      error instanceof Error ? error.message : 'Unknown error',
      'Failed to download payslips ZIP file. Please try again.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}
// app/api/payroll/payslips/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import PaySlip from '@/models/payroll/PaySlip';

/**
 * GET /api/payroll/payslips
 * Get payslips with optional filtering by payrollRunId
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const payrollRunId = searchParams.get('payrollRunId') || undefined;
    const status = searchParams.get('status') || undefined;
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Connect to database
    await connectToDatabase();

    // Build query
    const query: Record<string, unknown> = {};

    if (payrollRunId) {
      query.payrollRunId = payrollRunId;
    }

    if (status) {
      query.status = status;
    }

    // Count total documents
    const totalDocs = await PaySlip.countDocuments(query);

    // Get payslips
    const payslips = await PaySlip.find(query)
      .sort({ [sortBy]: sortOrder === 'asc' ? 1 : -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate('employeeId', 'firstName lastName employeeNumber')
      .populate({
        path: 'employeeId',
        populate: {
          path: 'departmentId',
          select: 'name'
        }
      })
      .populate('payrollRunId', 'name payPeriod')
      .populate('createdBy', 'name');

    // Format payslips for frontend
    const formattedPayslips = payslips.map(payslip => {
      const employee = payslip.employeeId as any;
      const department = employee.departmentId as any;
      const payrollRun = payslip.payrollRunId as any;

      return {
        id: payslip._id,
        employeeId: employee._id,
        payrollRunId: payrollRun._id,
        payPeriod: payslip.payPeriod,
        employeeDetails: {
          name: `${employee.firstName} ${employee.lastName}`,
          employeeNumber: employee.employeeNumber,
          department: department ? department.name : 'N/A',
          position: employee.position || 'N/A'
        },
        paymentDetails: {
          grossSalary: payslip.paymentDetails.grossSalary,
          totalDeductions: payslip.paymentDetails.totalDeductions,
          totalTax: payslip.paymentDetails.totalTax,
          netSalary: payslip.paymentDetails.netSalary,
          currency: payslip.paymentDetails.currency
        },
        status: payslip.status,
        emailHistory: payslip.emailHistory || [],
        downloadHistory: payslip.downloadHistory || [],
        createdAt: payslip.createdAt
      };
    });

    // Calculate pagination info
    const totalPages = Math.ceil(totalDocs / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      docs: formattedPayslips,
      totalDocs,
      page,
      totalPages,
      hasNextPage,
      hasPrevPage
    });
  } catch (error: unknown) {
    logger.error('Error getting payslips', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get payslips', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

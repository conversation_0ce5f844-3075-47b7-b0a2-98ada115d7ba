import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { payrollReportingService } from '@/lib/services/payroll/payroll-reporting-service';
import PayrollReport from '@/models/payroll/PayrollReport';

export const runtime = 'nodejs';

// app/api/payroll/reports/[id]/download/route.ts
// Custom auth system doesn't require authOptions;
/**
 * GET /api/payroll/reports/[id]/download
 * Download a payroll report
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_OFFICER
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Resolve the params promise
    const { id } = await params;
    // Get report
    const report = await PayrollReport.findById(id);
    if (!report) {
      return NextResponse.json(
        { error: 'Report not found' },
        { status: 404 }
      );
    }
    // Get file content
    const fileContent = await payrollReportingService.getReportFile(id);
    if (!fileContent) {
      return NextResponse.json(
        { error: 'Report file not found' },
        { status: 404 }
      );
    }
    // Set content type based on format
    let contentType = 'application/octet-stream';
    let fileExtension = '';
    switch (report.format) {
      case 'pdf':
        contentType = 'application/pdf';
        fileExtension = 'pdf';
        break;
      case 'excel':
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        fileExtension = 'xlsx';
        break;
      case 'csv':
        contentType = 'text/csv';
        fileExtension = 'csv';
        break;
    }
    // Update download count
    report.downloadCount = (report.downloadCount || 0) + 1;
    report.lastDownloadedAt = new Date();
    report.lastDownloadedBy = user.id;
    await report.save();
    // Convert Buffer to Uint8Array for Response
    const uint8Array = new Uint8Array(fileContent);
    // Return file
    return new Response(uint8Array, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="payroll-report-${report.reportType}-${report._id}.${fileExtension}"`
      }
    });
  } catch (error: unknown) {
    // Get the ID from params for logging
    const reportId = await params.then(p => p.id).catch(() => 'unknown');
    logger.error(`Error downloading payroll report ${reportId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to download payroll report', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
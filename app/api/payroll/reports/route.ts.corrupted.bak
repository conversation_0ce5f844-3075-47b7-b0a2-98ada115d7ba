// app/api/payroll/reports/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import PayrollReport from '@/models/payroll/PayrollReport';
import { payrollReportingService } from '@/lib/services/payroll/payroll-reporting-service';
import { z } from 'zod';

/**
 * GET /api/payroll/reports
 * Get payroll reports
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const reportType = searchParams.get('reportType') || '';
    const format = searchParams.get('format') || '';
    const fromDate = searchParams.get('fromDate') ? new Date(searchParams.get('fromDate') as string) : null;
    const toDate = searchParams.get('toDate') ? new Date(searchParams.get('toDate') as string) : null;
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build query
    interface ReportQuery {
      reportType?: string;
      format?: string;
      createdAt?: {
        $gte?: Date;
        $lte?: Date;
      };
    }

    const query: ReportQuery = {};

    if (reportType) {
      query.reportType = reportType;
    }

    if (format) {
      query.format = format;
    }

    if (fromDate || toDate) {
      query.createdAt = {};
      if (fromDate) {
        query.createdAt.$gte = fromDate;
      }
      if (toDate) {
        query.createdAt.$lte = toDate;
      }
    }

    // Count total documents
    const totalDocs = await PayrollReport.countDocuments(query);

    // Get reports
    const reports = await PayrollReport.find(query)
      .sort({ [sortBy]: sortOrder === 'asc' ? 1 : -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate('createdBy', 'name')
      .populate('lastDownloadedBy', 'name');

    // Calculate pagination info
    const totalPages = Math.ceil(totalDocs / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      data: {
        docs: reports,
        totalDocs,
        page,
        totalPages,
        hasNextPage,
        hasPrevPage
      }
    });
  } catch (error: unknown) {
    logger.error('Error getting payroll reports', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get payroll reports', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Schema for generating a report
const generateReportSchema = z.object({
  reportType: z.enum([
    'payroll-summary',
    'employee-earnings',
    'department-summary',
    'tax-summary',
    'deduction-summary',
    'allowance-summary',
    'ytd-summary'
  ]),
  name: z.string().min(1, 'Report name is required'),
  description: z.string().optional(),
  parameters: z.object({
    dateRange: z.object({
      startDate: z.string().refine(
        (value) => !isNaN(Date.parse(value)),
        { message: 'Invalid start date' }
      ),
      endDate: z.string().refine(
        (value) => !isNaN(Date.parse(value)),
        { message: 'Invalid end date' }
      )
    }),
    departments: z.array(z.string()).optional(),
    employees: z.array(z.string()).optional(),
    includeInactive: z.boolean().optional(),
    groupBy: z.enum(['employee', 'department', 'month', 'quarter', 'year']).optional(),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).optional()
  }),
  format: z.enum(['pdf', 'excel', 'csv'])
});

/**
 * POST /api/payroll/reports
 * Generate a new payroll report
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate request body
    const validationResult = generateReportSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    const reportData = validationResult.data;

    // Convert date strings to Date objects
    const parameters = {
      ...reportData.parameters,
      dateRange: {
        startDate: new Date(reportData.parameters.dateRange.startDate),
        endDate: new Date(reportData.parameters.dateRange.endDate)
      },
      userId: user.id
    };

    // Generate report
    const report = await payrollReportingService.generateReport({
      reportType: reportData.reportType,
      dateRange: parameters.dateRange,
      departments: parameters.departments,
      employees: parameters.employees,
      format: reportData.format,
      includeInactive: parameters.includeInactive,
      groupBy: parameters.groupBy,
      sortBy: parameters.sortBy,
      sortOrder: parameters.sortOrder,
      userId: user.id
    });

    return NextResponse.json({
      success: true,
      message: 'Report generation started',
      data: report
    });
  } catch (error: unknown) {
    logger.error('Error generating payroll report', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to generate report', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/payroll/reports
 * Delete a payroll report
 */
export async function DELETE(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get report ID from query parameters
    const searchParams = req.nextUrl.searchParams;
    const reportId = searchParams.get('id');

    if (!reportId) {
      return NextResponse.json(
        { error: 'Report ID is required' },
        { status: 400 }
      );
    }

    // Find the report
    const report = await PayrollReport.findById(reportId);

    if (!report) {
      return NextResponse.json(
        { error: 'Report not found' },
        { status: 404 }
      );
    }

    // Delete the report
    await PayrollReport.findByIdAndDelete(reportId);

    logger.info(`Payroll report ${reportId} deleted by user ${user.id}`, LogCategory.API);

    return NextResponse.json({
      success: true,
      message: 'Report deleted successfully'
    });
  } catch (error: unknown) {
    logger.error('Error deleting payroll report', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to delete report', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

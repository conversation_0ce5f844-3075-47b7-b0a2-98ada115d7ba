// app/api/payroll/reports/generate/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { payrollReportingService } from '@/lib/services/payroll/payroll-reporting-service';

export const runtime = 'nodejs';



/**
 * POST /api/payroll/reports/generate
 * Generate a payroll report
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.reportType || !body.dateRange || !body.format) {
      return NextResponse.json(
        { error: 'Missing required fields: reportType, dateRange, format' },
        { status: 400 }
      );
    }

    // Parse date range
    const dateRange = {
      startDate: new Date(body.dateRange.startDate),
      endDate: new Date(body.dateRange.endDate)
    };

    // Generate report
    const report = await payrollReportingService.generateReport({
      reportType: body.reportType,
      dateRange,
      departments: body.departments || [],
      employees: body.employees || [],
      format: body.format,
      includeInactive: body.includeInactive || false,
      groupBy: body.groupBy,
      sortBy: body.sortBy,
      sortOrder: body.sortOrder,
      userId: user.id
    });

    return NextResponse.json({
      success: true,
      message: 'Report generated successfully',
      data: report
    });
  } catch (error: unknown) {
    logger.error('Error generating payroll report', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to generate payroll report', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { taxService } from '@/lib/services/payroll/tax-service';

export const runtime = 'nodejs';

// Custom auth system doesn't require authOptions;
/**
 * POST /api/payroll/tax-brackets/default
 * Create default tax brackets for Malawi
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Create default tax brackets
    const taxBracket = await taxService.createDefaultMalawiTaxBrackets(user.id);
    return NextResponse.json({
      success: true,
      message: 'Default tax brackets created successfully',
      data: taxBracket
    }, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error creating default tax brackets', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to create default tax brackets', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}
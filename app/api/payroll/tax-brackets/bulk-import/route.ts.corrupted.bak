// app/api/payroll/tax-brackets/bulk-import/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { UserRole } from '@/types/user-roles'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import TaxBracket from '@/models/payroll/TaxBracket'
import { connectToDatabase } from '@/lib/backend/database'
import logger, { LogCategory } from '@/lib/backend/utils/logger'
import * as XLSX from 'xlsx'

export const runtime = 'nodejs';

// Define roles that can bulk import tax brackets
const TAX_BRACKET_IMPORT_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.HR_DIRECTOR
]

// Column mapping for tax bracket import
const COLUMN_DISPLAY_MAPPING: Record<string, string> = {
  'Tax Bracket Name': 'name',
  'Minimum Income': 'minIncome',
  'Maximum Income': 'maxIncome',
  'Tax Rate (%)': 'taxRate',
  'Fixed Amount': 'fixedAmount',
  'Country': 'country',
  'Effective Date': 'effectiveDate',
  'End Date': 'endDate',
  'Description': 'description',
  'Is Active': 'isActive'
}

/**
 * POST handler for bulk importing tax brackets
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, TAX_BRACKET_IMPORT_ROLES)

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Connect to database
    await connectToDatabase()

    // Get form data
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Validate file type
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv'
    ]

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload an Excel or CSV file.' },
        { status: 400 }
      )
    }

    // Read file
    const buffer = await file.arrayBuffer()
    const workbook = XLSX.read(buffer, { type: 'buffer' })
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]
    const rows = XLSX.utils.sheet_to_json(worksheet)

    if (rows.length === 0) {
      return NextResponse.json(
        { error: 'The file is empty or has no valid data' },
        { status: 400 }
      )
    }

    logger.info('Starting tax bracket bulk import', LogCategory.IMPORT, {
      userId: user.id,
      fileName: file.name,
      rowCount: rows.length
    })

    // Create a mapping between the columns in the file and our expected fields
    const availableColumns = Object.keys(rows[0])
    const columnMap = new Map<string, string>()

    // First, try to map using the display name mapping
    Object.keys(rows[0]).forEach(key => {
      // Check if this column name is in our display mapping
      if (key in COLUMN_DISPLAY_MAPPING) {
        const mappedField = COLUMN_DISPLAY_MAPPING[key]
        columnMap.set(mappedField, key)
      }
    })

    logger.debug('Column mapping', LogCategory.IMPORT, {
      columnMap: Object.fromEntries(columnMap),
      availableColumns
    })

    const results = {
      success: 0,
      errors: 0,
      warnings: 0,
      details: [] as any[]
    }

    // Process each row
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i]
      let normalizedRow: Record<string, any> = {}

      try {
        // Map columns from the file to our expected fields using the Map
        for (const [field, column] of columnMap.entries()) {
          normalizedRow[field] = row[column]
        }

        // Direct mapping from display names to expected field names for any unmapped fields
        Object.keys(row).forEach(key => {
          // If this is a display name in our mapping and we haven't already mapped it
          if (key in COLUMN_DISPLAY_MAPPING && !(COLUMN_DISPLAY_MAPPING[key] in normalizedRow)) {
            const mappedField = COLUMN_DISPLAY_MAPPING[key]
            normalizedRow[mappedField] = row[key]

            // Trim string values
            if (typeof normalizedRow[mappedField] === 'string') {
              normalizedRow[mappedField] = normalizedRow[mappedField].trim()
            }
          }
        })

        // Validate required fields
        if (!normalizedRow.name) {
          throw new Error('Tax bracket name is required')
        }

        if (normalizedRow.minIncome === undefined || normalizedRow.minIncome === null) {
          throw new Error('Minimum income is required')
        }

        if (normalizedRow.taxRate === undefined || normalizedRow.taxRate === null) {
          throw new Error('Tax rate is required')
        }

        // Convert and validate numeric fields
        const minIncome = parseFloat(normalizedRow.minIncome)
        const maxIncome = normalizedRow.maxIncome ? parseFloat(normalizedRow.maxIncome) : null
        const taxRate = parseFloat(normalizedRow.taxRate)
        const fixedAmount = normalizedRow.fixedAmount ? parseFloat(normalizedRow.fixedAmount) : 0

        if (isNaN(minIncome) || minIncome < 0) {
          throw new Error('Minimum income must be a valid positive number')
        }

        if (maxIncome !== null && (isNaN(maxIncome) || maxIncome <= minIncome)) {
          throw new Error('Maximum income must be greater than minimum income')
        }

        if (isNaN(taxRate) || taxRate < 0 || taxRate > 100) {
          throw new Error('Tax rate must be between 0 and 100')
        }

        if (isNaN(fixedAmount) || fixedAmount < 0) {
          throw new Error('Fixed amount must be a valid positive number')
        }

        // Parse dates
        let effectiveDate = new Date()
        let endDate = null

        if (normalizedRow.effectiveDate) {
          effectiveDate = new Date(normalizedRow.effectiveDate)
          if (isNaN(effectiveDate.getTime())) {
            throw new Error('Invalid effective date format')
          }
        }

        if (normalizedRow.endDate) {
          endDate = new Date(normalizedRow.endDate)
          if (isNaN(endDate.getTime())) {
            throw new Error('Invalid end date format')
          }
          if (endDate <= effectiveDate) {
            throw new Error('End date must be after effective date')
          }
        }

        // Parse boolean fields
        const isActive = normalizedRow.isActive !== undefined 
          ? ['true', 'yes', '1', 1, true].includes(String(normalizedRow.isActive).toLowerCase())
          : true

        // Check for duplicate tax bracket (same name and effective date)
        const existingBracket = await TaxBracket.findOne({
          name: normalizedRow.name.trim(),
          effectiveDate: effectiveDate
        })

        if (existingBracket) {
          throw new Error(`Tax bracket '${normalizedRow.name}' already exists for the effective date`)
        }

        // Create tax bracket data
        const taxBracketData = {
          name: normalizedRow.name.trim(),
          minIncome,
          maxIncome,
          taxRate,
          fixedAmount,
          country: normalizedRow.country?.trim() || 'Malawi',
          effectiveDate,
          endDate,
          description: normalizedRow.description?.trim() || '',
          isActive,
          createdBy: user.id,
          updatedBy: user.id
        }

        // Create the tax bracket
        const taxBracket = new TaxBracket(taxBracketData)
        await taxBracket.save()

        results.success++
        results.details.push({
          row: i + 1,
          status: 'success',
          data: {
            name: taxBracket.name,
            minIncome: taxBracket.minIncome,
            maxIncome: taxBracket.maxIncome,
            taxRate: taxBracket.taxRate,
            effectiveDate: taxBracket.effectiveDate
          }
        })

      } catch (error: unknown) {
        results.errors++
        results.details.push({
          row: i + 1,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          data: normalizedRow
        })

        logger.warn('Tax bracket import row error', LogCategory.IMPORT, {
          userId: user.id,
          row: i + 1,
          error: error instanceof Error ? error.message : 'Unknown error',
          data: normalizedRow
        })
      }
    }

    // Log the final results
    logger.info('Tax bracket bulk import completed', LogCategory.IMPORT, {
      userId: user.id,
      fileName: file.name,
      totalRows: rows.length,
      successCount: results.success,
      errorCount: results.errors,
      warningCount: results.warnings
    })

    return NextResponse.json({
      message: `Import completed. ${results.success} tax brackets imported successfully, ${results.errors} errors.`,
      results
    })

  } catch (error: unknown) {
    console.error('Error in tax bracket bulk import:', error)
    
    logger.error('Error in tax bracket bulk import', LogCategory.IMPORT, {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    })

    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred during import',
        results: { success: 0, errors: 0, warnings: 0, details: [] }
      }, 
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { payrollIntegrationService } from '@/services/payroll/PayrollIntegrationService';

/**
 * GET /api/payroll/cost-center-allocation
 * Generate cost center allocation report for leave costs
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only HR, Payroll, and Finance can access cost center data
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.PAYROLL_MANAGER,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions to access cost center data' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const periodStartParam = searchParams.get('periodStart');
    const periodEndParam = searchParams.get('periodEnd');
    const format = searchParams.get('format'); // 'json' or 'csv'

    // Validate dates
    if (!periodStartParam || !periodEndParam) {
      return NextResponse.json(
        { error: 'Missing required parameters: periodStart, periodEnd' },
        { status: 400 }
      );
    }

    const periodStart = new Date(periodStartParam);
    const periodEnd = new Date(periodEndParam);

    if (isNaN(periodStart.getTime()) || isNaN(periodEnd.getTime())) {
      return NextResponse.json(
        { error: 'Invalid date format. Use YYYY-MM-DD format' },
        { status: 400 }
      );
    }

    if (periodStart > periodEnd) {
      return NextResponse.json(
        { error: 'Period start date must be before end date' },
        { status: 400 }
      );
    }

    // Generate cost center allocation
    const allocations = await payrollIntegrationService.generateCostCenterAllocation(
      periodStart, 
      periodEnd
    );

    // Handle CSV export
    if (format === 'csv') {
      const csvContent = this.generateCostCenterCSV(allocations, periodStartParam, periodEndParam);
      
      return new NextResponse(csvContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="cost-center-allocation-${periodStartParam}-to-${periodEndParam}.csv"`
        }
      });
    }

    // Calculate totals
    const summary = {
      totalCostCenters: allocations.length,
      totalDeductions: allocations.reduce((sum, alloc) => sum + alloc.totalDeductions, 0),
      totalEncashments: allocations.reduce((sum, alloc) => sum + alloc.totalEncashments, 0),
      netAmount: allocations.reduce((sum, alloc) => sum + alloc.netAmount, 0),
      totalEmployees: allocations.reduce((sum, alloc) => sum + alloc.employeeCount, 0)
    };

    return NextResponse.json({
      success: true,
      data: {
        allocations,
        summary
      },
      metadata: {
        reportType: 'cost-center-allocation',
        periodStart: periodStartParam,
        periodEnd: periodEndParam,
        generatedAt: new Date().toISOString(),
        generatedBy: user.id
      }
    });

  } catch (error: unknown) {
    logger.error('Error generating cost center allocation', LogCategory.PAYROLL, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while generating cost center allocation' 
      },
      { status: 500 }
    );
  }

  /**
   * Generate CSV content for cost center allocation
   * @param allocations - Cost center allocations
   * @param periodStart - Period start date
   * @param periodEnd - Period end date
   * @returns CSV string
   */
  private generateCostCenterCSV(
    allocations: any[], 
    periodStart: string, 
    periodEnd: string
  ): string {
    let csv = 'Cost Center Allocation Report\n\n';
    csv += `Period: ${periodStart} to ${periodEnd}\n\n`;
    
    // Summary
    const totalDeductions = allocations.reduce((sum, alloc) => sum + alloc.totalDeductions, 0);
    const totalEncashments = allocations.reduce((sum, alloc) => sum + alloc.totalEncashments, 0);
    const netAmount = allocations.reduce((sum, alloc) => sum + alloc.netAmount, 0);
    const totalEmployees = allocations.reduce((sum, alloc) => sum + alloc.employeeCount, 0);
    
    csv += 'Summary\n';
    csv += 'Total Cost Centers,Total Deductions,Total Encashments,Net Amount,Total Employees\n';
    csv += `${allocations.length},${totalDeductions},${totalEncashments},${netAmount},${totalEmployees}\n\n`;

    // Detailed allocations
    csv += 'Cost Center Allocations\n';
    csv += 'Cost Center,Department ID,Department Name,Total Deductions,Total Encashments,Net Amount,Employee Count\n';
    
    allocations.forEach(alloc => {
      csv += `${alloc.costCenter},${alloc.departmentId},${alloc.departmentName},${alloc.totalDeductions},${alloc.totalEncashments},${alloc.netAmount},${alloc.employeeCount}\n`;
    });

    return csv;
  }
}

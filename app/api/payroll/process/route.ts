import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { unifiedPayrollService } from '@/lib/services/payroll/unified-payroll-service';
import { loanDeductionService } from '@/services/payroll/LoanDeductionService';
import { accountingService } from '@/services/accounting/AccountingService';
import Employee from '@/models/Employee';

export const runtime = 'nodejs';

// Custom auth system doesn't require authOptions;
/**
 * POST /api/payroll/process
 * Process payroll for employees
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.FINANCE_MANAGER
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get request body
    const body = await req.json();
    // Validate required fields
    if (!body.payrollDate || !body.employeeIds) {
      return NextResponse.json(
        { error: 'Missing required fields: payrollDate, employeeIds' },
        { status: 400 }
      );
    }
    // Process payroll for each employee
    const results = [];
    const payrollDate = new Date(body.payrollDate);
    for (const employeeId of body.employeeIds) {
      try {
        // Check if employee exists
        const employee = await Employee.findById(employeeId);
        if (!employee) {
          results.push({
            employeeId,
            success: false,
            error: 'Employee not found'
          });
          continue;
        }
        // Process payroll
        const payroll = await payrollService.processPayroll(employeeId, payrollDate, user.id);
        // Process loan deductions
        const loanDeductions = await loanDeductionService.processLoanDeductions(
          employeeId,
          payrollDate,
          user.id
        );
        // Update payroll with loan deductions
        if (loanDeductions.totalDeduction > 0) {
          payroll.deductions.push({
            type: 'loan_repayment',
            amount: loanDeductions.totalDeduction,
            description: 'Loan repayments',
            details: loanDeductions.deductions
          });
          payroll.totalDeductions += loanDeductions.totalDeduction;
          payroll.netPay -= loanDeductions.totalDeduction;
          // Save updated payroll
          await payroll.save();
          // Create accounting entries for loan deductions
          await loanDeductionService.createAccountingEntries(
            employeeId,
            payroll._id.toString(),
            loanDeductions.deductions,
            user.id
          );
        }
        // Create accounting entries for payroll
        await accountingService.createJournalEntry({
          date: payrollDate,
          reference: `PAYROLL-${payroll._id}`,
          description: `Payroll for ${employee.firstName} ${employee.lastName}`,
          entries: [
            {
              accountId: process.env.SALARY_EXPENSE_ACCOUNT_ID || '60001', // Salary Expense
              debit: payroll.grossPay,
              credit: 0,
              description: 'Gross salary',
              metadata: {
                employeeId,
                payrollId: payroll._id
              }
            },
            {
              accountId: process.env.SALARY_PAYABLE_ACCOUNT_ID || '20100', // Salary Payable
              debit: 0,
              credit: payroll.netPay,
              description: 'Net salary payable',
              metadata: {
                employeeId,
                payrollId: payroll._id
              }
            },
            {
              accountId: process.env.TAX_PAYABLE_ACCOUNT_ID || '20200', // Tax Payable
              debit: 0,
              credit: payroll.taxAmount,
              description: 'Tax deductions',
              metadata: {
                employeeId,
                payrollId: payroll._id,
                deductionType: 'tax'
              }
            }
          ],
          createdBy: user.id,
          status: 'posted',
          postingDate: payrollDate
        });
        results.push({
          employeeId,
          success: true,
          payrollId: payroll._id,
          grossPay: payroll.grossPay,
          netPay: payroll.netPay,
          deductions: payroll.totalDeductions,
          loanDeductions: loanDeductions.totalDeduction
        });
      } catch (error) {
        logger.error('Error processing payroll for employee', LogCategory.PAYROLL, {
          employeeId,
          error
        });
        results.push({
          employeeId,
          success: false,
          error: error instanceof Error ? error.message : 'An unknown error occurred'
        });
      }
    }
    return NextResponse.json({
      success: true,
      message: 'Payroll processing completed',
      results
    });
  } catch (error: unknown) {
    logger.error('Error processing payroll', LogCategory.PAYROLL, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
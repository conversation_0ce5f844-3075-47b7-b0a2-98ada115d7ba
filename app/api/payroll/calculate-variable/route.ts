import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { variableSalaryService } from '@/lib/services/payroll/variable-salary-service';
import Employee from '@/models/Employee';
import { errorService } from '@/lib/backend/services/error-service';
import { z } from 'zod';

export const runtime = 'nodejs';

// Schema for variable salary calculation request
const variableSalaryCalculationSchema = z.object({
  employeeId: z.string().min(1, 'Employee ID is required'),
  payPeriod: z.object({
    month: z.number().min(1).max(12),
    year: z.number().min(2020).max(2100)
  }),
  options: z.object({
    generateSummaries: z.boolean().optional().default(false),
    attendanceOptions: z.object({
      includeWeekends: z.boolean().optional(),
      includeHolidays: z.boolean().optional(),
      workingHoursPerDay: z.number().optional(),
      overtimeThreshold: z.number().optional(),
      lateThresholdMinutes: z.number().optional()
    }).optional(),
    taskOptions: z.object({
      includeInProgressTasks: z.boolean().optional(),
      qualityScoreThreshold: z.number().optional(),
      onTimeThresholdDays: z.number().optional(),
      performanceBonusThreshold: z.number().optional()
    }).optional(),
    skipRules: z.boolean().optional().default(false),
    overrides: z.object({
      basicSalary: z.number().optional(),
      hourlyRate: z.number().optional(),
      bonusAmount: z.number().optional(),
      deductionAmount: z.number().optional()
    }).optional()
  }).optional().default({})
});

// Schema for bulk calculation request
const bulkCalculationSchema = z.object({
  employeeIds: z.array(z.string()).min(1, 'At least one employee ID is required'),
  payPeriod: z.object({
    month: z.number().min(1).max(12),
    year: z.number().min(2020).max(2100)
  }),
  options: z.object({
    generateSummaries: z.boolean().optional().default(true),
    attendanceOptions: z.object({
      includeWeekends: z.boolean().optional(),
      includeHolidays: z.boolean().optional(),
      workingHoursPerDay: z.number().optional(),
      overtimeThreshold: z.number().optional(),
      lateThresholdMinutes: z.number().optional()
    }).optional(),
    taskOptions: z.object({
      includeInProgressTasks: z.boolean().optional(),
      qualityScoreThreshold: z.number().optional(),
      onTimeThresholdDays: z.number().optional(),
      performanceBonusThreshold: z.number().optional()
    }).optional(),
    skipRules: z.boolean().optional().default(false)
  }).optional().default({})
});

/**
 * POST /api/payroll/calculate-variable
 * Calculate variable salary for employee(s) based on attendance and task data
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user._id.toString(),
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Determine if this is a bulk calculation
    const isBulkCalculation = Array.isArray(body.employeeIds);

    if (isBulkCalculation) {
      // Validate bulk calculation request
      const validationResult = bulkCalculationSchema.safeParse(body);
      if (!validationResult.success) {
        return NextResponse.json(
          { 
            error: 'Validation failed',
            details: validationResult.error.errors
          },
          { status: 400 }
        );
      }

      const { employeeIds, payPeriod, options } = validationResult.data;

      // Validate all employees exist
      const employees = await Employee.find({
        _id: { $in: employeeIds }
      }).select('_id firstName lastName employmentStatus');

      const foundEmployeeIds = employees.map(emp => emp._id.toString());
      const missingEmployeeIds = employeeIds.filter(id => !foundEmployeeIds.includes(id));

      if (missingEmployeeIds.length > 0) {
        return NextResponse.json(
          { 
            error: 'Some employees not found',
            missingEmployeeIds
          },
          { status: 404 }
        );
      }

      // Check for inactive employees
      const inactiveEmployees = employees.filter(emp => emp.employmentStatus !== 'active');
      if (inactiveEmployees.length > 0) {
        logger.warn('Calculating salary for inactive employees', LogCategory.PAYROLL, {
          inactiveEmployees: inactiveEmployees.map(emp => ({
            id: emp._id,
            name: `${emp.firstName} ${emp.lastName}`,
            status: emp.employmentStatus
          }))
        });
      }

      // Calculate salaries for all employees
      const result = await variableSalaryService.calculateBulkSalaries(
        employeeIds,
        payPeriod,
        user._id.toString(),
        options
      );

      logger.info('Bulk variable salary calculation completed', LogCategory.PAYROLL, {
        userId: user._id.toString(),
        employeeCount: employeeIds.length,
        success: result.success,
        failed: result.failed,
        totalGrossSalary: result.totalGrossSalary,
        totalNetSalary: result.totalNetSalary
      });

      return NextResponse.json({
        success: true,
        message: `Bulk salary calculation completed. ${result.success} successful, ${result.failed} failed.`,
        data: {
          summary: {
            totalEmployees: employeeIds.length,
            successful: result.success,
            failed: result.failed,
            totalGrossSalary: result.totalGrossSalary,
            totalNetSalary: result.totalNetSalary
          },
          results: result.results,
          errors: result.errors
        }
      });

    } else {
      // Single employee calculation
      const validationResult = variableSalaryCalculationSchema.safeParse(body);
      if (!validationResult.success) {
        return NextResponse.json(
          { 
            error: 'Validation failed',
            details: validationResult.error.errors
          },
          { status: 400 }
        );
      }

      const { employeeId, payPeriod, options } = validationResult.data;

      // Check if employee exists
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        return NextResponse.json(
          { error: 'Employee not found' },
          { status: 404 }
        );
      }

      // Calculate salary using variable salary service
      const result = await variableSalaryService.calculateEmployeeSalary(
        employeeId,
        payPeriod,
        user._id.toString(),
        options
      );

      // Check for calculation errors
      if (result.error) {
        return errorService.handlePayrollError(
          'SALARY_CALCULATION_ERROR',
          {
            userId: user._id.toString(),
            endpoint: req.nextUrl.pathname,
            method: req.method,
            additionalData: {
              employeeId,
              employeeName: `${employee.firstName} ${employee.lastName}`,
              payPeriod,
              error: result.error,
              message: result.message
            }
          }
        );
      }

      logger.info('Variable salary calculation completed', LogCategory.PAYROLL, {
        userId: user._id.toString(),
        employeeId,
        employeeName: `${employee.firstName} ${employee.lastName}`,
        calculationType: result.calculationType,
        grossSalary: result.grossSalary,
        netSalary: result.netSalary
      });

      return NextResponse.json({
        success: true,
        message: 'Variable salary calculation completed successfully',
        data: result
      });
    }

  } catch (error) {
    logger.error('Error in variable salary calculation API', LogCategory.API, error);
    
    return errorService.handlePayrollError(
      'CALCULATION_ERROR',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    );
  }
}

/**
 * GET /api/payroll/calculate-variable?action=all&month=1&year=2024
 * Calculate variable salaries for all active employees
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user._id.toString(),
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const action = searchParams.get('action');
    const month = parseInt(searchParams.get('month') || '');
    const year = parseInt(searchParams.get('year') || '');

    if (action !== 'all') {
      return NextResponse.json(
        { error: 'Invalid action. Only "all" is supported for GET requests.' },
        { status: 400 }
      );
    }

    if (!month || !year || month < 1 || month > 12 || year < 2020 || year > 2100) {
      return NextResponse.json(
        { error: 'Valid month (1-12) and year (2020-2100) are required' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Calculate salaries for all active employees
    const result = await variableSalaryService.calculateAllEmployeeSalaries(
      { month, year },
      user._id.toString(),
      { generateSummaries: true }
    );

    logger.info('All employees variable salary calculation completed', LogCategory.PAYROLL, {
      userId: user._id.toString(),
      month,
      year,
      success: result.success,
      failed: result.failed,
      totalGrossSalary: result.totalGrossSalary,
      totalNetSalary: result.totalNetSalary
    });

    return NextResponse.json({
      success: true,
      message: `All employees salary calculation completed. ${result.success} successful, ${result.failed} failed.`,
      data: {
        summary: {
          totalEmployees: result.success + result.failed,
          successful: result.success,
          failed: result.failed,
          totalGrossSalary: result.totalGrossSalary,
          totalNetSalary: result.totalNetSalary
        },
        results: result.results,
        errors: result.errors
      }
    });

  } catch (error) {
    logger.error('Error in all employees variable salary calculation API', LogCategory.API, error);
    
    return errorService.handlePayrollError(
      'CALCULATION_ERROR',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    );
  }
}

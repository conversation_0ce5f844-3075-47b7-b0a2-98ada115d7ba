// app/api/payroll/compensation/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { connectToDatabase } from '@/lib/backend/database'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import { UserRole } from '@/types/user-roles'
import logger, { LogCategory } from '@/lib/backend/utils/logger'
import Compensation from '@/models/payroll/Compensation'

export const runtime = 'nodejs'

// Required roles for compensation access
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.FINANCE_DIRECTOR,
  UserRole.PAYROLL_MANAGER,
  UserRole.FINANCE_MANAGER,
  UserRole.HR_MANAGER,
  UserRole.ACCOUNTANT
]

/**
 * GET /api/payroll/compensation
 * Get compensation records with pagination and filtering
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES)
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    await connectToDatabase()

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const sort = searchParams.get('sort') || '-createdAt'
    const status = searchParams.get('status')
    const compensationType = searchParams.get('compensationType')
    const employeeId = searchParams.get('employeeId')

    // Build filter
    const filter: Record<string, any> = {}

    // Filter by compensation type
    if (compensationType) {
      filter.compensationType = compensationType
    }

    // Filter by status
    if (status) {
      filter.status = status
    }

    // Filter by employee
    if (employeeId) {
      filter.employeeId = employeeId
    }

    // Parse sort parameter
    const sortField = sort.startsWith('-') ? sort.substring(1) : sort
    const sortOrder = sort.startsWith('-') ? -1 : 1
    const sortObj = { [sortField]: sortOrder }

    // Calculate skip value
    const skip = (page - 1) * limit

    // Get total count
    const totalDocs = await Compensation.countDocuments(filter)

    // Get compensation records
    const records = await Compensation.find(filter)
      .populate({
        path: 'employeeId',
        select: 'firstName lastName email departmentId',
        populate: {
          path: 'departmentId',
          select: 'name'
        }
      })
      .populate('createdBy', 'firstName lastName')
      .sort(sortObj)
      .skip(skip)
      .limit(limit)
      .lean()

    // Transform records to match frontend interface
    const transformedRecords = records.map(record => {
      return {
        _id: record._id,
        employeeId: {
          _id: record.employeeId?._id,
          firstName: record.employeeId?.firstName || 'Unknown',
          lastName: record.employeeId?.lastName || 'Employee',
          email: record.employeeId?.email || '<EMAIL>',
          department: record.employeeId?.departmentId ? { name: record.employeeId.departmentId.name } : undefined
        },
        compensationType: record.compensationType,
        amount: record.amount,
        effectiveDate: record.effectiveDate,
        description: record.description,
        notes: record.notes || '',
        currency: record.currency,
        status: record.status,
        isRecurring: record.isRecurring,
        frequency: record.frequency,
        endDate: record.endDate,
        taxable: record.taxable,
        pensionable: record.pensionable,
        createdAt: record.createdAt,
        createdBy: {
          firstName: record.createdBy?.firstName || '',
          lastName: record.createdBy?.lastName || ''
        }
      }
    })

    // Calculate pagination info
    const totalPages = Math.ceil(totalDocs / limit)
    const hasNextPage = page < totalPages
    const hasPrevPage = page > 1

    return NextResponse.json({
      success: true,
      data: transformedRecords,
      pagination: {
        totalDocs,
        page,
        totalPages,
        hasNextPage,
        hasPrevPage,
        limit
      }
    })

  } catch (error) {
    logger.error('Error fetching compensation records', LogCategory.API, error)
    return NextResponse.json(
      { error: 'Failed to fetch compensation records', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/payroll/compensation
 * Create a new compensation record
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES.slice(0, 6)) // More restrictive for creation
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    await connectToDatabase()

    const body = await request.json()
    const {
      employeeId,
      compensationType,
      amount,
      effectiveDate,
      description,
      notes,
      currency = 'MWK',
      isRecurring = false,
      frequency = 'one_time',
      endDate,
      taxable = true,
      pensionable = false
    } = body

    // Validate required fields
    if (!employeeId || !compensationType || !amount || !effectiveDate) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Create compensation record
    const compensationData = {
      employeeId,
      compensationType,
      amount: Math.abs(amount),
      effectiveDate: new Date(effectiveDate),
      description: description || `${compensationType.replace('_', ' ')} compensation`,
      notes,
      currency,
      isRecurring,
      frequency,
      endDate: endDate ? new Date(endDate) : undefined,
      taxable,
      pensionable,
      status: 'pending',
      createdBy: user.id
    }

    const record = await Compensation.create(compensationData)

    logger.info('Compensation record created', LogCategory.PAYROLL, {
      recordId: record._id,
      employeeId,
      compensationType,
      amount,
      userId: user.id
    })

    return NextResponse.json({
      success: true,
      message: 'Compensation record created successfully',
      data: record
    })

  } catch (error) {
    logger.error('Error creating compensation record', LogCategory.API, error)
    return NextResponse.json(
      { error: 'Failed to create compensation record', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

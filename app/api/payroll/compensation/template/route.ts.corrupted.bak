// app/api/payroll/compensation/template/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { connectToDatabase } from '@/lib/backend/database'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import { UserRole } from '@/types/user-roles'
import { Employee } from '@/models/Employee'
import Allowance from '@/models/payroll/Allowance'
import * as XLSX from 'xlsx'

export const runtime = 'nodejs'

// Required roles for template download
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.FINANCE_DIRECTOR,
  UserRole.PAYROLL_MANAGER,
  UserRole.FINANCE_MANAGER
]

/**
 * GET /api/payroll/compensation/template
 * Download Excel template for compensation bulk import (legacy - with sample data)
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES)
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    await connectToDatabase()

    // Get allowances for compensation types
    const allowances = await Allowance.find({ isActive: true })
      .select('name code isTaxable isPensionable')
      .lean()

    // Create compensation template data with sample data
    const templateData = [
      // Header row
      [
        'Employee ID',
        'Employee Email',
        'Employee Name',
        'Department',
        'Compensation Type',
        'Amount',
        'Effective Date',
        'Description',
        'Notes',
        'Currency',
        'Payroll Run ID',
        'Is Recurring',
        'Frequency',
        'End Date',
        'Taxable',
        'Pensionable'
      ],
      // Example data rows
      [
        '507f1f77bcf86cd799439011',
        '<EMAIL>',
        'John Doe',
        'Finance Department',
        'performance_bonus',
        '50000',
        '2024-02-01',
        'Q4 2023 Performance Bonus',
        'Excellent performance rating',
        'MWK',
        '',
        'false',
        'one_time',
        '',
        'true',
        'false'
      ],
      [
        '',
        '<EMAIL>',
        'Jane Smith',
        'HR Department',
        'holiday_bonus',
        '25000',
        '2024-02-01',
        'Christmas Holiday Bonus',
        'Annual holiday bonus',
        'MWK',
        '',
        'false',
        'one_time',
        '',
        'true',
        'false'
      ],
      [
        '',
        '<EMAIL>',
        'Mike Wilson',
        'ICT Department',
        'overtime',
        '15000',
        '2024-02-01',
        'January Overtime Payment',
        'Weekend work compensation',
        'MWK',
        '',
        'false',
        'one_time',
        '',
        'true',
        'true'
      ],
      [
        '',
        '<EMAIL>',
        'Sarah Johnson',
        'Compliance Department',
        'special_allowance',
        '20000',
        '2024-02-01',
        'Project Completion Allowance',
        'Special project completion bonus',
        'MWK',
        '',
        'true',
        'monthly',
        '2024-06-30',
        'true',
        'false'
      ],
      [
        '',
        '<EMAIL>',
        'David Brown',
        'Finance Department',
        'retroactive_adjustment',
        '30000',
        '2024-01-01',
        'Salary Adjustment Backpay',
        'Retroactive salary increase',
        'MWK',
        '',
        'false',
        'one_time',
        '',
        'true',
        'true'
      ],
      [
        '',
        '<EMAIL>',
        'Mary White',
        'Administration',
        'one_time_deduction',
        '5000',
        '2024-02-01',
        'Uniform Cost Deduction',
        'Deduction for uniform purchase',
        'MWK',
        '',
        'false',
        'one_time',
        '',
        'false',
        'false'
      ]
    ]

    // Create a new workbook
    const wb = XLSX.utils.book_new()

    // Create worksheet from the data
    const ws = XLSX.utils.aoa_to_sheet(templateData)

    // Set column widths for better readability
    const colWidths = [
      { wch: 25 }, // Employee ID
      { wch: 30 }, // Employee Email
      { wch: 20 }, // Employee Name
      { wch: 20 }, // Department
      { wch: 20 }, // Compensation Type
      { wch: 15 }, // Amount
      { wch: 15 }, // Effective Date
      { wch: 35 }, // Description
      { wch: 40 }, // Notes
      { wch: 10 }, // Currency
      { wch: 25 }, // Payroll Run ID
      { wch: 12 }, // Is Recurring
      { wch: 12 }, // Frequency
      { wch: 15 }, // End Date
      { wch: 10 }, // Taxable
      { wch: 12 }  // Pensionable
    ]

    ws['!cols'] = colWidths

    // Add the worksheet to the workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Compensation Data')

    // Create instructions sheet
    const instructionsData = [
      ['Compensation Bulk Import Instructions'],
      [''],
      ['Required Fields:'],
      ['• Employee ID OR Employee Email (at least one must be provided)'],
      ['• Compensation Type (performance_bonus, holiday_bonus, overtime, special_allowance, one_time_deduction, retroactive_adjustment)'],
      ['• Amount (the compensation amount in MWK)'],
      ['• Effective Date (format: YYYY-MM-DD, e.g., 2024-02-01)'],
      [''],
      ['Optional Fields:'],
      ['• Employee Name (for reference only)'],
      ['• Department (for reference only)'],
      ['• Description (explanation of the compensation)'],
      ['• Notes (additional comments)'],
      ['• Currency (defaults to MWK if not provided)'],
      ['• Payroll Run ID (to associate with specific payroll run)'],
      ['• Is Recurring (true/false, defaults to false)'],
      ['• Frequency (one_time, monthly, quarterly, annually - defaults to one_time)'],
      ['• End Date (for recurring compensations, format: YYYY-MM-DD)'],
      ['• Taxable (true/false, defaults to true)'],
      ['• Pensionable (true/false, defaults to false)'],
      [''],
      ['Compensation Types:'],
      ['• performance_bonus: Performance-based bonus payments'],
      ['• holiday_bonus: Holiday or seasonal bonus payments'],
      ['• overtime: Overtime work compensation'],
      ['• special_allowance: Special project or role allowances'],
      ['• one_time_deduction: One-time deductions (uniform, loans, etc.)'],
      ['• retroactive_adjustment: Backpay or retroactive salary adjustments'],
      [''],
      ['Important Notes:'],
      ['• Each compensation record will be created as pending status'],
      ['• Deductions (one_time_deduction) will be processed as negative amounts'],
      ['• Recurring compensations require an end date'],
      ['• All amounts should be in MWK unless otherwise specified'],
      ['• Effective dates should be current or future dates'],
      ['• Taxable compensations will be subject to income tax calculations'],
      ['• Pensionable compensations will be included in pension calculations'],
      [''],
      ['Example Data:'],
      ['The template includes example data showing different scenarios:'],
      ['• Performance bonus for excellent work'],
      ['• Holiday bonus for seasonal payments'],
      ['• Overtime compensation for extra work'],
      ['• Special allowance for project work'],
      ['• Retroactive adjustment for backpay'],
      ['• One-time deduction for uniform costs'],
      [''],
      ['Tips:'],
      ['• You can use either Employee ID or Email to identify employees'],
      ['• If both ID and Email are provided, ID takes precedence'],
      ['• Department and Employee Name are for reference and validation'],
      ['• Remove example data before importing your actual data'],
      ['• Test with a small batch first before importing large datasets'],
      ['• Ensure all dates are in YYYY-MM-DD format'],
      ['• Use consistent compensation types from the allowed list']
    ]

    const instructionsWs = XLSX.utils.aoa_to_sheet(instructionsData)

    // Set column width for instructions
    instructionsWs['!cols'] = [{ wch: 80 }]

    // Add instructions sheet
    XLSX.utils.book_append_sheet(wb, instructionsWs, 'Instructions')

    // Generate Excel file buffer
    const excelBuffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' })

    // Create response with Excel file
    return new NextResponse(excelBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="compensation_bulk_import_template.xlsx"'
      }
    })

  } catch (error) {
    console.error('Error generating compensation template:', error)
    return NextResponse.json(
      { error: 'Failed to generate template', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/payroll/compensation/template
 * Generate dynamic Excel template with selected employees
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES)
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    await connectToDatabase()

    // Get request body
    const body = await request.json()
    const { employeeIds } = body

    if (!employeeIds || !Array.isArray(employeeIds) || employeeIds.length === 0) {
      return NextResponse.json({ error: 'Employee IDs are required' }, { status: 400 })
    }

    // Get selected employees
    const employees = await Employee.find({
      _id: { $in: employeeIds },
      employmentStatus: 'active'
    })
      .select('employeeId firstName lastName email position departmentId')
      .populate('departmentId', 'name')
      .sort({ lastName: 1, firstName: 1 })
      .lean()

    if (employees.length === 0) {
      return NextResponse.json({ error: 'No valid employees found' }, { status: 400 })
    }

    // Get allowances for compensation types
    const allowances = await Allowance.find({ isActive: true })
      .select('name code isTaxable isPensionable')
      .lean()

    // Create template data with selected employees
    const templateData = [
      // Header row
      [
        'Employee ID',
        'Employee Email',
        'Employee Name',
        'Department',
        'Compensation Type',
        'Amount',
        'Effective Date',
        'Description',
        'Notes',
        'Currency',
        'Payroll Run ID',
        'Is Recurring',
        'Frequency',
        'End Date',
        'Taxable',
        'Pensionable'
      ]
    ]

    // Add rows for each selected employee with ALL available compensation types
    employees.forEach(employee => {
      // Add a row for each allowance/compensation type for this employee
      allowances.forEach(allowance => {
        templateData.push([
          employee.employeeId || '',
          employee.email || '',
          `${employee.firstName} ${employee.lastName}`,
          employee.departmentId?.name || '',
          allowance.code, // Use allowance code as compensation type
          '', // Amount - to be filled by user
          new Date().toISOString().split('T')[0], // Today's date as default
          allowance.name, // Use allowance name as description
          '', // Notes - to be filled by user
          'MWK',
          '', // Payroll Run ID - optional
          'false',
          'one_time',
          '', // End Date - optional
          allowance.isTaxable.toString(),
          allowance.isPensionable.toString()
        ])
      })

      // Also add standard compensation types if no allowances found
      if (allowances.length === 0) {
        const standardTypes = [
          { code: 'performance_bonus', name: 'Performance Bonus', taxable: true, pensionable: false },
          { code: 'holiday_bonus', name: 'Holiday Bonus', taxable: true, pensionable: false },
          { code: 'overtime', name: 'Overtime Payment', taxable: true, pensionable: true },
          { code: 'special_allowance', name: 'Special Allowance', taxable: true, pensionable: false },
          { code: 'one_time_deduction', name: 'One-time Deduction', taxable: false, pensionable: false },
          { code: 'retroactive_adjustment', name: 'Retroactive Adjustment', taxable: true, pensionable: true }
        ]

        standardTypes.forEach(compType => {
          templateData.push([
            employee.employeeId || '',
            employee.email || '',
            `${employee.firstName} ${employee.lastName}`,
            employee.departmentId?.name || '',
            compType.code,
            '', // Amount - to be filled by user
            new Date().toISOString().split('T')[0],
            compType.name,
            '', // Notes - to be filled by user
            'MWK',
            '', // Payroll Run ID - optional
            'false',
            'one_time',
            '', // End Date - optional
            compType.taxable.toString(),
            compType.pensionable.toString()
          ])
        })
      }
    })

    // Create workbook
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(templateData)

    // Set column widths
    ws['!cols'] = [
      { wch: 25 }, // Employee ID
      { wch: 30 }, // Employee Email
      { wch: 25 }, // Employee Name
      { wch: 20 }, // Department
      { wch: 20 }, // Compensation Type
      { wch: 15 }, // Amount
      { wch: 15 }, // Effective Date
      { wch: 30 }, // Description
      { wch: 30 }, // Notes
      { wch: 10 }, // Currency
      { wch: 20 }, // Payroll Run ID
      { wch: 12 }, // Is Recurring
      { wch: 12 }, // Frequency
      { wch: 15 }, // End Date
      { wch: 10 }, // Taxable
      { wch: 12 }  // Pensionable
    ]

    // Add main sheet
    XLSX.utils.book_append_sheet(wb, ws, 'Compensation Data')

    // Create compensation types reference sheet
    const compensationTypesData = [
      ['Available Compensation Types'],
      [''],
      ['Type', 'Description', 'Default Taxable', 'Default Pensionable'],
      ['performance_bonus', 'Performance-based bonus payments', 'true', 'false'],
      ['holiday_bonus', 'Holiday or seasonal bonus payments', 'true', 'false'],
      ['overtime', 'Overtime work compensation', 'true', 'true'],
      ['special_allowance', 'Special project or role allowances', 'true', 'false'],
      ['one_time_deduction', 'One-time deductions (uniform, loans, etc.)', 'false', 'false'],
      ['retroactive_adjustment', 'Backpay or retroactive salary adjustments', 'true', 'true']
    ]

    // Add allowances from database
    if (allowances.length > 0) {
      compensationTypesData.push([''], ['From Allowances Database:'])
      allowances.forEach(allowance => {
        compensationTypesData.push([
          allowance.code,
          allowance.name,
          allowance.isTaxable.toString(),
          allowance.isPensionable.toString()
        ])
      })
    }

    const compensationTypesWs = XLSX.utils.aoa_to_sheet(compensationTypesData)
    compensationTypesWs['!cols'] = [{ wch: 25 }, { wch: 40 }, { wch: 15 }, { wch: 18 }]
    XLSX.utils.book_append_sheet(wb, compensationTypesWs, 'Compensation Types')

    // Create instructions sheet
    const instructionsData = [
      ['Compensation Template Instructions'],
      [''],
      [`Generated for ${employees.length} selected employees with ${allowances.length || 6} compensation types each`],
      [''],
      ['Template Structure:'],
      ['• Each employee has multiple rows - one for each available compensation type'],
      ['• Compensation types are fetched from the Allowances database'],
      ['• Taxable and Pensionable flags are pre-set based on allowance configuration'],
      ['• You only need to fill in the Amount column for applicable compensations'],
      [''],
      ['Instructions:'],
      ['1. Review each employee\'s compensation rows'],
      ['2. Fill in the Amount column ONLY for compensations you want to process'],
      ['3. Leave Amount blank for compensation types not applicable to the employee'],
      ['4. Adjust Effective Date if needed (format: YYYY-MM-DD)'],
      ['5. Modify Description and Notes as needed'],
      ['6. Do NOT change Employee ID, Email, or Name'],
      ['7. Save and upload the file for bulk import'],
      [''],
      ['Important Notes:'],
      ['• Employee information is pre-filled from the database'],
      ['• Multiple rows per employee are normal and expected'],
      ['• Only rows with Amount values will be processed during import'],
      ['• Duplicate employees with different compensation types are allowed'],
      ['• All amounts should be positive numbers'],
      ['• Dates must be in YYYY-MM-DD format'],
      ['• Currency defaults to MWK'],
      [''],
      ['Validation During Import:'],
      ['• Employee emails will be validated against the employee database'],
      ['• Invalid employee emails will be filtered out and reported'],
      ['• Valid employees will be processed and invalid ones skipped'],
      ['• Detailed import results will show success/error breakdown'],
      ['• You will see which employees were filtered out due to invalid emails']
    ]

    const instructionsWs = XLSX.utils.aoa_to_sheet(instructionsData)
    instructionsWs['!cols'] = [{ wch: 80 }]
    XLSX.utils.book_append_sheet(wb, instructionsWs, 'Instructions')

    // Generate Excel file buffer
    const excelBuffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' })

    // Create response with Excel file
    return new NextResponse(excelBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="compensation_template_${employees.length}_employees.xlsx"`
      }
    })

  } catch (error) {
    console.error('Error generating dynamic compensation template:', error)
    return NextResponse.json(
      { error: 'Failed to generate template', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

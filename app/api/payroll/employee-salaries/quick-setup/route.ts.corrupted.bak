import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { connectToDatabase } from '@/lib/backend/database';
import { Employee } from '@/models/Employee';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';
import SalaryStructure from '@/models/payroll/SalaryStructure';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

// Define roles that can perform quick setup
const EMPLOYEE_SALARY_ADMIN_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER
];

/**
 * POST /api/payroll/employee-salaries/quick-setup
 * Quick setup endpoint to create salary records for all employees without existing salaries
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, EMPLOYEE_SALARY_ADMIN_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body for configuration
    const body = await request.json();
    const {
      effectiveDate = new Date().toISOString().split('T')[0], // Default to today
      currency = 'MWK',
      paymentMethod = 'bank_transfer',
      useSalaryFromEmployeeRecord = true,
      defaultBasicSalary = 250000,
      onlyEmployeesWithoutSalaries = true
    } = body;

    logger.info('Starting quick employee salary setup', LogCategory.PAYROLL, {
      userId: user.id,
      effectiveDate,
      currency,
      useSalaryFromEmployeeRecord,
      defaultBasicSalary
    });

    // Get all active employees
    const employees = await Employee.find({
      employmentStatus: 'active',
      isBlocked: { $ne: true }
    })
      .select('firstName lastName email employeeId employeeNumber position departmentId salary')
      .populate('departmentId', 'name')
      .sort({ lastName: 1, firstName: 1 })
      .lean();

    if (employees.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'No active employees found in the system'
      });
    }

    // Get employees who already have salary records
    const existingSalaryEmployeeIds = await EmployeeSalary.find({ isActive: true })
      .distinct('employeeId');

    const existingSalaryEmployeeIdsSet = new Set(
      existingSalaryEmployeeIds.map(id => id.toString())
    );

    // Filter employees based on configuration
    const employeesToProcess = onlyEmployeesWithoutSalaries
      ? employees.filter(emp => !existingSalaryEmployeeIdsSet.has(emp._id.toString()))
      : employees;

    if (employeesToProcess.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'All employees already have salary records',
        data: {
          totalEmployees: employees.length,
          employeesWithSalaries: existingSalaryEmployeeIds.length,
          employeesProcessed: 0
        }
      });
    }

    // Get default salary structure
    const defaultSalaryStructure = await SalaryStructure.findOne({
      isActive: true,
      $or: [
        { name: /default/i },
        { name: /standard/i },
        { name: /basic/i }
      ]
    });

    if (!defaultSalaryStructure) {
      return NextResponse.json({
        success: false,
        message: 'No default salary structure found. Please create a salary structure first.'
      }, { status: 400 });
    }

    // Process employees and create salary records
    const results = {
      totalEmployees: employees.length,
      employeesWithExistingSalaries: existingSalaryEmployeeIds.length,
      employeesProcessed: 0,
      employeesCreated: 0,
      employeesSkipped: 0,
      errors: [] as Array<{ employeeName: string; error: string }>,
      created: [] as Array<{
        employeeName: string;
        email: string;
        basicSalary: number;
        department: string;
      }>
    };

    for (const employee of employeesToProcess) {
      try {
        results.employeesProcessed++;

        // Determine basic salary
        let basicSalary = defaultBasicSalary;
        if (useSalaryFromEmployeeRecord && employee.salary && employee.salary > 0) {
          basicSalary = employee.salary;
        }

        // Create salary record
        const salaryData = {
          employeeId: employee._id,
          salaryStructureId: defaultSalaryStructure._id,
          basicSalary,
          currency,
          effectiveDate: new Date(effectiveDate),
          paymentMethod,
          isActive: true,
          allowances: [],
          deductions: [],
          notes: `Created via quick setup on ${new Date().toLocaleDateString()}`,
          createdBy: user.id,
          updatedBy: user.id
        };

        const newSalary = new EmployeeSalary(salaryData);
        await newSalary.save();

        results.employeesCreated++;
        results.created.push({
          employeeName: `${employee.firstName} ${employee.lastName}`,
          email: employee.email,
          basicSalary,
          department: (employee.departmentId as any)?.name || 'No Department'
        });

        logger.debug('Created salary record for employee', LogCategory.PAYROLL, {
          employeeId: employee._id,
          employeeName: `${employee.firstName} ${employee.lastName}`,
          basicSalary
        });

      } catch (error) {
        results.employeesSkipped++;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        results.errors.push({
          employeeName: `${employee.firstName} ${employee.lastName}`,
          error: errorMessage
        });

        logger.error('Failed to create salary record for employee', LogCategory.PAYROLL, {
          employeeId: employee._id,
          employeeName: `${employee.firstName} ${employee.lastName}`,
          error: errorMessage
        });
      }
    }

    logger.info('Quick employee salary setup completed', LogCategory.PAYROLL, {
      userId: user.id,
      results
    });

    return NextResponse.json({
      success: true,
      message: `Quick setup completed! Created ${results.employeesCreated} salary records.`,
      data: results
    });

  } catch (error: unknown) {
    logger.error('Quick employee salary setup failed', LogCategory.PAYROLL, {
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return NextResponse.json(
      {
        error: 'Failed to perform quick setup',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

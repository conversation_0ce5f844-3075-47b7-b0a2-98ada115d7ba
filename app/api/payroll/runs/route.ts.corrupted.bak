// app/api/payroll/runs/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { payrollService } from '@/lib/services/payroll/unified-payroll-service';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * GET /api/payroll/runs
 * Get payroll runs
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const period = searchParams.get('period') || undefined;
    const status = searchParams.get('status') || undefined;
    const fromDate = searchParams.get('fromDate') || undefined;
    const toDate = searchParams.get('toDate') || undefined;
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Get payroll runs
    const result = await payrollService.getPayrollRuns({
      page,
      limit,
      period,
      status,
      fromDate: fromDate ? new Date(fromDate) : undefined,
      toDate: toDate ? new Date(toDate) : undefined,
      sortBy,
      sortOrder: sortOrder === 'asc' ? 'asc' : 'desc'
    });

    return NextResponse.json({
      success: true,
      data: {
        payrollRuns: result.docs,
        pagination: {
          page: result.page,
          limit,
          total: result.totalDocs,
          totalPages: result.totalPages,
          hasNextPage: result.hasNextPage,
          hasPrevPage: result.hasPrevPage
        }
      }
    });
  } catch (error: unknown) {
    logger.error('Error getting payroll runs', LogCategory.API, error);
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'PAYROLL_RUNS_FETCH_FAILED',
      error instanceof Error ? error.message : 'Unknown error',
      'Failed to retrieve payroll runs. Please try again.',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

/**
 * POST /api/payroll/runs
 * Create a new payroll run
 */
export async function POST(req: NextRequest): Promise<Response> {
  // Define a more specific type for the request body
  interface PayrollRunBody {
    name: string;
    payPeriod: {
      month: number | string;
      year: number | string;
      startDate: string;
      endDate: string;
    };
    departments?: string[];
    force?: boolean;
    notes?: string;
    [key: string]: unknown;
  }

  // Declare body variable at the top level so it's accessible in catch block
  let bodyData: PayrollRunBody = {
    name: '',
    payPeriod: {
      month: 0,
      year: 0,
      startDate: '',
      endDate: ''
    }
  };

  try {
    logger.info('Creating new payroll run', LogCategory.API, {
      path: req.nextUrl.pathname,
      method: req.method
    });

    // Check authentication
    const user = await getCurrentUser(req);

    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    logger.info('User authenticated for payroll run creation', LogCategory.API, {
      userId: user?.id,
      userRole: user?.role
    });

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.PAYROLL_SPECIALIST,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: {
            requiredRoles: [
              UserRole.SUPER_ADMIN,
              UserRole.SYSTEM_ADMIN,
              UserRole.FINANCE_DIRECTOR,
              UserRole.FINANCE_MANAGER,
              UserRole.PAYROLL_SPECIALIST,
              UserRole.HR_DIRECTOR,
              UserRole.HR_MANAGER
            ]
          }
        }
      );
    }

    // Get request body
    bodyData = await req.json();

    // Validate required fields with detailed error messages
    const missingFields = [];

    if (!bodyData.name) missingFields.push('name');
    if (!bodyData.payPeriod) {
      missingFields.push('payPeriod');
    } else {
      if (!bodyData.payPeriod.month) missingFields.push('payPeriod.month');
      if (!bodyData.payPeriod.year) missingFields.push('payPeriod.year');
      if (!bodyData.payPeriod.startDate) missingFields.push('payPeriod.startDate');
      if (!bodyData.payPeriod.endDate) missingFields.push('payPeriod.endDate');
    }

    if (missingFields.length > 0) {
      return errorService.handlePayrollError(
        'VALIDATION_FAILED',
        {
          userId: user.id,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: {
            missingFields,
            providedData: bodyData
          }
        }
      );
    }

    // Validate data types
    try {
      // Ensure month and year are numbers
      bodyData.payPeriod.month = Number(bodyData.payPeriod.month);
      bodyData.payPeriod.year = Number(bodyData.payPeriod.year);

      // Ensure dates are valid
      if (bodyData.payPeriod.startDate) {
        new Date(bodyData.payPeriod.startDate);
      }

      if (bodyData.payPeriod.endDate) {
        new Date(bodyData.payPeriod.endDate);
      }
    } catch (error: unknown) {
      logger.warn('Invalid data types in payroll run creation', LogCategory.API, {
        userId: user?.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        body: JSON.stringify(bodyData)
      });

      return NextResponse.json(
        {
          error: 'Invalid data types in request body',
          details: error instanceof Error ? error.message : 'Unknown error',
          success: false
        },
        { status: 400 }
      );
    }

    // Ensure departments is an array
    if (!bodyData.departments) {
      bodyData.departments = [];
    }

    // Ensure dates are properly formatted
    try {
      bodyData.payPeriod.startDate = new Date(bodyData.payPeriod.startDate).toISOString();
      bodyData.payPeriod.endDate = new Date(bodyData.payPeriod.endDate).toISOString();
    } catch (error: unknown) {
      logger.warn('Error formatting dates', LogCategory.API, {
        startDate: bodyData.payPeriod.startDate,
        endDate: bodyData.payPeriod.endDate,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Check if a payroll run already exists for this period
    try {
      // Only check for duplicates if force flag is not set
      if (!bodyData.force) {
        const existingRuns = await payrollService.getPayrollRuns({
          period: `${bodyData.payPeriod.month}/${bodyData.payPeriod.year}`,
          limit: 1
        });

        if (existingRuns && existingRuns.docs && existingRuns.docs.length > 0) {
          logger.info('Duplicate payroll run detected', LogCategory.API, {
            userId: user?.id,
            payPeriod: `${bodyData.payPeriod.month}/${bodyData.payPeriod.year}`,
            existingRunId: existingRuns.docs[0]._id || existingRuns.docs[0].id
          });

          return errorService.handlePayrollError(
            'DUPLICATE_ENTRY',
            {
              userId: user.id,
              endpoint: req.nextUrl.pathname,
              method: req.method,
              additionalData: {
                payPeriod: `${bodyData.payPeriod.month}/${bodyData.payPeriod.year}`,
                existingPayrollRun: existingRuns.docs[0]
              }
            }
          );
        }
      } else {
        logger.info('Force creating payroll run despite potential duplicates', LogCategory.API, {
          userId: user?.id,
          payPeriod: `${bodyData.payPeriod.month}/${bodyData.payPeriod.year}`,
          name: bodyData.name
        });
      }
    } catch (checkError: unknown) {
      logger.warn('Error checking for duplicate payroll runs', LogCategory.API, {
        error: checkError instanceof Error ? checkError.message : 'Unknown error',
        payPeriod: `${bodyData.payPeriod.month}/${bodyData.payPeriod.year}`
      });
      // Continue with creation even if check fails
    }

    // Create payroll run
    logger.info('Creating payroll run with service', LogCategory.API, {
      userId: user?.id,
      payPeriod: `${bodyData.payPeriod.month}/${bodyData.payPeriod.year}`,
      name: bodyData.name,
      departmentsCount: bodyData.departments.length,
      force: !!bodyData.force
    });

    // Ensure user is not null before accessing id
    if (!user || !user.id) {
      throw new Error('User ID is required to create a payroll run');
    }

    // Ensure month and year are numbers and dates are Date objects before passing to service
    // Also convert department string IDs to ObjectIds
    const payrollRunData = {
      ...bodyData,
      payPeriod: {
        ...bodyData.payPeriod,
        month: Number(bodyData.payPeriod.month),
        year: Number(bodyData.payPeriod.year),
        startDate: new Date(bodyData.payPeriod.startDate),
        endDate: new Date(bodyData.payPeriod.endDate)
      },
      // Convert string department IDs to ObjectIds if they exist
      departments: bodyData.departments?.map(id => new mongoose.Types.ObjectId(id))
    };

    const payrollRun = await payrollService.createPayrollRun(payrollRunData, user.id);

    logger.info('Payroll run created successfully', LogCategory.API, {
      userId: user.id, // Safe to use user.id here since we've checked it above
      payrollRunId: payrollRun._id ? payrollRun._id.toString() : payrollRun.id,
      name: payrollRun.name
    });

    return NextResponse.json({
      success: true,
      message: 'Payroll run created successfully',
      data: payrollRun
    }, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error creating payroll run', LogCategory.API, error instanceof Error ? error : new Error('Unknown error'), {
      path: req.nextUrl.pathname,
      method: req.method,
      errorMessage: error instanceof Error ? error.message : 'Unknown error'
    });

    // Determine error type based on error message
    if (error instanceof Error) {
      if (error.message.includes('validation')) {
        return errorService.handlePayrollError(
          'VALIDATION_FAILED',
          {
            endpoint: req.nextUrl.pathname,
            method: req.method,
            additionalData: { validationError: error.message }
          }
        );
      } else if (error.message.includes('duplicate') || error.message.includes('already exists')) {
        return errorService.handlePayrollError(
          'DUPLICATE_ENTRY',
          {
            endpoint: req.nextUrl.pathname,
            method: req.method,
            additionalData: {
              payPeriod: `${bodyData?.payPeriod?.month}/${bodyData?.payPeriod?.year}`,
              errorMessage: error.message
            }
          }
        );
      }
    }

    // Generic system error
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'PAYROLL_RUN_CREATION_FAILED',
      error instanceof Error ? error.message : 'Unknown error',
      'Failed to create payroll run. Please try again.',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        additionalData: { bodyData }
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

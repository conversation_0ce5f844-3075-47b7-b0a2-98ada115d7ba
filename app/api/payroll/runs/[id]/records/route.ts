import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { payrollService } from '@/lib/services/payroll/unified-payroll-service';

export const runtime = 'nodejs';

// app/api/payroll/runs/[id]/records/route.ts
// import { connectToDatabase } from '@/lib/backend/database';
/**
 * GET /api/payroll/runs/[id]/records
 * Get payroll records for a payroll run
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Resolve the params promise first
  const { id } = await params;
  const payrollRunId = id; // Store in a variable accessible in the catch block
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status') || undefined;
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    // Get payroll records
    const result = await payrollService.getPayrollRecords(payrollRunId, {
      page,
      limit,
      status,
      sortBy,
      sortOrder: sortOrder === 'asc' ? 'asc' : 'desc'
    });
    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error(`Error getting payroll records for run ${payrollRunId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get payroll records', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
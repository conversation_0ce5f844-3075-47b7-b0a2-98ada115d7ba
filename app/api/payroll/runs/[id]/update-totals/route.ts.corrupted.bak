import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import PayrollRun from '@/models/payroll/PayrollRun';
import Employee from '@/models/Employee';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';

/**
 * POST /api/payroll/runs/[id]/update-totals
 * Update payroll run totals
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Resolve the params promise first
  const { id } = await params;
  const payrollRunId = id; // Store in a variable accessible in the catch block

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get payroll run
    const payrollRun = await PayrollRun.findById(id);

    if (!payrollRun) {
      return NextResponse.json(
        { error: 'Payroll run not found' },
        { status: 404 }
      );
    }

    // Check if payroll run is in a valid state
    if (!['completed', 'approved', 'paid'].includes(payrollRun.status)) {
      return NextResponse.json(
        { error: 'Payroll run must be completed, approved, or paid to update totals' },
        { status: 400 }
      );
    }

    // Get all employees
    const employees = await Employee.find();
    logger.info(`Found ${employees.length} employees`, LogCategory.PAYROLL);

    // Calculate totals
    let totalGrossSalary = 0;
    let totalDeductions = 0;
    let totalTax = 0;
    let totalNetSalary = 0;
    let processedEmployees = 0;

    for (const employee of employees) {
      // Get employee salary
      const employeeSalary = await EmployeeSalary.findOne({
        employeeId: employee._id,
        isActive: true
      });

      if (!employeeSalary) {
        logger.warn(`No active salary found for employee ${employee._id}`, LogCategory.PAYROLL);
        continue;
      }

      // Calculate gross salary (basic + allowances)
      let grossSalary = employeeSalary.basicSalary || 0;
      let totalAllowances = 0;

      // Add allowances
      if (employeeSalary.allowances && employeeSalary.allowances.length > 0) {
        for (const allowance of employeeSalary.allowances) {
          let amount = 0;

          if (allowance.amount) {
            amount = allowance.amount;
          } else if (allowance.percentage) {
            amount = employeeSalary.basicSalary * (allowance.percentage / 100);
          }

          totalAllowances += amount;
        }
      }

      grossSalary += totalAllowances;

      // Calculate tax (simplified - 15% of gross)
      const taxRate = 0.15; // 15% tax rate as a fallback
      const tax = grossSalary * taxRate;

      // Calculate deductions
      let totalEmployeeDeductions = 0;

      // Add deductions
      if (employeeSalary.deductions && employeeSalary.deductions.length > 0) {
        for (const deduction of employeeSalary.deductions) {
          let amount = 0;

          if (deduction.amount) {
            amount = deduction.amount;
          } else if (deduction.percentage) {
            amount = employeeSalary.basicSalary * (deduction.percentage / 100);
          }

          totalEmployeeDeductions += amount;
        }
      }

      // Calculate net salary
      const netSalary = grossSalary - tax - totalEmployeeDeductions;

      // Update totals
      totalGrossSalary += grossSalary;
      totalTax += tax;
      totalDeductions += totalEmployeeDeductions;
      totalNetSalary += netSalary;
      processedEmployees++;

      logger.info(`Processed employee ${employee._id}: Gross=${grossSalary}, Tax=${tax}, Deductions=${totalEmployeeDeductions}, Net=${netSalary}`, LogCategory.PAYROLL);
    }

    // Update payroll run
    const updatedPayrollRun = await PayrollRun.findByIdAndUpdate(
      id,
      {
        $set: {
          totalGrossSalary,
          totalDeductions,
          totalTax,
          totalNetSalary,
          processedEmployees,
          updatedBy: user.id,
          updatedAt: new Date()
        }
      },
      { new: true }
    );

    logger.info('Payroll run totals updated successfully', LogCategory.PAYROLL, {
      payrollRunId: id,
      totalGrossSalary,
      totalDeductions,
      totalTax,
      totalNetSalary,
      processedEmployees
    });

    return NextResponse.json({
      success: true,
      message: 'Payroll run totals updated successfully',
      data: updatedPayrollRun
    });
  } catch (error) {
    logger.error(`Error updating payroll run totals for ${payrollRunId}`, LogCategory.API, error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update payroll run totals',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import PayrollRun from '@/models/payroll/PayrollRun';
import PayrollRecord from '@/models/payroll/PayrollRecord';
import { Employee } from '@/models/Employee';
import Department from '@/models/Department';
import PaySlip from '@/models/payroll/PaySlip';

/**
 * GET /api/payroll/runs/[id]/employees
 * Get employees for a specific payroll run with their payslip status
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_OFFICER
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: {
            requiredRoles: [
              UserRole.SUPER_ADMIN,
              UserRole.SYSTEM_ADMIN,
              UserRole.FINANCE_DIRECTOR,
              UserRole.FINANCE_MANAGER,
              UserRole.HR_DIRECTOR,
              UserRole.HR_MANAGER,
              UserRole.ACCOUNTANT,
              UserRole.PAYROLL_OFFICER
            ]
          }
        }
      );
    }

    // Get payroll run ID
    const { id: payrollRunId } = await params;

    // Connect to database
    await connectToDatabase();

    // Verify payroll run exists
    const payrollRun = await PayrollRun.findById(payrollRunId);
    if (!payrollRun) {
      return errorService.handlePayrollError(
        'RESOURCE_NOT_FOUND',
        {
          userId: user.id,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: { payrollRunId }
        }
      );
    }

    // Get all active employees (filtered by departments if payroll run has department restrictions)
    const employeeQuery: any = { employmentStatus: 'active' };

    // Filter by departments if the payroll run has department restrictions
    if (payrollRun.departments && payrollRun.departments.length > 0) {
      employeeQuery.departmentId = { $in: payrollRun.departments };
    }

    const allEmployees = await Employee.find(employeeQuery)
      .populate({
        path: 'departmentId',
        model: Department,
        select: 'name'
      })
      .lean();

    // Get payroll records for this run to check processing status
    const payrollRecords = await PayrollRecord.find({ payrollRunId }).lean();
    const payrollRecordMap = new Map(
      payrollRecords.map(record => [
        record.employeeId.toString(),
        {
          grossSalary: record.grossSalary,
          netSalary: record.netSalary,
          status: record.status,
          components: record.components
        }
      ])
    );

    // Get existing payslips for this payroll run
    const existingPayslips = await PaySlip.find({ payrollRunId }).lean();
    const payslipMap = new Map(
      existingPayslips.map(payslip => [
        payslip.employeeId.toString(),
        {
          id: payslip._id.toString(),
          status: payslip.status
        }
      ])
    );

    // Transform data for frontend - include ALL employees
    const employees = allEmployees.map(employee => {
      const department = employee.departmentId as any;
      const payrollRecord = payrollRecordMap.get(employee._id.toString());
      const payslip = payslipMap.get(employee._id.toString());

      return {
        _id: employee._id.toString(),
        firstName: employee.firstName || '',
        lastName: employee.lastName || '',
        email: employee.email || '',
        employeeNumber: employee.employeeNumber || '',
        department: {
          _id: department?._id?.toString() || '',
          name: department?.name || 'Unknown Department'
        },
        position: employee.position || '',
        status: employee.employmentStatus || 'active',
        hasPayrollRecord: !!payrollRecord,
        payrollStatus: payrollRecord?.status || 'not_processed',
        hasPayslip: !!payslip,
        payslipId: payslip?.id,
        payslipStatus: payslip?.status,
        grossSalary: payrollRecord?.grossSalary || 0,
        netSalary: payrollRecord?.netSalary || 0
      };
    });

    // Sort employees by name
    employees.sort((a, b) => {
      const nameA = `${a.firstName} ${a.lastName}`.toLowerCase();
      const nameB = `${b.firstName} ${b.lastName}`.toLowerCase();
      return nameA.localeCompare(nameB);
    });

    logger.info(`Retrieved ${employees.length} employees for payroll run ${payrollRunId}`, LogCategory.API, {
      payrollRunId,
      userId: user.id,
      employeeCount: employees.length,
      payslipsGenerated: employees.filter(e => e.hasPayslip).length
    });

    return NextResponse.json({
      success: true,
      data: {
        payrollRun: {
          _id: payrollRun._id.toString(),
          name: payrollRun.name,
          status: payrollRun.status,
          payPeriod: payrollRun.payPeriod,
          totalEmployees: payrollRun.totalEmployees,
          processedEmployees: payrollRun.processedEmployees
        },
        employees,
        summary: {
          totalEmployees: employees.length,
          processedEmployees: employees.filter(e => e.hasPayrollRecord).length,
          unprocessedEmployees: employees.filter(e => !e.hasPayrollRecord).length,
          payslipsGenerated: employees.filter(e => e.hasPayslip).length,
          payslipsPending: employees.filter(e => !e.hasPayslip).length
        }
      }
    });

  } catch (error: unknown) {
    const payrollRunId = await params.then(p => p.id).catch(() => 'unknown');
    logger.error(`Error getting employees for payroll run ${payrollRunId}`, LogCategory.API, error);

    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'PAYROLL_EMPLOYEES_FETCH_FAILED',
      error instanceof Error ? error.message : 'Unknown error',
      'Failed to retrieve employees for payroll run. Please try again.',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        additionalData: { payrollRunId }
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

/**
 * POST /api/payroll/runs/[id]/employees
 * Add employees to a payroll run (for future use)
 */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions (higher level required for modifications)
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Get payroll run ID
    const { id: payrollRunId } = await params;

    // Get request body
    const body = await req.json();
    const { employeeIds } = body;

    if (!employeeIds || !Array.isArray(employeeIds)) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'INVALID_EMPLOYEE_IDS',
        'Employee IDs must be provided as an array',
        'Please provide a valid array of employee IDs.',
        {
          userId: user.id,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: { providedData: body }
        },
        400,
        ErrorSeverity.MEDIUM
      );
    }

    // Connect to database
    await connectToDatabase();

    // Verify payroll run exists and is in draft status
    const payrollRun = await PayrollRun.findById(payrollRunId);
    if (!payrollRun) {
      return errorService.handlePayrollError(
        'RESOURCE_NOT_FOUND',
        {
          userId: user.id,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: { payrollRunId }
        }
      );
    }

    if (payrollRun.status !== 'draft') {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'PAYROLL_RUN_NOT_EDITABLE',
        'Payroll run is not in draft status',
        'Only draft payroll runs can be modified.',
        {
          userId: user.id,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: { payrollRunId, currentStatus: payrollRun.status }
        },
        400,
        ErrorSeverity.MEDIUM
      );
    }

    // This endpoint is for future use - currently payroll runs are created with employees
    // Return a not implemented response for now
    return errorService.createApiResponse(
      ErrorType.VALIDATION,
      'FEATURE_NOT_IMPLEMENTED',
      'Adding employees to existing payroll runs is not yet implemented',
      'This feature will be available in a future update.',
      {
        userId: user.id,
        endpoint: req.nextUrl.pathname,
        method: req.method,
        additionalData: { payrollRunId, employeeIds }
      },
      501,
      ErrorSeverity.LOW
    );

  } catch (error: unknown) {
    const payrollRunId = await params.then(p => p.id).catch(() => 'unknown');
    logger.error(`Error adding employees to payroll run ${payrollRunId}`, LogCategory.API, error);

    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'PAYROLL_EMPLOYEES_ADD_FAILED',
      error instanceof Error ? error.message : 'Unknown error',
      'Failed to add employees to payroll run. Please try again.',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        additionalData: { payrollRunId }
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

//  app/api/payroll/runs/[id]/pay/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { payrollService } from '@/lib/services/payroll/unified-payroll-service';
import PayrollRun from '@/models/payroll/PayrollRun';

/**
 * POST /api/payroll/runs/[id]/pay
 * Mark a payroll run as paid
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  // Resolve the params promise first
  const { id } = await params;
  const payrollRunId = id;

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get payroll run
    const payrollRun = await PayrollRun.findById(id);

    if (!payrollRun) {
      return NextResponse.json(
        { error: 'Payroll run not found' },
        { status: 404 }
      );
    }

    // Check if payroll run is in approved status
    if (payrollRun.status !== 'approved') {
      return NextResponse.json(
        { error: `Cannot mark payroll run as paid with status '${payrollRun.status}'` },
        { status: 400 }
      );
    }

    // Get request body
    const body = await req.json();

    // Mark payroll run as paid
    // Call the service method with the required parameters: payrollRunId and userId
    const result = await payrollService.markPayrollRunAsPaid(id, user.id);

    // If we need to update additional payment details, we can do it here
    if (body.paymentDate || body.paymentReference || body.notes) {
      // Update the payroll run with additional payment details if needed
      await PayrollRun.findByIdAndUpdate(id, {
        $set: {
          paymentDate: body.paymentDate ? new Date(body.paymentDate) : new Date(),
          paymentReference: body.paymentReference,
          notes: body.notes
        }
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Payroll run marked as paid successfully',
      data: result
    });
  } catch (error: unknown) {
    logger.error(`Error marking payroll run as paid ${payrollRunId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to mark payroll run as paid', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

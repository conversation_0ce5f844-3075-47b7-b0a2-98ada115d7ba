import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import PayrollRun from '@/models/payroll/PayrollRun';
import { enhancedPayrollProcessor } from '@/lib/services/payroll/enhanced-payroll-processor';

export const runtime = 'nodejs';

/**
 * POST /api/payroll/runs/[id]/process-enhanced
 * Start enhanced payroll processing with real-time progress tracking
 */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.PAYROLL_OFFICER
    ]);
    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: {
            requiredRoles: [
              UserRole.SUPER_ADMIN,
              UserRole.SYSTEM_ADMIN,
              UserRole.FINANCE_DIRECTOR,
              UserRole.FINANCE_MANAGER,
              UserRole.HR_DIRECTOR,
              UserRole.HR_MANAGER,
              UserRole.PAYROLL_OFFICER
            ]
          }
        }
      );
    }
    // Get payroll run ID
    const { id: payrollRunId } = await params;
    // Get request body
    const body = await req.json();
    const { batchSize = 10, maxConcurrency = 3, notes } = body;
    // Connect to database
    await connectToDatabase();
    // Verify payroll run exists
    const payrollRun = await PayrollRun.findById(payrollRunId);
    if (!payrollRun) {
      return errorService.handlePayrollError(
        'RESOURCE_NOT_FOUND',
        {
          userId: user.id,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: { payrollRunId }
        }
      );
    }
    // Check if payroll run is in correct status
    if (payrollRun.status !== 'draft' && payrollRun.status !== 'processing') {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'INVALID_PAYROLL_STATUS',
        `Payroll run is in ${payrollRun.status} status`,
        'Only draft or processing payroll runs can be processed.',
        {
          userId: user.id,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: { payrollRunId, currentStatus: payrollRun.status }
        },
        400,
        ErrorSeverity.MEDIUM
      );
    }
    // Add notes if provided
    if (notes) {
      payrollRun.notes = notes;
      await payrollRun.save();
    }
    // Start enhanced payroll processing
    const result = await enhancedPayrollProcessor.startPayrollProcessing(
      payrollRunId,
      user.id,
      {
        batchSize: Math.max(batchSize, 5), // Minimum batch size
        maxConcurrency: Math.min(Math.max(maxConcurrency, 1), 5) // Limit concurrency
      }
    );
    logger.info(`Enhanced payroll processing started for run ${payrollRunId}`, LogCategory.PAYROLL, {
      payrollRunId,
      userId: user.id,
      operationId: result.operationId,
      totalEmployees: result.totalEmployees,
      batchSize,
      maxConcurrency
    });
    return NextResponse.json({
      success: true,
      message: 'Enhanced payroll processing started successfully',
      data: {
        operationId: result.operationId,
        totalEmployees: result.totalEmployees,
        payrollRunId,
        batchSize,
        maxConcurrency,
        message: 'Processing started with real-time progress tracking'
      }
    });
  } catch (error: unknown) {
    const payrollRunId = await params.then(p => p.id).catch(() => 'unknown');
    logger.error(`Error starting enhanced payroll processing for run ${payrollRunId}`, LogCategory.API, error);
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'ENHANCED_PAYROLL_PROCESSING_FAILED',
      error instanceof Error ? error.message : 'Unknown error',
      'Failed to start enhanced payroll processing. Please try again.',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        additionalData: { payrollRunId }
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}
/**
 * GET /api/payroll/runs/[id]/process-enhanced
 * Get enhanced payroll processing progress
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }
    // Get payroll run ID and operation ID
    const { id: payrollRunId } = await params;
    const { searchParams } = new URL(req.url);
    const operationId = searchParams.get('operationId');
    if (!operationId) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'MISSING_OPERATION_ID',
        'Operation ID is required',
        'Please provide a valid operation ID to get progress.',
        {
          userId: user.id,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: { payrollRunId }
        },
        400,
        ErrorSeverity.MEDIUM
      );
    }
    // Get progress from enhanced processor
    const progress = enhancedPayrollProcessor.getProgress(operationId);
    console.log('Progress API called:', { operationId, progress: progress ? 'found' : 'not found' });
    if (!progress) {
      console.log('Operation not found in progress map');
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'OPERATION_NOT_FOUND',
        'Operation not found or expired',
        'The requested operation was not found or has expired.',
        {
          userId: user.id,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: { payrollRunId, operationId }
        },
        404,
        ErrorSeverity.MEDIUM
      );
    }
    // Calculate percentage
    const percentage = progress.totalEmployees > 0
      ? Math.round((progress.processedEmployees / progress.totalEmployees) * 100)
      : 0;
    // Sort employees: completed first (with green checkmarks), then processing, then pending, then errors
    const sortedEmployees = [...progress.employees].sort((a, b) => {
      const statusOrder = { completed: 0, processing: 1, pending: 2, error: 3 };
      return statusOrder[a.status] - statusOrder[b.status];
    });
    return NextResponse.json({
      success: true,
      data: {
        operationId: progress.operationId,
        payrollRunId: progress.payrollRunId,
        status: progress.status,
        totalEmployees: progress.totalEmployees,
        processedEmployees: progress.processedEmployees,
        failedEmployees: progress.failedEmployees,
        percentage,
        currentEmployee: progress.currentEmployee,
        employees: sortedEmployees,
        estimatedTimeRemaining: progress.estimatedTimeRemaining,
        averageProcessingTime: progress.averageProcessingTime,
        error: progress.error,
        startTime: progress.startTime,
        elapsedTime: Date.now() - progress.startTime
      }
    });
  } catch (error: unknown) {
    const payrollRunId = await params.then(p => p.id).catch(() => 'unknown');
    logger.error(`Error getting enhanced payroll processing progress for run ${payrollRunId}`, LogCategory.API, error);
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'PROGRESS_FETCH_FAILED',
      error instanceof Error ? error.message : 'Unknown error',
      'Failed to get processing progress. Please try again.',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        additionalData: { payrollRunId }
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}
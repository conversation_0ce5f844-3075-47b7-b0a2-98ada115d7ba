import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import PayrollRun from '@/models/payroll/PayrollRun';
import PayrollRecord from '@/models/payroll/PayrollRecord';
import mongoose from 'mongoose';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

// app/api/payroll/runs/[id]/debug-records/route.ts
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectToDatabase();
    const currentUser = await getCurrentUser(req);
    if (!currentUser) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    const { id } = await params;
    // Get payroll run
    const payrollRun = await PayrollRun.findById(id);
    if (!payrollRun) {
      return NextResponse.json(
        { error: 'Payroll run not found' },
        { status: 404 }
      );
    }
    logger.info(`Debugging payroll records for payroll run ${id}`, LogCategory.PAYROLL, {
      payrollRunId: id,
      payrollRunStatus: payrollRun.status
    });
    // Get all payroll records for this payroll run
    const allPayrollRecords = await PayrollRecord.find({
      payrollRunId: new mongoose.Types.ObjectId(id)
    }).populate('employeeId', 'firstName lastName employeeNumber')
      .lean();
    // Get payroll records by status
    const recordsByStatus = {
      draft: allPayrollRecords.filter(r => r.status === 'draft'),
      approved: allPayrollRecords.filter(r => r.status === 'approved'),
      paid: allPayrollRecords.filter(r => r.status === 'paid'),
      cancelled: allPayrollRecords.filter(r => r.status === 'cancelled')
    };
    // Calculate totals for each status
    const totalsByStatus = Object.entries(recordsByStatus).reduce((acc, [status, records]) => {
      acc[status] = records.reduce((totals, record) => ({
        count: totals.count + 1,
        totalGrossSalary: totals.totalGrossSalary + (record.grossSalary || 0),
        totalDeductions: totals.totalDeductions + (record.totalDeductions || 0),
        totalTax: totals.totalTax + (record.totalTax || 0),
        totalNetSalary: totals.totalNetSalary + (record.netSalary || 0)
      }), {
        count: 0,
        totalGrossSalary: 0,
        totalDeductions: 0,
        totalTax: 0,
        totalNetSalary: 0
      });
      return acc;
    }, {} as any);
    // Calculate overall totals (excluding cancelled)
    const activeRecords = allPayrollRecords.filter(r => r.status !== 'cancelled');
    const overallTotals = activeRecords.reduce((totals, record) => ({
      totalGrossSalary: totals.totalGrossSalary + (record.grossSalary || 0),
      totalDeductions: totals.totalDeductions + (record.totalDeductions || 0),
      totalTax: totals.totalTax + (record.totalTax || 0),
      totalNetSalary: totals.totalNetSalary + (record.netSalary || 0)
    }), {
      totalGrossSalary: 0,
      totalDeductions: 0,
      totalTax: 0,
      totalNetSalary: 0
    });
    // Sample records for inspection
    const sampleRecords = allPayrollRecords.slice(0, 3).map(record => ({
      _id: record._id,
      employeeName: record.employeeId ?
        `${(record.employeeId as any).firstName} ${(record.employeeId as any).lastName}` :
        'Unknown Employee',
      status: record.status,
      grossSalary: record.grossSalary,
      totalDeductions: record.totalDeductions,
      totalTax: record.totalTax,
      netSalary: record.netSalary,
      createdAt: record.createdAt
    }));
    const debugInfo = {
      payrollRun: {
        _id: payrollRun._id,
        name: payrollRun.name,
        status: payrollRun.status,
        totalEmployees: payrollRun.totalEmployees,
        processedEmployees: payrollRun.processedEmployees,
        currentTotals: {
          totalGrossSalary: payrollRun.totalGrossSalary,
          totalDeductions: payrollRun.totalDeductions,
          totalTax: payrollRun.totalTax,
          totalNetSalary: payrollRun.totalNetSalary
        }
      },
      payrollRecords: {
        totalRecords: allPayrollRecords.length,
        activeRecords: activeRecords.length,
        recordsByStatus: {
          draft: recordsByStatus.draft.length,
          approved: recordsByStatus.approved.length,
          paid: recordsByStatus.paid.length,
          cancelled: recordsByStatus.cancelled.length
        },
        totalsByStatus,
        overallTotals,
        sampleRecords
      },
      analysis: {
        hasRecords: allPayrollRecords.length > 0,
        hasActiveRecords: activeRecords.length > 0,
        totalsMatch: {
          grossSalary: payrollRun.totalGrossSalary === overallTotals.totalGrossSalary,
          deductions: payrollRun.totalDeductions === overallTotals.totalDeductions,
          tax: payrollRun.totalTax === overallTotals.totalTax,
          netSalary: payrollRun.totalNetSalary === overallTotals.totalNetSalary
        },
        recommendations: [] as string[]
      }
    };
    // Add recommendations based on analysis
    if (allPayrollRecords.length === 0) {
      debugInfo.analysis.recommendations.push(
        'No payroll records found. The payroll processing may have failed or not been completed.'
      );
    } else if (activeRecords.length === 0) {
      debugInfo.analysis.recommendations.push(
        'All payroll records are cancelled. Check why records were cancelled.'
      );
    } else if (payrollRun.totalGrossSalary === 0 && overallTotals.totalGrossSalary > 0) {
      debugInfo.analysis.recommendations.push(
        'Payroll run totals are zero but individual records have values. Use "Recalculate Totals" to fix this.'
      );
    } else if (payrollRun.totalGrossSalary !== overallTotals.totalGrossSalary) {
      debugInfo.analysis.recommendations.push(
        'Payroll run totals do not match calculated totals from records. Use "Recalculate Totals" to sync them.'
      );
    } else {
      debugInfo.analysis.recommendations.push(
        'Payroll run totals match the calculated totals from records. Everything looks correct.'
      );
    }
    logger.info(`Debug info generated for payroll run ${id}`, LogCategory.PAYROLL, {
      payrollRunId: id,
      totalRecords: allPayrollRecords.length,
      activeRecords: activeRecords.length,
      totalsMatch: debugInfo.analysis.totalsMatch
    });
    return NextResponse.json({
      success: true,
      data: debugInfo
    });
  } catch (error) {
    logger.error(`Error debugging payroll records for payroll run ${(await params).id}`, LogCategory.API, error);
    return NextResponse.json(
      {
        error: 'Failed to debug payroll records',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
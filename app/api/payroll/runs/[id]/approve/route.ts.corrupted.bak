// app/api/payroll/runs/[id]/approve/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { payrollService } from '@/lib/services/payroll/unified-payroll-service';
import PayrollRun from '@/models/payroll/PayrollRun';

/**
 * POST /api/payroll/runs/[id]/approve
 * Approve a payroll run
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  // Resolve the params promise first
  const { id } = await params;
  const payrollRunId = id; // Store in a variable accessible in the catch block

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get payroll run
    const payrollRun = await PayrollRun.findById(id);

    if (!payrollRun) {
      return NextResponse.json(
        { error: 'Payroll run not found' },
        { status: 404 }
      );
    }

    // Check if payroll run is in completed status
    if (payrollRun.status !== 'completed') {
      return NextResponse.json(
        { error: `Cannot approve payroll run with status '${payrollRun.status}'` },
        { status: 400 }
      );
    }

    // Get request body
    const body = await req.json();

    // Approve payroll run
    const result = await payrollService.approvePayrollRun(id, user.id, body?.notes);

    return NextResponse.json({
      success: true,
      message: 'Payroll run approved successfully',
      data: result
    });
  } catch (error: unknown) {
    logger.error(`Error approving payroll run ${payrollRunId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to approve payroll run', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

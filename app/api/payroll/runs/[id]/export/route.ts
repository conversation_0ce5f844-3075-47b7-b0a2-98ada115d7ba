import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import PayrollRun from '@/models/payroll/PayrollRun';
import PayrollRecord from '@/models/payroll/PayrollRecord';
import * as XLSX from 'xlsx';
import { format } from 'date-fns';

export const runtime = 'nodejs';

// app/api/payroll/runs/[id]/export/route.ts
/**
 * GET /api/payroll/runs/[id]/export
 * Export payroll run data in Excel or PDF format
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  // Resolve the params promise first
  const { id } = await params;
  const payrollRunId = id; // Store in a variable accessible in the catch block
  try {
    // Check authentication
    const currentUser = await getCurrentUser(req);
    if (!currentUser) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(currentUser, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_OFFICER
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Get format from query parameters
    const url = new URL(req.url);
    const exportFormat = url.searchParams.get('format') || 'excel'; // Renamed to avoid conflict with date-fns format
    // Connect to database
    await connectToDatabase();
    // Get payroll run
    const payrollRun = await PayrollRun.findById(id);
    if (!payrollRun) {
      return NextResponse.json(
        { error: 'Payroll run not found' },
        { status: 404 }
      );
    }
    // Get payroll records
    const payrollRecords = await PayrollRecord.find({ payrollRunId: id })
      .populate('employeeId', 'firstName lastName employeeNumber position')
      .populate({
        path: 'employeeId',
        populate: {
          path: 'departmentId',
          select: 'name'
        }
      })
      .lean();
    if (payrollRecords.length === 0) {
      return NextResponse.json(
        { error: 'No payroll records found for this payroll run' },
        { status: 404 }
      );
    }
    // Format data for export
    // Type assertion for the payroll records
    const exportData = payrollRecords.map((record: any) => {
      const employee = record.employeeId;
      const department = employee?.departmentId;
      return {
        'Employee Number': employee?.employeeNumber || '',
        'Employee Name': employee ? `${employee.firstName} ${employee.lastName}` : '',
        'Position': employee?.position || '',
        'Department': department?.name || '',
        'Basic Salary': record.basicSalary || 0,
        'Gross Salary': record.grossSalary || 0,
        'Total Deductions': record.totalDeductions || 0,
        'Tax Amount': record.taxAmount || 0,
        'Net Salary': record.netSalary || 0,
        'Payment Method': record.paymentMethod || '',
        'Bank Name': record.bankName || '',
        'Account Number': record.bankAccountNumber || '',
        'Payment Status': record.paymentStatus || 'pending'
      };
    });
    // Generate file based on format
    if (exportFormat === 'excel') {
      // Create workbook
      const workbook = XLSX.utils.book_new();
      // Create worksheet
      const worksheet = XLSX.utils.json_to_sheet(exportData);
      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Payroll Data');
      // Add summary worksheet
      const summaryData = [
        {
          'Payroll Run': payrollRun.name,
          'Period': `${format(new Date(payrollRun.payPeriod.startDate), 'MMM dd, yyyy')} - ${format(new Date(payrollRun.payPeriod.endDate), 'MMM dd, yyyy')}`,
          'Status': payrollRun.status,
          'Total Employees': payrollRun.totalEmployees || 0,
          'Total Gross Salary': payrollRun.totalGrossSalary || 0,
          'Total Deductions': payrollRun.totalDeductions || 0,
          'Total Tax': payrollRun.totalTax || 0,
          'Total Net Salary': payrollRun.totalNetSalary || 0,
          'Generated On': format(new Date(), 'MMM dd, yyyy HH:mm:ss'),
          'Generated By': `${currentUser.firstName} ${currentUser.lastName}` || currentUser.email
        }
      ];
      const summaryWorksheet = XLSX.utils.json_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, summaryWorksheet, 'Summary');
      // Write workbook to buffer
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      // Convert Buffer to Uint8Array for Response
      const uint8Array = new Uint8Array(buffer);
      // Return Excel file
      return new Response(uint8Array, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': `attachment; filename="payroll-run-${payrollRun.name.replace(/\s+/g, '-')}.xlsx"`
        }
      });
    } else if (exportFormat === 'pdf') {
      // Import the PDF Generator
      const { PdfGenerator } = await import('@/lib/backend/utils/pdf-generator');
      // Create PDF document
      const pdfGenerator = new PdfGenerator({
        title: `Payroll Run - ${payrollRun.name}`,
        author: 'TCM Enterprise Business Suite',
        subject: 'Payroll Run Export',
        keywords: ['payroll', 'export', 'report'],
        pageSize: 'A4',
        pageOrientation: 'landscape',
        margins: { top: 50, bottom: 50, left: 40, right: 40 },
        info: {
          Creator: 'TCM Enterprise Business Suite',
          Producer: 'TCM Payroll System'
        }
      });
      // Add header
      pdfGenerator.addHeader({
        title: 'TEACHERS COUNCIL OF MALAWI',
        subtitle: `Payroll Run: ${payrollRun.name}`,
        showLogo: true,
        logoPath: '/public/images/logo.png'
      });
      // Add payslip title
      pdfGenerator.addTitle(`Payroll Period: ${format(new Date(payrollRun.payPeriod.startDate), 'MMM dd, yyyy')} - ${format(new Date(payrollRun.payPeriod.endDate), 'MMM dd, yyyy')}`, {
        fontSize: 14,
        align: 'center',
        marginBottom: 1
      });
      // Add summary information
      pdfGenerator.addText(`Status: ${payrollRun.status}`, { fontSize: 12 });
      pdfGenerator.addText(`Total Employees: ${payrollRun.totalEmployees || 0}`, { fontSize: 12 });
      pdfGenerator.addText(`Total Gross Salary: MWK ${(payrollRun.totalGrossSalary || 0).toLocaleString()}`, { fontSize: 12 });
      pdfGenerator.addText(`Total Deductions: MWK ${(payrollRun.totalDeductions || 0).toLocaleString()}`, { fontSize: 12 });
      pdfGenerator.addText(`Total Tax: MWK ${(payrollRun.totalTax || 0).toLocaleString()}`, { fontSize: 12 });
      pdfGenerator.addText(`Total Net Salary: MWK ${(payrollRun.totalNetSalary || 0).toLocaleString()}`, { fontSize: 12 });
      // Add title for the table
      pdfGenerator.addTitle('Payroll Records', {
        fontSize: 14,
        align: 'center',
        marginBottom: 1
      });
      // Define table columns with proper align types
      const columns = [
        { header: 'Emp #', property: 'employeeNumber', width: 60, align: 'left' as const },
        { header: 'Name', property: 'name', width: 120, align: 'left' as const },
        { header: 'Position', property: 'position', width: 100, align: 'left' as const },
        { header: 'Department', property: 'department', width: 100, align: 'left' as const },
        { header: 'Basic Salary', property: 'basicSalary', width: 80, align: 'right' as const },
        { header: 'Gross Salary', property: 'grossSalary', width: 80, align: 'right' as const },
        { header: 'Deductions', property: 'deductions', width: 80, align: 'right' as const },
        { header: 'Tax', property: 'tax', width: 60, align: 'right' as const },
        { header: 'Net Salary', property: 'netSalary', width: 80, align: 'right' as const },
        { header: 'Status', property: 'status', width: 60, align: 'center' as const }
      ];
      // Format data for table
      const tableData = exportData.map(item => ({
        employeeNumber: item['Employee Number'],
        name: item['Employee Name'],
        position: item['Position'],
        department: item['Department'],
        basicSalary: `MWK ${item['Basic Salary'].toLocaleString()}`,
        grossSalary: `MWK ${item['Gross Salary'].toLocaleString()}`,
        deductions: `MWK ${item['Total Deductions'].toLocaleString()}`,
        tax: `MWK ${item['Tax Amount'].toLocaleString()}`,
        netSalary: `MWK ${item['Net Salary'].toLocaleString()}`,
        status: item['Payment Status']
      }));
      // Add table to PDF
      pdfGenerator.addTable(tableData, columns, {
        headerBgColor: '#f0f0f0',
        headerTextColor: '#000000',
        alternateRowBgColor: '#f9f9f9',
        fontSize: 9,
        headerFontSize: 10
      });
      // Add footer with page numbers
      pdfGenerator.addFooter({
        showPageNumber: true,
        showDate: true,
        customFooter: (doc) => {
          const footerText = `Generated on ${format(new Date(), 'MMM dd, yyyy HH:mm:ss')} by ${currentUser.firstName} ${currentUser.lastName || currentUser.email}`;
          const textWidth = doc.widthOfString(footerText);
          const textX = (doc.page.width - textWidth) / 2;
          doc.fontSize(8).text(footerText, textX, doc.page.height - 30);
        }
      });
      // Generate PDF
      const pdfBuffer = await pdfGenerator.generate();
      // Convert Buffer to Uint8Array for Response
      const uint8Array = new Uint8Array(pdfBuffer);
      // Return PDF file
      return new Response(uint8Array, {
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="payroll-run-${payrollRun.name.replace(/\s+/g, '-')}.pdf"`
        }
      });
    } else {
      return NextResponse.json(
        { error: 'Unsupported format. Supported formats: excel, pdf' },
        { status: 400 }
      );
    }
  } catch (error: unknown) {
    logger.error(`Error exporting payroll run ${payrollRunId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to export payroll run', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
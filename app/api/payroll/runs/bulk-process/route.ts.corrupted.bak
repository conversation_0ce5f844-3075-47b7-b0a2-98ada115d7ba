// app/api/payroll/runs/bulk-process/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { connectToDatabase } from '@/lib/backend/database'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import { UserRole } from '@/types/user-roles'
import logger, { LogCategory } from '@/lib/backend/utils/logger'
import PayrollRun from '@/models/payroll/PayrollRun'
import PayrollProcessingBatch from '@/models/payroll/PayrollProcessingBatch'
import { payrollService } from '@/lib/services/payroll/unified-payroll-service'
import mongoose from 'mongoose'

export const runtime = 'nodejs';



// Required roles for bulk processing
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.HR_DIRECTOR,
  UserRole.FINANCE_MANAGER
]

interface BulkProcessRequest {
  payrollRunIds: string[]
  batchSize?: number
  useBatch?: boolean
  notes?: string
  departments?: string[]
}

interface BulkProcessResult {
  success: boolean
  message: string
  data: {
    totalRuns: number
    processedRuns: number
    failedRuns: number
    skippedRuns: number
    batchId?: string
    results: Array<{
      payrollRunId: string
      payrollRunName: string
      status: 'processed' | 'failed' | 'skipped'
      message?: string
      error?: string
    }>
  }
}

/**
 * POST /api/payroll/runs/bulk-process
 * Process multiple payroll runs in bulk
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES)
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    await connectToDatabase()

    // Parse request body
    const body: BulkProcessRequest = await request.json()
    const { payrollRunIds, batchSize = 50, useBatch = true, notes, departments } = body

    // Validate request
    if (!payrollRunIds || !Array.isArray(payrollRunIds) || payrollRunIds.length === 0) {
      return NextResponse.json(
        { error: 'Payroll run IDs are required and must be a non-empty array' },
        { status: 400 }
      )
    }

    // Validate payroll run IDs
    const invalidIds = payrollRunIds.filter(id => !mongoose.Types.ObjectId.isValid(id))
    if (invalidIds.length > 0) {
      return NextResponse.json(
        { error: `Invalid payroll run IDs: ${invalidIds.join(', ')}` },
        { status: 400 }
      )
    }

    // Get payroll runs to process
    const query: any = {
      _id: { $in: payrollRunIds.map(id => new mongoose.Types.ObjectId(id)) },
      status: 'draft'
    }

    // Filter by departments if specified
    if (departments && departments.length > 0) {
      query.departments = { $in: departments }
    }

    const payrollRuns = await PayrollRun.find(query)

    if (payrollRuns.length === 0) {
      return NextResponse.json(
        { error: 'No eligible payroll runs found for processing (must be in draft status)' },
        { status: 404 }
      )
    }

    // Check for runs that are not in draft status
    const allRuns = await PayrollRun.find({
      _id: { $in: payrollRunIds.map(id => new mongoose.Types.ObjectId(id)) }
    })

    const nonDraftRuns = allRuns.filter(run => run.status !== 'draft')
    const skippedRuns = nonDraftRuns.map(run => ({
      payrollRunId: run._id.toString(),
      payrollRunName: run.name,
      status: 'skipped' as const,
      message: `Payroll run is in ${run.status} status, not draft`
    }))

    logger.info('Starting bulk payroll processing', LogCategory.PAYROLL, {
      userId: user.id,
      totalRuns: payrollRunIds.length,
      eligibleRuns: payrollRuns.length,
      skippedRuns: skippedRuns.length,
      useBatch,
      batchSize
    })

    if (useBatch) {
      // Create a bulk processing batch
      const batch = new PayrollProcessingBatch({
        payrollRunId: payrollRuns[0]._id, // Use first run as reference
        status: 'pending',
        totalEmployees: payrollRuns.reduce((sum, run) => sum + run.totalEmployees, 0),
        processedEmployees: 0,
        startedAt: new Date(),
        createdBy: user.id,
        updatedBy: user.id
      })

      await batch.save()

      // Start bulk processing in the background
      const batchId = batch._id.toString()
      processBulkRuns(batchId, payrollRuns, user.id, notes, batchSize).catch(error => {
        logger.error('Error in bulk payroll processing', LogCategory.PAYROLL, error)
      })

      const result: BulkProcessResult = {
        success: true,
        message: `Bulk payroll processing started for ${payrollRuns.length} runs`,
        data: {
          totalRuns: payrollRunIds.length,
          processedRuns: 0,
          failedRuns: 0,
          skippedRuns: skippedRuns.length,
          batchId,
          results: skippedRuns
        }
      }

      return NextResponse.json(result)
    } else {
      // Process synchronously (not recommended for large batches)
      const results = []
      let processedRuns = 0
      let failedRuns = 0

      for (const run of payrollRuns) {
        try {
          await payrollService.processPayrollRun(run._id.toString(), {
            userId: user.id,
            notes
          })

          results.push({
            payrollRunId: run._id.toString(),
            payrollRunName: run.name,
            status: 'processed' as const,
            message: 'Payroll run processed successfully'
          })
          processedRuns++
        } catch (error) {
          results.push({
            payrollRunId: run._id.toString(),
            payrollRunName: run.name,
            status: 'failed' as const,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
          failedRuns++
        }
      }

      const result: BulkProcessResult = {
        success: true,
        message: `Bulk processing completed: ${processedRuns} processed, ${failedRuns} failed, ${skippedRuns.length} skipped`,
        data: {
          totalRuns: payrollRunIds.length,
          processedRuns,
          failedRuns,
          skippedRuns: skippedRuns.length,
          results: [...results, ...skippedRuns]
        }
      }

      return NextResponse.json(result)
    }

  } catch (error) {
    logger.error('Error in bulk payroll processing', LogCategory.API, error)
    return NextResponse.json(
      { error: 'Failed to process payroll runs in bulk', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

/**
 * Process multiple payroll runs in the background
 */
async function processBulkRuns(
  batchId: string,
  payrollRuns: any[],
  userId: string,
  notes?: string,
  batchSize: number = 50
) {
  try {
    await connectToDatabase()

    // Get the batch
    const batch = await PayrollProcessingBatch.findById(batchId)
    if (!batch) {
      throw new Error(`Batch ${batchId} not found`)
    }

    // Update batch status
    batch.status = 'processing'
    await batch.save()

    const results = []
    let processedRuns = 0
    let failedRuns = 0

    for (const run of payrollRuns) {
      try {
        // Update current processing info
        batch.currentEmployee = `Processing: ${run.name}`
        await batch.save()

        // Process the payroll run
        await payrollService.processPayrollRun(run._id.toString(), {
          userId,
          notes: notes || `Bulk processed on ${new Date().toLocaleString()}`
        })

        results.push({
          payrollRunId: run._id.toString(),
          payrollRunName: run.name,
          status: 'processed',
          message: 'Payroll run processed successfully'
        })
        processedRuns++

        logger.info(`Bulk processing: Completed payroll run ${run.name}`, LogCategory.PAYROLL, {
          batchId,
          payrollRunId: run._id.toString(),
          processedRuns,
          totalRuns: payrollRuns.length
        })

      } catch (error) {
        results.push({
          payrollRunId: run._id.toString(),
          payrollRunName: run.name,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
        failedRuns++

        logger.error(`Bulk processing: Failed to process payroll run ${run.name}`, LogCategory.PAYROLL, error)
      }

      // Update batch progress
      batch.processedEmployees = processedRuns
      await batch.save()
    }

    // Complete the batch
    batch.status = 'completed'
    batch.completedAt = new Date()
    batch.currentEmployee = `Completed: ${processedRuns} processed, ${failedRuns} failed`
    await batch.save()

    logger.info('Bulk payroll processing completed', LogCategory.PAYROLL, {
      batchId,
      processedRuns,
      failedRuns,
      totalRuns: payrollRuns.length
    })

  } catch (error) {
    logger.error('Error in bulk payroll processing', LogCategory.PAYROLL, error)

    try {
      // Update batch status to error
      const batch = await PayrollProcessingBatch.findById(batchId)
      if (batch) {
        batch.status = 'error'
        batch.error = error instanceof Error ? error.message : 'Unknown error'
        batch.updatedBy = new mongoose.Types.ObjectId(userId)
        await batch.save()
      }
    } catch (updateError) {
      logger.error('Error updating batch status', LogCategory.PAYROLL, updateError)
    }
  }
}

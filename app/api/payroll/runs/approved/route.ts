import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { PayrollRun } from '@/models/payroll/PayrollRun';
import PayrollRecord from '@/models/payroll/PayrollRecord';
import { Employee } from '@/models/Employee';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import { UserRole } from '@/types/user-roles';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';

export const runtime = 'nodejs';

/**
 * Get approved payroll runs from database
 */
async function getApprovedPayrollRuns(options: {
  limit?: number;
  includeVoucherCreated?: boolean;
  month?: number;
  year?: number;
}) {
  await connectToDatabase();
  const { limit = 12, includeVoucherCreated = false, month, year } = options;
  // Build query for approved payroll runs
  const query: any = { status: 'approved' };
  // Filter by specific month/year if provided
  if (month && year) {
    query['payPeriod.month'] = month;
    query['payPeriod.year'] = year;
  }
  // Filter by voucher status if needed
  if (!includeVoucherCreated) {
    query.$or = [
      { voucherId: { $exists: false } },
      { voucherId: null },
      { voucherStatus: 'not_created' }
    ];
  }
  const payrollRuns = await PayrollRun.find(query)
    .populate('createdBy', 'name email')
    .populate('approvedBy', 'name email')
    .sort({ 'payPeriod.year': -1, 'payPeriod.month': -1 })
    .limit(limit)
    .lean();
  return payrollRuns;
}
/**
 * Get approved payroll runs
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const allowedRoles = ['HR_MANAGER', 'FINANCE_MANAGER', 'SUPER_ADMIN', 'SYSTEM_ADMIN', 'ACCOUNTANT'];
    if (!allowedRoles.includes(user.role)) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to view payroll runs' },
        { status: 403 }
      );
    }
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '12');
    const includeVoucherCreated = searchParams.get('includeVoucherCreated') === 'true';
    // Get payroll runs from database
    const payrollRuns = await getApprovedPayrollRuns({
      limit,
      includeVoucherCreated
    });
    // Transform data for response
    const transformedRuns = payrollRuns.map(run => ({
      id: run._id.toString(),
      name: run.name,
      description: run.description,
      month: run.payPeriod.month,
      year: run.payPeriod.year,
      status: run.status,
      totalEmployees: run.totalEmployees,
      processedEmployees: run.processedEmployees,
      totalGrossSalary: run.totalGrossSalary,
      totalDeductions: run.totalDeductions,
      totalTax: run.totalTax,
      totalNetSalary: run.totalNetSalary,
      currency: run.currency,
      payPeriod: run.payPeriod,
      approvedAt: run.approvedAt,
      approvedBy: run.approvedBy,
      createdAt: run.createdAt,
      voucherStatus: run.voucherStatus || 'not_created',
      voucherId: run.voucherId || null
    }));
    // Calculate summary statistics
    const summary = {
      totalRuns: transformedRuns.length,
      totalAmount: transformedRuns.reduce((sum, run) => sum + run.totalNetSalary, 0),
      totalEmployees: transformedRuns.reduce((sum, run) => sum + run.totalEmployees, 0),
      oldestRun: transformedRuns.length > 0 ?
        transformedRuns.reduce((oldest, run) =>
          new Date(run.approvedAt) < new Date(oldest.approvedAt) ? run : oldest
        ) : null
    };
    return NextResponse.json({
      success: true,
      data: {
        payrollRuns: transformedRuns,
        summary,
        userPermissions: {
          canCreateVouchers: true,
          canViewDetails: true,
          canApprove: ['SUPER_ADMIN', 'SYSTEM_ADMIN'].includes(user.role)
        }
      }
    });
  } catch (error: any) {
    console.error('Error getting approved payroll runs:', error);
    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: error.message
      },
      { status: 500 }
    );
  }
}
/**
 * Get specific payroll run by month and year with employee records
 * Enhanced with structured error handling for voucher creation
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'AUTH_REQUIRED',
        'Authentication required',
        'You must be logged in to access payroll data.',
        {
          userId: undefined,
          endpoint: '/api/payroll/runs/approved',
          method: 'POST'
        },
        401,
        ErrorSeverity.MEDIUM,
        'No valid authentication token provided.',
        ['Please log in to your account', 'Contact support if login issues persist'],
        [
          {
            label: 'Login',
            action: 'login',
            type: 'link',
            variant: 'primary',
            url: '/auth/login'
          }
        ]
      );
    }
    // Check permissions using proper role checking
    const allowedRoles = [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_SPECIALIST
    ];
    if (!hasRequiredPermissions(user, allowedRoles)) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'INSUFFICIENT_PERMISSIONS',
        'Insufficient permissions to access payroll data',
        'You do not have permission to view payroll runs for voucher creation.',
        {
          userId: user._id?.toString(),
          userRole: user.role,
          endpoint: '/api/payroll/runs/approved',
          method: 'POST'
        },
        403,
        ErrorSeverity.MEDIUM,
        `User role '${user.role}' is not authorized for payroll data access.`,
        ['Contact HR to request appropriate permissions', 'Ensure you have the correct role assigned'],
        [
          {
            label: 'Contact HR',
            action: 'contact-hr',
            type: 'link',
            variant: 'primary',
            url: '/dashboard/support'
          }
        ]
      );
    }
    // Parse request body
    const body = await request.json();
    const { month, year, includeEmployees = false } = body;
    if (!month || !year) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'MISSING_REQUIRED_FIELDS',
        'Month and year are required',
        'Please provide both month and year to search for payroll runs.',
        {
          userId: user._id?.toString(),
          endpoint: '/api/payroll/runs/approved',
          method: 'POST',
          additionalData: { providedMonth: month, providedYear: year }
        },
        400,
        ErrorSeverity.LOW,
        'Request body must include valid month and year parameters.',
        ['Ensure both month (1-12) and year (YYYY) are provided', 'Check the request format and try again'],
        [
          {
            label: 'Retry',
            action: 'retry',
            type: 'button',
            variant: 'primary'
          }
        ]
      );
    }
    // Get payroll runs for the specified period
    const payrollRuns = await getApprovedPayrollRuns({
      month: parseInt(month),
      year: parseInt(year),
      includeVoucherCreated: true
    });
    // Check if any payroll run exists for this period
    if (payrollRuns.length === 0) {
      // First check if ANY payroll run exists for this period (not just approved ones)
      const anyPayrollRuns = await PayrollRun.find({
        'payPeriod.month': parseInt(month),
        'payPeriod.year': parseInt(year)
      }).lean();
      if (anyPayrollRuns.length === 0) {
        // No payroll run exists at all for this period
        return errorService.createApiResponse(
          ErrorType.NOT_FOUND,
          'PAYROLL_RUN_NOT_FOUND',
          `No payroll run found for ${month}/${year}`,
          `No payroll run was found for the selected period. A payroll run must be created and approved before creating a voucher.`,
          {
            userId: user._id?.toString(),
            endpoint: '/api/payroll/runs/approved',
            method: 'POST',
            additionalData: { month, year, searchType: 'voucher_creation' }
          },
          404,
          ErrorSeverity.MEDIUM,
          `No payroll run exists for period ${month}/${year}. This usually means no payroll was processed for this period.`,
          [
            'Create a payroll run for this period first',
            'Select a different month with an existing payroll run',
            'Contact HR to verify payroll processing for this period'
          ],
          [
            {
              label: 'Create Payroll Run',
              action: 'create-payroll-run',
              type: 'link',
              variant: 'primary',
              url: '/dashboard/payroll/run/create'
            },
            {
              label: 'View All Payroll Runs',
              action: 'view-payroll-runs',
              type: 'link',
              variant: 'secondary',
              url: '/dashboard/payroll/run'
            }
          ]
        );
      } else {
        // Payroll run exists but is not approved
        const existingRun = anyPayrollRuns[0];
        return errorService.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'PAYROLL_NOT_APPROVED',
          `Payroll run for ${month}/${year} is not approved`,
          `The payroll run for the selected period has not been approved yet and cannot be used for voucher creation.`,
          {
            userId: user._id?.toString(),
            endpoint: '/api/payroll/runs/approved',
            method: 'POST',
            additionalData: {
              month,
              year,
              payrollRunId: existingRun._id,
              currentStatus: existingRun.status,
              searchType: 'voucher_creation'
            }
          },
          400,
          ErrorSeverity.MEDIUM,
          `Payroll run status: ${existingRun.status}. Only approved payroll runs can be used for voucher creation.`,
          [
            'Wait for the payroll run to be approved by HR',
            'Contact HR to approve the payroll run',
            'Select a different month with an approved payroll run'
          ],
          [
            {
              label: 'View Payroll Run',
              action: 'view-payroll-run',
              type: 'link',
              variant: 'primary',
              url: `/dashboard/payroll/run/${existingRun._id}`
            },
            {
              label: 'Contact HR',
              action: 'contact-hr',
              type: 'link',
              variant: 'secondary',
              url: '/dashboard/support'
            }
          ]
        );
      }
    }
    // Get the most recent payroll run for the period
    const payrollRun = payrollRuns[0];
    // Check if voucher already exists for this payroll run
    if (payrollRun.voucherStatus === 'created' || payrollRun.voucherId) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'VOUCHER_ALREADY_EXISTS',
        `Voucher already exists for ${month}/${year} payroll`,
        `A voucher has already been created for this payroll run and cannot be created again.`,
        {
          userId: user._id?.toString(),
          endpoint: '/api/payroll/runs/approved',
          method: 'POST',
          additionalData: {
            month,
            year,
            payrollRunId: payrollRun._id,
            existingVoucherId: payrollRun.voucherId,
            voucherStatus: payrollRun.voucherStatus,
            searchType: 'voucher_creation'
          }
        },
        409,
        ErrorSeverity.MEDIUM,
        `Voucher ID: ${payrollRun.voucherId || 'Unknown'}. Each payroll run can only have one voucher.`,
        [
          'Select a different month without an existing voucher',
          'View the existing voucher details',
          'Contact finance team if you need to modify the existing voucher'
        ],
        [
          {
            label: 'View Existing Voucher',
            action: 'view-voucher',
            type: 'link',
            variant: 'primary',
            url: `/dashboard/accounting/vouchers/management?search=${payrollRun.voucherId}`
          },
          {
            label: 'View All Vouchers',
            action: 'view-vouchers',
            type: 'link',
            variant: 'secondary',
            url: '/dashboard/accounting/vouchers/management'
          }
        ]
      );
    }
    let employeeRecords = [];
    if (includeEmployees) {
      try {
        // Get payroll records for this run
        const records = await PayrollRecord.find({ payrollRunId: payrollRun._id })
          .populate({
            path: 'employeeId',
            select: 'firstName lastName employeeNumber email departmentId position',
            populate: [
              { path: 'departmentId', select: 'name' }
            ]
          })
          .sort({ 'employeeId.lastName': 1, 'employeeId.firstName': 1 })
          .lean();
        if (records.length === 0) {
          return errorService.createApiResponse(
            ErrorType.NOT_FOUND,
            'NO_EMPLOYEE_RECORDS',
            `No employee records found for payroll run`,
            `This payroll run has no individual employee records. This usually happens when processing failed or was incomplete.`,
            {
              userId: user._id?.toString(),
              endpoint: '/api/payroll/runs/approved',
              method: 'POST',
              additionalData: {
                month,
                year,
                payrollRunId: payrollRun._id,
                searchType: 'voucher_creation'
              }
            },
            404,
            ErrorSeverity.HIGH,
            'Payroll run exists but contains no employee records.',
            [
              'Contact HR to verify payroll processing',
              'Check if payroll run needs to be reprocessed',
              'Select a different payroll run with employee records'
            ],
            [
              {
                label: 'View Payroll Run',
                action: 'view-payroll-run',
                type: 'link',
                variant: 'primary',
                url: `/dashboard/payroll/run/${payrollRun._id}`
              },
              {
                label: 'Contact HR',
                action: 'contact-hr',
                type: 'link',
                variant: 'secondary',
                url: '/dashboard/support'
              }
            ]
          );
        }
        employeeRecords = records.map(record => ({
          _id: record._id.toString(),
          employee: {
            _id: record.employeeId._id.toString(),
            firstName: record.employeeId.firstName || '',
            lastName: record.employeeId.lastName || '',
            fullName: `${record.employeeId.firstName || ''} ${record.employeeId.lastName || ''}`.trim() || 'Unknown Employee',
            employeeNumber: record.employeeId.employeeNumber || 'N/A',
            email: record.employeeId.email || '',
            department: record.employeeId.departmentId?.name || 'Unknown',
            position: record.employeeId.position || 'Unknown'
          },
          payrollData: {
            grossSalary: record.grossSalary,
            totalDeductions: record.totalDeductions,
            totalTax: record.totalTax,
            netSalary: record.netSalary,
            currency: record.currency,
            paymentMethod: record.paymentMethod,
            bankAccount: record.bankAccount,
            status: record.status,
            components: record.components
          }
        }));
      } catch (employeeError) {
        return errorService.createApiResponse(
          ErrorType.DATABASE,
          'EMPLOYEE_RECORDS_ERROR',
          'Failed to fetch employee records',
          'Unable to retrieve employee records for this payroll run. Please try again.',
          {
            userId: user._id?.toString(),
            endpoint: '/api/payroll/runs/approved',
            method: 'POST',
            additionalData: {
              month,
              year,
              payrollRunId: payrollRun._id,
              error: employeeError instanceof Error ? employeeError.message : 'Unknown error',
              searchType: 'voucher_creation'
            }
          },
          500,
          ErrorSeverity.HIGH,
          `Database error while fetching employee records: ${employeeError instanceof Error ? employeeError.message : 'Unknown error'}`,
          [
            'Try refreshing the page and selecting the month again',
            'Contact support if the problem persists',
            'Check your internet connection'
          ],
          [
            {
              label: 'Retry',
              action: 'retry',
              type: 'button',
              variant: 'primary'
            },
            {
              label: 'Contact Support',
              action: 'contact-support',
              type: 'link',
              variant: 'secondary',
              url: '/dashboard/support'
            }
          ]
        );
      }
    }
    // Transform payroll run data
    const transformedRun = {
      id: payrollRun._id.toString(),
      name: payrollRun.name,
      description: payrollRun.description,
      month: payrollRun.payPeriod.month,
      year: payrollRun.payPeriod.year,
      status: payrollRun.status,
      totalEmployees: payrollRun.totalEmployees,
      processedEmployees: payrollRun.processedEmployees,
      totalGrossSalary: payrollRun.totalGrossSalary,
      totalDeductions: payrollRun.totalDeductions,
      totalTax: payrollRun.totalTax,
      totalNetSalary: payrollRun.totalNetSalary,
      currency: payrollRun.currency,
      payPeriod: payrollRun.payPeriod,
      approvedAt: payrollRun.approvedAt,
      approvedBy: payrollRun.approvedBy,
      createdAt: payrollRun.createdAt,
      voucherStatus: payrollRun.voucherStatus || 'not_created',
      voucherId: payrollRun.voucherId || null
    };
    return NextResponse.json({
      success: true,
      data: {
        payrollRun: transformedRun,
        employeeRecords: includeEmployees ? employeeRecords : [],
        canCreateVoucher: (payrollRun.voucherStatus || 'not_created') === 'not_created',
        summary: includeEmployees ? {
          totalEmployees: employeeRecords.length,
          totalGrossSalary: employeeRecords.reduce((sum, emp) => sum + emp.payrollData.grossSalary, 0),
          totalNetSalary: employeeRecords.reduce((sum, emp) => sum + emp.payrollData.netSalary, 0),
          totalDeductions: employeeRecords.reduce((sum, emp) => sum + emp.payrollData.totalDeductions, 0),
          totalTax: employeeRecords.reduce((sum, emp) => sum + emp.payrollData.totalTax, 0)
        } : null
      }
    });
  } catch (error: any) {
    console.error('Error getting payroll run by period:', error);
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'INTERNAL_SERVER_ERROR',
      'Internal server error occurred',
      'An unexpected error occurred while processing your request. Please try again.',
      {
        userId: 'unknown',
        endpoint: '/api/payroll/runs/approved',
        method: 'POST',
        additionalData: {
          error: error.message,
          stack: error.stack,
          operation: 'fetch_payroll_run_for_voucher'
        }
      },
      500,
      ErrorSeverity.CRITICAL,
      `Unexpected error in payroll run fetch: ${error.message}`,
      [
        'Try refreshing the page and attempting the operation again',
        'Check your internet connection',
        'Contact support if the problem persists'
      ],
      [
        {
          label: 'Retry',
          action: 'retry',
          type: 'button',
          variant: 'primary'
        },
        {
          label: 'Contact Support',
          action: 'contact-support',
          type: 'link',
          variant: 'secondary',
          url: '/dashboard/support'
        }
      ]
    );
  }
}
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { payrollAccountingService } from '@/lib/services/payroll/payroll-accounting-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';
import { z } from 'zod';

// Schema for bulk department allocation
const bulkDepartmentAllocationSchema = z.object({
  payrollRunIds: z.array(z.string().min(1, 'Payroll run ID is required')).min(1, 'At least one payroll run ID is required'),
  options: z.object({
    autoPost: z.boolean().optional().default(false),
    batchSize: z.number().min(1).max(50).optional().default(10),
    includeSubDepartments: z.boolean().optional().default(false),
    allocationMethod: z.enum(['proportional', 'equal', 'custom']).optional().default('proportional'),
  }).optional().default({})
});

// Schema for bulk cost center mapping
const bulkCostCenterMappingSchema = z.object({
  payrollRunIds: z.array(z.string().min(1, 'Payroll run ID is required')).min(1, 'At least one payroll run ID is required'),
  costCenterMappings: z.array(z.object({
    departmentId: z.string().min(1, 'Department ID is required'),
    costCenterId: z.string().min(1, 'Cost center ID is required'),
    allocationPercentage: z.number().min(0).max(100).optional().default(100),
  })).optional(),
  options: z.object({
    autoPost: z.boolean().optional().default(false),
    batchSize: z.number().min(1).max(50).optional().default(10),
    validateBudgetLimits: z.boolean().optional().default(true),
  }).optional().default({})
});

export const runtime = 'nodejs';

/**
 * POST /api/payroll/accounting/bulk-allocations
 * Handle bulk allocation operations for payroll accounting integration
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to perform bulk allocations' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();
    
    // Get request body
    const body = await request.json();
    
    // Get action
    const { action } = body;
    
    if (!action) {
      return NextResponse.json({ error: 'Action is required' }, { status: 400 });
    }
    
    // Process based on action
    switch (action) {
      case 'bulk_department_allocation':
        return handleBulkDepartmentAllocation(body, user.id);
      
      case 'bulk_cost_center_mapping':
        return handleBulkCostCenterMapping(body, user.id);
      
      case 'bulk_combined_allocation':
        return handleBulkCombinedAllocation(body, user.id);
      
      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: bulk_department_allocation, bulk_cost_center_mapping, bulk_combined_allocation' },
          { status: 400 }
        );
    }
  } catch (error: unknown) {
    logger.error('Error in bulk allocations API', LogCategory.PAYROLL, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * Handle bulk department allocation
 * @param body - Request body
 * @param userId - User ID
 * @returns Next.js response
 */
async function handleBulkDepartmentAllocation(body: unknown, userId: string) {
  try {
    // Validate request body
    const validationResult = bulkDepartmentAllocationSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }
    
    const { payrollRunIds, options } = validationResult.data;
    
    logger.info('Starting bulk department allocation', LogCategory.PAYROLL, {
      payrollRunIds,
      options,
      userId
    });
    
    // Process payroll runs in batches
    const results = [];
    const errors = [];
    const batchSize = options.batchSize || 10;
    
    for (let i = 0; i < payrollRunIds.length; i += batchSize) {
      const batch = payrollRunIds.slice(i, i + batchSize);
      
      logger.info(`Processing department allocation batch ${Math.floor(i / batchSize) + 1}`, LogCategory.PAYROLL, {
        batchSize: batch.length,
        totalBatches: Math.ceil(payrollRunIds.length / batchSize)
      });
      
      // Process each payroll run in the batch
      for (const payrollRunId of batch) {
        try {
          // Create department allocation entries
          const allocationEntries = await payrollAccountingService.createDepartmentAllocationEntries(
            payrollRunId,
            userId
          );
          
          results.push({
            payrollRunId,
            allocationEntries,
            status: 'success',
            entriesCount: allocationEntries?.length || 0
          });
          
          // Auto-post if requested
          if (options.autoPost && allocationEntries) {
            for (const entry of allocationEntries) {
              if (entry._id) {
                await payrollAccountingService.postJournalEntryPublic(entry._id, userId);
              }
            }
          }
          
        } catch (error) {
          logger.error(`Error processing department allocation for payroll run ${payrollRunId}`, LogCategory.PAYROLL, error);
          errors.push({
            payrollRunId,
            error: error instanceof Error ? error.message : 'Unknown error',
            status: 'error'
          });
        }
      }
      
      // Add a small delay between batches
      if (i + batchSize < payrollRunIds.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    const summary = {
      total: payrollRunIds.length,
      successful: results.length,
      failed: errors.length,
      totalEntriesCreated: results.reduce((sum, r) => sum + r.entriesCount, 0),
      options,
      results,
      errors
    };
    
    logger.info('Bulk department allocation completed', LogCategory.PAYROLL, summary);
    
    return NextResponse.json({
      success: true,
      message: `Bulk department allocation completed. ${summary.successful} successful, ${summary.failed} failed, ${summary.totalEntriesCreated} entries created.`,
      data: summary
    });
    
  } catch (error: unknown) {
    logger.error('Error in bulk department allocation', LogCategory.PAYROLL, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * Handle bulk cost center mapping
 * @param body - Request body
 * @param userId - User ID
 * @returns Next.js response
 */
async function handleBulkCostCenterMapping(body: unknown, userId: string) {
  try {
    // Validate request body
    const validationResult = bulkCostCenterMappingSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }
    
    const { payrollRunIds, costCenterMappings, options } = validationResult.data;
    
    logger.info('Starting bulk cost center mapping', LogCategory.PAYROLL, {
      payrollRunIds,
      costCenterMappings,
      options,
      userId
    });
    
    // Process payroll runs in batches
    const results = [];
    const errors = [];
    const batchSize = options.batchSize || 10;
    
    for (let i = 0; i < payrollRunIds.length; i += batchSize) {
      const batch = payrollRunIds.slice(i, i + batchSize);
      
      // Process each payroll run in the batch
      for (const payrollRunId of batch) {
        try {
          // Create cost center mapping entries
          const mappingEntries = await payrollAccountingService.createCostCenterMappingEntries(
            payrollRunId,
            userId,
            costCenterMappings
          );
          
          results.push({
            payrollRunId,
            mappingEntries,
            status: 'success',
            entriesCount: mappingEntries?.length || 0
          });
          
          // Auto-post if requested
          if (options.autoPost && mappingEntries) {
            for (const entry of mappingEntries) {
              if (entry._id) {
                await payrollAccountingService.postJournalEntryPublic(entry._id, userId);
              }
            }
          }
          
        } catch (error) {
          logger.error(`Error processing cost center mapping for payroll run ${payrollRunId}`, LogCategory.PAYROLL, error);
          errors.push({
            payrollRunId,
            error: error instanceof Error ? error.message : 'Unknown error',
            status: 'error'
          });
        }
      }
      
      // Add a small delay between batches
      if (i + batchSize < payrollRunIds.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    const summary = {
      total: payrollRunIds.length,
      successful: results.length,
      failed: errors.length,
      totalEntriesCreated: results.reduce((sum, r) => sum + r.entriesCount, 0),
      costCenterMappings,
      options,
      results,
      errors
    };
    
    logger.info('Bulk cost center mapping completed', LogCategory.PAYROLL, summary);
    
    return NextResponse.json({
      success: true,
      message: `Bulk cost center mapping completed. ${summary.successful} successful, ${summary.failed} failed, ${summary.totalEntriesCreated} entries created.`,
      data: summary
    });
    
  } catch (error: unknown) {
    logger.error('Error in bulk cost center mapping', LogCategory.PAYROLL, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * Handle bulk combined allocation (department + cost center)
 * @param body - Request body
 * @param userId - User ID
 * @returns Next.js response
 */
async function handleBulkCombinedAllocation(body: unknown, userId: string) {
  try {
    // Combined schema
    const combinedSchema = z.object({
      payrollRunIds: z.array(z.string().min(1, 'Payroll run ID is required')).min(1, 'At least one payroll run ID is required'),
      costCenterMappings: z.array(z.object({
        departmentId: z.string().min(1, 'Department ID is required'),
        costCenterId: z.string().min(1, 'Cost center ID is required'),
        allocationPercentage: z.number().min(0).max(100).optional().default(100),
      })).optional(),
      options: z.object({
        autoPost: z.boolean().optional().default(false),
        batchSize: z.number().min(1).max(50).optional().default(10),
        includeSubDepartments: z.boolean().optional().default(false),
        validateBudgetLimits: z.boolean().optional().default(true),
      }).optional().default({})
    });

    // Validate request body
    const validationResult = combinedSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }
    
    const { payrollRunIds, costCenterMappings, options } = validationResult.data;
    
    logger.info('Starting bulk combined allocation', LogCategory.PAYROLL, {
      payrollRunIds,
      costCenterMappings,
      options,
      userId
    });
    
    const combinedResults = {
      departmentAllocations: [],
      costCenterMappings: [],
      errors: []
    };
    
    // Process each payroll run
    for (const payrollRunId of payrollRunIds) {
      try {
        // Create department allocation entries
        const departmentEntries = await payrollAccountingService.createDepartmentAllocationEntries(
          payrollRunId,
          userId
        );
        
        combinedResults.departmentAllocations.push({
          payrollRunId,
          entries: departmentEntries,
          status: 'success'
        });
        
        // Create cost center mapping entries if mappings provided
        if (costCenterMappings && costCenterMappings.length > 0) {
          const costCenterEntries = await payrollAccountingService.createCostCenterMappingEntries(
            payrollRunId,
            userId,
            costCenterMappings
          );
          
          combinedResults.costCenterMappings.push({
            payrollRunId,
            entries: costCenterEntries,
            status: 'success'
          });
        }
        
        // Auto-post if requested
        if (options.autoPost) {
          // Post department entries
          if (departmentEntries) {
            for (const entry of departmentEntries) {
              if (entry._id) {
                await payrollAccountingService.postJournalEntryPublic(entry._id, userId);
              }
            }
          }

          // Post cost center entries
          if (costCenterMappings && costCenterMappings.length > 0) {
            const costCenterEntries = combinedResults.costCenterMappings.find(
              r => r.payrollRunId === payrollRunId
            )?.entries;

            if (costCenterEntries) {
              for (const entry of costCenterEntries) {
                if (entry._id) {
                  await payrollAccountingService.postJournalEntryPublic(entry._id, userId);
                }
              }
            }
          }
        }
        
      } catch (error) {
        logger.error(`Error processing combined allocation for payroll run ${payrollRunId}`, LogCategory.PAYROLL, error);
        combinedResults.errors.push({
          payrollRunId,
          error: error instanceof Error ? error.message : 'Unknown error',
          status: 'error'
        });
      }
    }
    
    const summary = {
      total: payrollRunIds.length,
      departmentAllocationsCreated: combinedResults.departmentAllocations.length,
      costCenterMappingsCreated: combinedResults.costCenterMappings.length,
      errors: combinedResults.errors.length,
      options,
      results: combinedResults
    };
    
    logger.info('Bulk combined allocation completed', LogCategory.PAYROLL, summary);
    
    return NextResponse.json({
      success: true,
      message: `Bulk combined allocation completed. ${summary.departmentAllocationsCreated} department allocations, ${summary.costCenterMappingsCreated} cost center mappings, ${summary.errors} errors.`,
      data: summary
    });
    
  } catch (error: unknown) {
    logger.error('Error in bulk combined allocation', LogCategory.PAYROLL, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

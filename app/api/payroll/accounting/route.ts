import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { unifiedPayrollService } from '@/lib/services/payroll/unified-payroll-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';
import { z } from 'zod';

export const runtime = 'nodejs';

// Custom auth system doesn't require authOptions;
// Removed deprecated payroll-accounting-service import - using unified payroll service
// Schema for creating journal entries
const createJournalEntriesSchema = z.object({
  payrollRunId: z.string().min(1, 'Payroll run ID is required'),
});
// Schema for posting journal entries
const postJournalEntriesSchema = z.object({
  payrollRunId: z.string().min(1, 'Payroll run ID is required'),
});
// Schema for creating payment journal entries
const createPaymentJournalEntriesSchema = z.object({
  payrollRunId: z.string().min(1, 'Payroll run ID is required'),
  bankAccountId: z.string().min(1, 'Bank account ID is required'),
  paymentDate: z.string().refine(
    (value) => !isNaN(Date.parse(value)),
    { message: 'Invalid payment date' }
  ),
});
/**
 * POST handler for payroll accounting integration
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();
    // Get session
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_MANAGER
    ]);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    // Get request body
    const body = await request.json();
    // Get action
    const { action } = body;
    if (!action) {
      return NextResponse.json({ error: 'Action is required' }, { status: 400 });
    }
    // Process based on action
    switch (action) {
      case 'create_journal_entries':
        return handleCreateJournalEntries(body, user.id);
      case 'post_journal_entries':
        return handlePostJournalEntries(body, user.id);
      case 'create_payment_journal_entries':
        return handleCreatePaymentJournalEntries(body, user.id);
      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: create_journal_entries, post_journal_entries, create_payment_journal_entries' },
          { status: 400 }
        );
    }
  } catch (error: unknown) {
    logger.error('Error in payroll accounting integration handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}
/**
 * Handle creating journal entries
 * @param body - Request body
 * @param userId - User ID
 * @returns Next.js response
 */
async function handleCreateJournalEntries(body: unknown, userId: string) {
  try {
    // Validate request body
    const validationResult = createJournalEntriesSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }
    const { payrollRunId } = validationResult.data;
    // Create journal entries
    const journalEntry = await payrollAccountingService.createPayrollJournalEntries(
      payrollRunId,
      userId
    );
    return NextResponse.json({
      success: true,
      message: 'Journal entries created successfully',
      data: journalEntry
    });
  } catch (error: unknown) {
    logger.error('Error creating journal entries', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}
/**
 * Handle posting journal entries
 * @param body - Request body
 * @param userId - User ID
 * @returns Next.js response
 */
async function handlePostJournalEntries(body: unknown, userId: string) {
  try {
    // Validate request body
    const validationResult = postJournalEntriesSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }
    const { payrollRunId } = validationResult.data;
    // Post journal entries
    const journalEntry = await payrollAccountingService.postPayrollJournalEntries(
      payrollRunId,
      userId
    );
    return NextResponse.json({
      success: true,
      message: 'Journal entries posted successfully',
      data: journalEntry
    });
  } catch (error: unknown) {
    logger.error('Error posting journal entries', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}
/**
 * Handle creating payment journal entries
 * @param body - Request body
 * @param userId - User ID
 * @returns Next.js response
 */
async function handleCreatePaymentJournalEntries(body: unknown, userId: string) {
  try {
    // Validate request body
    const validationResult = createPaymentJournalEntriesSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }
    const { payrollRunId, bankAccountId, paymentDate } = validationResult.data;
    // Create payment journal entries
    const journalEntry = await payrollAccountingService.createPayrollPaymentJournalEntries(
      payrollRunId,
      bankAccountId,
      new Date(paymentDate),
      userId
    );
    return NextResponse.json({
      success: true,
      message: 'Payment journal entries created successfully',
      data: journalEntry
    });
  } catch (error: unknown) {
    logger.error('Error creating payment journal entries', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}
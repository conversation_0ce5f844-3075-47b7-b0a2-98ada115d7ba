import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
// Removed deprecated payroll-accounting-service import - using unified payroll service
import { unifiedPayrollService } from '@/lib/services/payroll/unified-payroll-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';
import { z } from 'zod';

// Schema for bulk journal entries creation
const bulkJournalEntriesSchema = z.object({
  payrollRunIds: z.array(z.string().min(1, 'Payroll run ID is required')).min(1, 'At least one payroll run ID is required'),
  options: z.object({
    autoPost: z.boolean().optional().default(false),
    batchSize: z.number().min(1).max(100).optional().default(10),
    includeDepartmentAllocation: z.boolean().optional().default(true),
    includeCostCenterMapping: z.boolean().optional().default(true),
  }).optional().default({})
});

// Schema for bulk allocations
const bulkAllocationsSchema = z.object({
  payrollRunIds: z.array(z.string().min(1, 'Payroll run ID is required')).min(1, 'At least one payroll run ID is required'),
  allocationType: z.enum(['department', 'cost_center', 'both']).default('both'),
  options: z.object({
    autoPost: z.boolean().optional().default(false),
    batchSize: z.number().min(1).max(100).optional().default(10),
  }).optional().default({})
});

export const runtime = 'nodejs';

/**
 * POST /api/payroll/accounting/bulk-journal-entries
 * Create journal entries for multiple payroll runs in bulk
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to create bulk journal entries' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Get action
    const { action } = body;

    if (!action) {
      return NextResponse.json({ error: 'Action is required' }, { status: 400 });
    }

    // Process based on action
    switch (action) {
      case 'bulk_create_journal_entries':
        return handleBulkCreateJournalEntries(body, user.id);

      case 'bulk_allocations':
        return handleBulkAllocations(body, user.id);

      case 'automated_posting_workflow':
        return handleAutomatedPostingWorkflow(body, user.id);

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: bulk_create_journal_entries, bulk_allocations, automated_posting_workflow' },
          { status: 400 }
        );
    }
  } catch (error: unknown) {
    logger.error('Error in bulk journal entries API', LogCategory.PAYROLL, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * Handle bulk journal entries creation
 * @param body - Request body
 * @param userId - User ID
 * @returns Next.js response
 */
async function handleBulkCreateJournalEntries(body: unknown, userId: string) {
  try {
    // Validate request body
    const validationResult = bulkJournalEntriesSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    const { payrollRunIds, options } = validationResult.data;

    logger.info('Starting bulk journal entries creation', LogCategory.PAYROLL, {
      payrollRunIds,
      options,
      userId
    });

    // Process payroll runs in batches
    const results = [];
    const errors = [];
    const batchSize = options.batchSize || 10;

    for (let i = 0; i < payrollRunIds.length; i += batchSize) {
      const batch = payrollRunIds.slice(i, i + batchSize);

      logger.info(`Processing batch ${Math.floor(i / batchSize) + 1}`, LogCategory.PAYROLL, {
        batchSize: batch.length,
        totalBatches: Math.ceil(payrollRunIds.length / batchSize)
      });

      // Process each payroll run in the batch
      for (const payrollRunId of batch) {
        try {
          // Create journal entries
          const journalEntry = await payrollAccountingService.createPayrollJournalEntries(
            payrollRunId,
            userId
          );

          // Create department allocation if requested
          if (options.includeDepartmentAllocation) {
            const allocationEntries = await payrollAccountingService.createDepartmentAllocationEntries(
              payrollRunId,
              userId
            );

            results.push({
              payrollRunId,
              journalEntry,
              allocationEntries,
              status: 'success'
            });
          } else {
            results.push({
              payrollRunId,
              journalEntry,
              status: 'success'
            });
          }

          // Auto-post if requested
          if (options.autoPost && journalEntry) {
            await payrollAccountingService.postJournalEntry(journalEntry._id, userId);
          }

        } catch (error) {
          logger.error(`Error processing payroll run ${payrollRunId}`, LogCategory.PAYROLL, error);
          errors.push({
            payrollRunId,
            error: error instanceof Error ? error.message : 'Unknown error',
            status: 'error'
          });
        }
      }

      // Add a small delay between batches to prevent overwhelming the system
      if (i + batchSize < payrollRunIds.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    const summary = {
      total: payrollRunIds.length,
      successful: results.length,
      failed: errors.length,
      results,
      errors
    };

    logger.info('Bulk journal entries creation completed', LogCategory.PAYROLL, summary);

    return NextResponse.json({
      success: true,
      message: `Bulk journal entries creation completed. ${summary.successful} successful, ${summary.failed} failed.`,
      data: summary
    });

  } catch (error: unknown) {
    logger.error('Error in bulk journal entries creation', LogCategory.PAYROLL, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * Handle bulk allocations
 * @param body - Request body
 * @param userId - User ID
 * @returns Next.js response
 */
async function handleBulkAllocations(body: unknown, userId: string) {
  try {
    // Validate request body
    const validationResult = bulkAllocationsSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    const { payrollRunIds, allocationType, options } = validationResult.data;

    logger.info('Starting bulk allocations', LogCategory.PAYROLL, {
      payrollRunIds,
      allocationType,
      options,
      userId
    });

    // Process payroll runs in batches
    const results = [];
    const errors = [];
    const batchSize = options.batchSize || 10;

    for (let i = 0; i < payrollRunIds.length; i += batchSize) {
      const batch = payrollRunIds.slice(i, i + batchSize);

      // Process each payroll run in the batch
      for (const payrollRunId of batch) {
        try {
          const allocationResults: any = {
            payrollRunId,
            allocations: {}
          };

          // Create department allocation
          if (allocationType === 'department' || allocationType === 'both') {
            const departmentAllocation = await payrollAccountingService.createDepartmentAllocationEntries(
              payrollRunId,
              userId
            );
            allocationResults.allocations.department = departmentAllocation;
          }

          // Create cost center mapping
          if (allocationType === 'cost_center' || allocationType === 'both') {
            const costCenterMapping = await payrollAccountingService.createCostCenterMappingEntries(
              payrollRunId,
              userId
            );
            allocationResults.allocations.costCenter = costCenterMapping;
          }

          allocationResults.status = 'success';
          results.push(allocationResults);

          // Auto-post if requested
          if (options.autoPost) {
            // Post department allocation entries
            if (allocationResults.allocations.department) {
              for (const entry of allocationResults.allocations.department) {
                if (entry._id) {
                  await payrollAccountingService.postJournalEntry(entry._id, userId);
                }
              }
            }

            // Post cost center mapping entries
            if (allocationResults.allocations.costCenter) {
              for (const entry of allocationResults.allocations.costCenter) {
                if (entry._id) {
                  await payrollAccountingService.postJournalEntry(entry._id, userId);
                }
              }
            }
          }

        } catch (error) {
          logger.error(`Error processing allocations for payroll run ${payrollRunId}`, LogCategory.PAYROLL, error);
          errors.push({
            payrollRunId,
            error: error instanceof Error ? error.message : 'Unknown error',
            status: 'error'
          });
        }
      }

      // Add a small delay between batches
      if (i + batchSize < payrollRunIds.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    const summary = {
      total: payrollRunIds.length,
      successful: results.length,
      failed: errors.length,
      allocationType,
      results,
      errors
    };

    logger.info('Bulk allocations completed', LogCategory.PAYROLL, summary);

    return NextResponse.json({
      success: true,
      message: `Bulk allocations completed. ${summary.successful} successful, ${summary.failed} failed.`,
      data: summary
    });

  } catch (error: unknown) {
    logger.error('Error in bulk allocations', LogCategory.PAYROLL, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * Handle automated posting workflow
 * @param body - Request body
 * @param userId - User ID
 * @returns Next.js response
 */
async function handleAutomatedPostingWorkflow(body: unknown, userId: string) {
  try {
    // Schema for automated posting workflow
    const automatedPostingSchema = z.object({
      payrollRunIds: z.array(z.string().min(1, 'Payroll run ID is required')).min(1, 'At least one payroll run ID is required'),
      workflow: z.object({
        createJournalEntries: z.boolean().default(true),
        createAllocations: z.boolean().default(true),
        autoPost: z.boolean().default(false),
        postingDelay: z.number().min(0).max(3600).optional().default(0), // seconds
        notifyOnCompletion: z.boolean().default(true),
        rollbackOnError: z.boolean().default(false),
      }).optional().default({})
    });

    // Validate request body
    const validationResult = automatedPostingSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    const { payrollRunIds, workflow } = validationResult.data;

    logger.info('Starting automated posting workflow', LogCategory.PAYROLL, {
      payrollRunIds,
      workflow,
      userId
    });

    const workflowResults = {
      journalEntries: [],
      allocations: [],
      posted: [],
      errors: []
    };

    // Step 1: Create journal entries if requested
    if (workflow.createJournalEntries) {
      logger.info('Creating journal entries in workflow', LogCategory.PAYROLL);

      for (const payrollRunId of payrollRunIds) {
        try {
          const journalEntry = await payrollAccountingService.createPayrollJournalEntries(
            payrollRunId,
            userId
          );

          workflowResults.journalEntries.push({
            payrollRunId,
            journalEntry,
            status: 'success'
          });

        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : 'Unknown error';
          logger.error(`Error creating journal entry for payroll run ${payrollRunId}`, LogCategory.PAYROLL, error);

          workflowResults.errors.push({
            payrollRunId,
            step: 'journal_entry_creation',
            error: errorMsg
          });

          if (workflow.rollbackOnError) {
            // Implement rollback logic here if needed
            logger.warn('Rollback requested but not implemented', LogCategory.PAYROLL);
          }
        }
      }
    }

    // Step 2: Create allocations if requested
    if (workflow.createAllocations) {
      logger.info('Creating allocations in workflow', LogCategory.PAYROLL);

      for (const payrollRunId of payrollRunIds) {
        try {
          const departmentAllocation = await payrollAccountingService.createDepartmentAllocationEntries(
            payrollRunId,
            userId
          );

          workflowResults.allocations.push({
            payrollRunId,
            departmentAllocation,
            status: 'success'
          });

        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : 'Unknown error';
          logger.error(`Error creating allocations for payroll run ${payrollRunId}`, LogCategory.PAYROLL, error);

          workflowResults.errors.push({
            payrollRunId,
            step: 'allocation_creation',
            error: errorMsg
          });
        }
      }
    }

    // Step 3: Auto-post if requested
    if (workflow.autoPost) {
      logger.info('Auto-posting entries in workflow', LogCategory.PAYROLL);

      // Add delay if specified
      if (workflow.postingDelay > 0) {
        logger.info(`Waiting ${workflow.postingDelay} seconds before posting`, LogCategory.PAYROLL);
        await new Promise(resolve => setTimeout(resolve, workflow.postingDelay * 1000));
      }

      // Post journal entries
      for (const result of workflowResults.journalEntries) {
        if (result.status === 'success' && result.journalEntry?._id) {
          try {
            await payrollAccountingService.postJournalEntry(result.journalEntry._id, userId);
            workflowResults.posted.push({
              payrollRunId: result.payrollRunId,
              type: 'journal_entry',
              entryId: result.journalEntry._id,
              status: 'posted'
            });
          } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            workflowResults.errors.push({
              payrollRunId: result.payrollRunId,
              step: 'journal_entry_posting',
              error: errorMsg
            });
          }
        }
      }

      // Post allocation entries
      for (const result of workflowResults.allocations) {
        if (result.status === 'success' && result.departmentAllocation) {
          for (const allocation of result.departmentAllocation) {
            if (allocation._id) {
              try {
                await payrollAccountingService.postJournalEntry(allocation._id, userId);
                workflowResults.posted.push({
                  payrollRunId: result.payrollRunId,
                  type: 'allocation_entry',
                  entryId: allocation._id,
                  status: 'posted'
                });
              } catch (error) {
                const errorMsg = error instanceof Error ? error.message : 'Unknown error';
                workflowResults.errors.push({
                  payrollRunId: result.payrollRunId,
                  step: 'allocation_posting',
                  error: errorMsg
                });
              }
            }
          }
        }
      }
    }

    const summary = {
      totalPayrollRuns: payrollRunIds.length,
      journalEntriesCreated: workflowResults.journalEntries.filter(r => r.status === 'success').length,
      allocationsCreated: workflowResults.allocations.filter(r => r.status === 'success').length,
      entriesPosted: workflowResults.posted.length,
      errors: workflowResults.errors.length,
      workflow,
      results: workflowResults
    };

    logger.info('Automated posting workflow completed', LogCategory.PAYROLL, summary);

    return NextResponse.json({
      success: true,
      message: `Automated posting workflow completed. ${summary.journalEntriesCreated} journal entries, ${summary.allocationsCreated} allocations, ${summary.entriesPosted} posted, ${summary.errors} errors.`,
      data: summary
    });

  } catch (error: unknown) {
    logger.error('Error in automated posting workflow', LogCategory.PAYROLL, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

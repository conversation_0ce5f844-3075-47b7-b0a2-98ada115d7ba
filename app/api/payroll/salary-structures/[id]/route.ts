import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import SalaryStructure from '@/models/payroll/SalaryStructure';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';

export const runtime = 'nodejs';

// app/api/payroll/salary-structures/[id]/route.ts
// Custom auth system doesn't require authOptions;
/**
 * GET /api/payroll/salary-structures/[id]
 * Get a salary structure by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  // Resolve the params promise first
  const { id } = await params;
  const structureId = id; // Store in a variable accessible in the catch block
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get salary structure
    const salaryStructure = await SalaryStructure.findById(id)
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name')
      .populate('applicableDepartments', 'name');
    if (!salaryStructure) {
      return NextResponse.json(
        { error: 'Salary structure not found' },
        { status: 404 }
      );
    }
    return NextResponse.json({
      success: true,
      data: salaryStructure
    });
  } catch (error) {
    logger.error(`Error getting salary structure ${structureId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get salary structure', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
/**
 * PATCH /api/payroll/salary-structures/[id]
 * Update a salary structure
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  // Resolve the params promise first
  const { id } = await params;
  const structureId = id; // Store in a variable accessible in the catch block
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Get request body
    const body = await req.json();
    // Connect to database
    await connectToDatabase();
    // Get salary structure
    const salaryStructure = await SalaryStructure.findById(id);
    if (!salaryStructure) {
      return NextResponse.json(
        { error: 'Salary structure not found' },
        { status: 404 }
      );
    }
    // Check if name is being changed and if a structure with the new name already exists
    if (body.name && body.name !== salaryStructure.name) {
      const existingSalaryStructure = await SalaryStructure.findOne({
        _id: { $ne: id },
        name: body.name
      });
      if (existingSalaryStructure) {
        return NextResponse.json(
          { error: 'A salary structure with the same name already exists' },
          { status: 400 }
        );
      }
    }
    // Update salary structure
    Object.assign(salaryStructure, {
      ...body,
      updatedBy: user.id
    });
    await salaryStructure.save();
    return NextResponse.json({
      success: true,
      message: 'Salary structure updated successfully',
      data: salaryStructure
    });
  } catch (error) {
    logger.error(`Error updating salary structure ${structureId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to update salary structure', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
/**
 * DELETE /api/payroll/salary-structures/[id]
 * Delete a salary structure
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  // Resolve the params promise first
  const { id } = await params;
  const structureId = id; // Store in a variable accessible in the catch block
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get salary structure
    const salaryStructure = await SalaryStructure.findById(id);
    if (!salaryStructure) {
      return NextResponse.json(
        { error: 'Salary structure not found' },
        { status: 404 }
      );
    }
    // Check if this structure is being used by any employees
    const employeesUsingStructure = await EmployeeSalary.countDocuments({
      salaryStructureId: id,
      isActive: true
    });
    if (employeesUsingStructure > 0) {
      return NextResponse.json(
        { error: 'Cannot delete a salary structure that is being used by employees' },
        { status: 400 }
      );
    }
    // Delete salary structure
    await salaryStructure.deleteOne();
    return NextResponse.json({
      success: true,
      message: 'Salary structure deleted successfully'
    });
  } catch (error) {
    logger.error(`Error deleting salary structure ${structureId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to delete salary structure', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
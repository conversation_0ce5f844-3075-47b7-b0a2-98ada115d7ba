import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Using custom authentication system
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import SalaryStructure from '@/models/payroll/SalaryStructure';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';

/**
 * POST /api/payroll/salary-structures/bulk-delete
 * Bulk delete salary structures
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();
    const { ids } = body;

    // Validate IDs
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'No salary structure IDs provided' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Check if any of these structures are being used by employees
    const employeesUsingStructures = await EmployeeSalary.countDocuments({
      salaryStructureId: { $in: ids },
      isActive: true
    });

    if (employeesUsingStructures > 0) {
      return NextResponse.json(
        { error: 'Cannot delete salary structures that are being used by employees' },
        { status: 400 }
      );
    }

    // Delete salary structures
    const result = await SalaryStructure.deleteMany({
      _id: { $in: ids }
    });

    logger.info(`Bulk deleted ${result.deletedCount} salary structures`, LogCategory.API, {
      userId: user.id,
      count: result.deletedCount,
      ids
    });

    return NextResponse.json({
      success: true,
      message: `Successfully deleted ${result.deletedCount} salary structures`,
      deletedCount: result.deletedCount
    });
  } catch (error: unknown) {
    logger.error('Error in bulk delete salary structures', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to delete salary structures', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

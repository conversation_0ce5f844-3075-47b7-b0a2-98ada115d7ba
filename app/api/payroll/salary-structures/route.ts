import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import SalaryStructure from '@/models/payroll/SalaryStructure';

export const runtime = 'nodejs';

/**
 * GET /api/payroll/salary-structures
 * Get salary structures
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const isActive = searchParams.get('isActive') === 'true';
    const name = searchParams.get('name') || '';
    const sortField = searchParams.get('sortField') || 'name';
    const sortOrder = searchParams.get('sortOrder') || 'asc';
    // Connect to database
    await connectToDatabase();
    // Build query
    const query: Record<string, unknown> = {};
    if (isActive) {
      query.isActive = true;
    }
    if (name) {
      query.name = { $regex: name, $options: 'i' };
    }
    // Count total documents
    const totalDocs = await SalaryStructure.countDocuments(query);
    // Get salary structures
    const salaryStructures = await SalaryStructure.find(query)
      .sort({ [sortField]: sortOrder === 'asc' ? 1 : -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name')
      .populate('applicableDepartments', 'name');
    // Calculate pagination info
    const totalPages = Math.ceil(totalDocs / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;
    return NextResponse.json({
      success: true,
      data: {
        docs: salaryStructures,
        totalDocs,
        page,
        totalPages,
        hasNextPage,
        hasPrevPage
      }
    });
  } catch (error: unknown) {
    logger.error('Error getting salary structures', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get salary structures', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}
/**
 * POST /api/payroll/salary-structures
 * Create a new salary structure
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Get request body
    const body = await req.json();
    // Validate required fields
    if (!body.name || !body.effectiveDate || !body.components || !Array.isArray(body.components)) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Check if a salary structure with the same name already exists
    const existingSalaryStructure = await SalaryStructure.findOne({
      name: body.name
    });
    if (existingSalaryStructure) {
      return NextResponse.json(
        { error: 'A salary structure with the same name already exists' },
        { status: 400 }
      );
    }
    // Create salary structure
    const salaryStructure = new SalaryStructure({
      ...body,
      createdBy: user.id
    });
    await salaryStructure.save();
    return NextResponse.json({
      success: true,
      message: 'Salary structure created successfully',
      data: salaryStructure
    }, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error creating salary structure', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to create salary structure', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}
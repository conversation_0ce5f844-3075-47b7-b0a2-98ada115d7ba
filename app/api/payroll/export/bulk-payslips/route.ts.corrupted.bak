// app/api/payroll/export/bulk-payslips/route.ts
import { NextRequest, NextResponse } from "next/server"
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import { connectToDatabase } from '@/lib/backend/database'
import { UserRole } from '@/types/user-roles'
import PaySlip from "@/models/payroll/PaySlip"
import { Employee } from "@/models/Employee"
import PayrollRun from "@/models/payroll/PayrollRun"
import * as XLSX from 'xlsx'

// Required permissions for bulk payslip export
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.PAYROLL_SPECIALIST,
  UserRole.HR_DIRECTOR
]

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES)
    if (!hasPermission) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 })
    }

    await connectToDatabase()

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const format = searchParams.get('format') || 'excel' // excel, pdf, zip
    const payrollRunId = searchParams.get('payrollRunId')
    const employeeIds = searchParams.get('employeeIds')?.split(',')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const department = searchParams.get('department')

    // Build query
    let query: any = {}

    if (payrollRunId) {
      query.payrollRunId = payrollRunId
    }

    if (employeeIds && employeeIds.length > 0) {
      query.employeeId = { $in: employeeIds }
    }

    if (startDate || endDate) {
      query.payPeriodStart = {}
      if (startDate) query.payPeriodStart.$gte = new Date(startDate)
      if (endDate) query.payPeriodStart.$lte = new Date(endDate)
    }

    // Get payslips with employee and payroll run data
    const payslips = await PaySlip.find(query)
      .populate('employeeId', 'firstName lastName email department position employeeNumber')
      .populate('payrollRunId', 'name payPeriod status')
      .sort({ 'payPeriod.startDate': -1, 'employeeId.lastName': 1 })
      .lean()

    if (!payslips || payslips.length === 0) {
      return NextResponse.json({ error: "No payslips found for the specified criteria" }, { status: 404 })
    }

    // Filter by department if specified
    const filteredPayslips = department
      ? payslips.filter(payslip => payslip.employeeId?.department === department)
      : payslips

    if (filteredPayslips.length === 0) {
      return NextResponse.json({ error: "No payslips found for the specified department" }, { status: 404 })
    }

    // Generate export based on format
    switch (format.toLowerCase()) {
      case 'excel':
        return await generateExcelExport(filteredPayslips)
      case 'csv':
        return await generateCSVExport(filteredPayslips)
      default:
        return NextResponse.json({ error: "Invalid format. Supported formats: excel, csv" }, { status: 400 })
    }

  } catch (error) {
    console.error("Bulk payslips export error:", error)
    return NextResponse.json(
      { error: "Failed to export payslips" },
      { status: 500 }
    )
  }
}

async function generateExcelExport(payslips: any[]) {
  // Create workbook and worksheet
  const workbook = XLSX.utils.book_new()

  // Prepare summary data
  const summaryData = payslips.map(payslip => ({
    'Employee ID': payslip.employeeId?.employeeNumber || 'N/A',
    'Employee Name': `${payslip.employeeId?.firstName || ''} ${payslip.employeeId?.lastName || ''}`.trim(),
    'Department': payslip.employeeId?.department || 'N/A',
    'Position': payslip.employeeId?.position || 'N/A',
    'Pay Period': payslip.payPeriod ? `${payslip.payPeriod.month}/${payslip.payPeriod.year}` : 'N/A',
    'Gross Salary': payslip.paymentDetails?.grossSalary || 0,
    'Total Deductions': payslip.paymentDetails?.totalDeductions || 0,
    'Total Tax': payslip.paymentDetails?.totalTax || 0,
    'Net Salary': payslip.paymentDetails?.netSalary || 0,
    'Status': payslip.status || 'N/A'
  }))

  // Create summary worksheet
  const summaryWorksheet = XLSX.utils.json_to_sheet(summaryData)
  XLSX.utils.book_append_sheet(workbook, summaryWorksheet, 'Summary')

  // Prepare detailed data
  const detailedData = payslips.map(payslip => ({
    'Employee ID': payslip.employeeId?.employeeNumber || 'N/A',
    'Employee Name': `${payslip.employeeId?.firstName || ''} ${payslip.employeeId?.lastName || ''}`.trim(),
    'Department': payslip.employeeId?.department || 'N/A',
    'Pay Period': payslip.payPeriod ? `${payslip.payPeriod.month}/${payslip.payPeriod.year}` : 'N/A',
    'Basic Salary': payslip.paymentDetails?.basicSalary || 0,
    'Allowances': payslip.earningsBreakdown?.map((e: any) => `${e.name}: ${e.amount}`).join('; ') || 'None',
    'Deductions': payslip.deductionsBreakdown?.map((d: any) => `${d.name}: ${d.amount}`).join('; ') || 'None',
    'Gross Salary': payslip.paymentDetails?.grossSalary || 0,
    'Tax Amount': payslip.paymentDetails?.totalTax || 0,
    'Net Salary': payslip.paymentDetails?.netSalary || 0
  }))

  // Create detailed worksheet
  const detailedWorksheet = XLSX.utils.json_to_sheet(detailedData)
  XLSX.utils.book_append_sheet(workbook, detailedWorksheet, 'Detailed Payslips')

  // Generate buffer
  const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' })

  const filename = `payslips_export_${new Date().toISOString().split('T')[0]}.xlsx`

  return new NextResponse(buffer, {
    headers: {
      'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="${filename}"`,
      'Content-Length': buffer.byteLength.toString()
    }
  })
}

async function generateCSVExport(payslips: any[]) {
  // Prepare CSV data
  const csvData = payslips.map(payslip => ({
    'Employee ID': payslip.employeeId?.employeeNumber || 'N/A',
    'Employee Name': `${payslip.employeeId?.firstName || ''} ${payslip.employeeId?.lastName || ''}`.trim(),
    'Department': payslip.employeeId?.department || 'N/A',
    'Position': payslip.employeeId?.position || 'N/A',
    'Pay Period': payslip.payPeriod ? `${payslip.payPeriod.month}/${payslip.payPeriod.year}` : 'N/A',
    'Gross Salary': payslip.paymentDetails?.grossSalary || 0,
    'Total Deductions': payslip.paymentDetails?.totalDeductions || 0,
    'Total Tax': payslip.paymentDetails?.totalTax || 0,
    'Net Salary': payslip.paymentDetails?.netSalary || 0,
    'Status': payslip.status || 'N/A'
  }))

  // Convert to CSV
  const worksheet = XLSX.utils.json_to_sheet(csvData)
  const csvContent = XLSX.utils.sheet_to_csv(worksheet)

  const filename = `payslips_export_${new Date().toISOString().split('T')[0]}.csv`

  return new NextResponse(csvContent, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="${filename}"`,
      'Content-Length': csvContent.length.toString()
    }
  })
}

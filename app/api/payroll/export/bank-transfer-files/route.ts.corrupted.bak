// app/api/payroll/export/bank-transfer-files/route.ts
import { NextRequest, NextResponse } from "next/server"
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import { connectToDatabase } from '@/lib/backend/database'
import { UserRole } from '@/types/user-roles'
import PaySlip from "@/models/payroll/PaySlip"
import { Employee } from "@/models/Employee"
import PayrollRun from "@/models/payroll/PayrollRun"

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.PAYROLL_SPECIALIST
    ])

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    await connectToDatabase()

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const format = searchParams.get('format') || 'excel' // excel, csv, txt, mt940
    const payrollRunId = searchParams.get('payrollRunId')
    const bankFormat = searchParams.get('bankFormat') || 'standard' // standard, nbs_malawi, fmb_malawi, standard_bank
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    // Validate required parameters
    if (!payrollRunId && (!startDate || !endDate)) {
      return NextResponse.json({
        error: "Either payrollRunId or date range (startDate and endDate) is required"
      }, { status: 400 })
    }

    // Build query
    let query: any = {}

    if (payrollRunId) {
      query.payrollRunId = payrollRunId
    } else {
      query.payPeriodStart = { $gte: new Date(startDate!), $lte: new Date(endDate!) }
    }

    // Only include approved/paid payslips
    query.status = { $in: ['approved', 'paid'] }

    // Get payslips with employee data including bank details
    const payslips = await PaySlip.find(query)
      .populate('employeeId', 'firstName lastName email department position employeeNumber bankAccountNumber bankName bankBranch bankCode')
      .populate('payrollRunId', 'name payPeriod status')
      .sort({ 'employeeId.lastName': 1 })
      .lean()

    if (!payslips || payslips.length === 0) {
      return NextResponse.json({ error: "No approved payslips found for the specified criteria" }, { status: 404 })
    }

    // Filter out employees without bank details
    const validPayslips = payslips.filter(payslip =>
      payslip.employeeId?.bankAccountNumber &&
      payslip.employeeId?.bankName &&
      payslip.netSalary &&
      payslip.netSalary > 0
    )

    if (validPayslips.length === 0) {
      return NextResponse.json({
        error: "No payslips found with valid bank details and positive net salary"
      }, { status: 404 })
    }

    // Generate bank transfer file based on format and bank
    switch (format.toLowerCase()) {
      case 'csv':
        return await generateBankTransferCSV(validPayslips, bankFormat)
      case 'txt':
        return await generateBankTransferTXT(validPayslips, bankFormat)
      default:
        return NextResponse.json({
          error: "Invalid format. Supported formats: csv, txt"
        }, { status: 400 })
    }

  } catch (error) {
    console.error("Bank transfer file generation error:", error)
    return NextResponse.json(
      { error: "Failed to generate bank transfer file" },
      { status: 500 }
    )
  }
}

// Excel generation removed due to missing ExcelJS dependency
// Use CSV format instead for spreadsheet compatibility

async function generateBankTransferCSV(payslips: any[], bankFormat: string) {
  const columns = getBankTransferColumns(bankFormat)
  const headers = columns.map(col => col.header)

  const csvRows = [headers.join(',')]

  payslips.forEach((payslip, index) => {
    const rowData = generateBankTransferRowData(payslip, bankFormat, index + 1)
    csvRows.push(rowData.map((value: any) => `"${value}"`).join(','))
  })

  const csvContent = csvRows.join('\n')
  const filename = `bank_transfers_${bankFormat}_${new Date().toISOString().split('T')[0]}.csv`

  return new NextResponse(csvContent, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="${filename}"`
    }
  })
}

async function generateBankTransferTXT(payslips: any[], bankFormat: string) {
  let content = ''

  switch (bankFormat.toLowerCase()) {
    case 'nbs_malawi':
      content = generateNBSMalawiFormat(payslips)
      break
    case 'fmb_malawi':
      content = generateFMBMalawiFormat(payslips)
      break
    case 'standard_bank':
      content = generateStandardBankFormat(payslips)
      break
    default:
      content = generateStandardTXTFormat(payslips)
  }

  const filename = `bank_transfers_${bankFormat}_${new Date().toISOString().split('T')[0]}.txt`

  return new NextResponse(content, {
    headers: {
      'Content-Type': 'text/plain',
      'Content-Disposition': `attachment; filename="${filename}"`
    }
  })
}

// MT940 format removed to simplify build

function getBankTransferColumns(bankFormat: string) {
  const baseColumns = [
    { header: 'Sequence', key: 'sequence', width: 10 },
    { header: 'Employee ID', key: 'employeeId', width: 15 },
    { header: 'Employee Name', key: 'employeeName', width: 30 },
    { header: 'Bank Name', key: 'bankName', width: 20 },
    { header: 'Bank Code', key: 'bankCode', width: 15 },
    { header: 'Branch', key: 'branch', width: 20 },
    { header: 'Account Number', key: 'accountNumber', width: 20 },
    { header: 'Amount', key: 'amount', width: 15 },
    { header: 'Currency', key: 'currency', width: 10 },
    { header: 'Reference', key: 'reference', width: 25 },
    { header: 'Description', key: 'description', width: 30 }
  ]

  switch (bankFormat.toLowerCase()) {
    case 'nbs_malawi':
      return [
        ...baseColumns,
        { header: 'NBS Code', key: 'nbsCode', width: 15 },
        { header: 'Transaction Type', key: 'transactionType', width: 15 }
      ]
    case 'fmb_malawi':
      return [
        ...baseColumns,
        { header: 'FMB Reference', key: 'fmbReference', width: 20 },
        { header: 'Payment Method', key: 'paymentMethod', width: 15 }
      ]
    default:
      return baseColumns
  }
}

function generateBankTransferRowData(payslip: any, bankFormat: string, sequence: number) {
  const employee = payslip.employeeId
  const employeeName = `${employee?.firstName || ''} ${employee?.lastName || ''}`.trim()
  const amount = payslip.netSalary || 0
  const reference = `SAL-${employee?.employeeNumber || sequence}-${new Date().getMonth() + 1}${new Date().getFullYear()}`

  const baseData = [
    sequence,
    employee?.employeeNumber || `EMP${sequence}`,
    employeeName,
    employee?.bankName || 'Unknown',
    employee?.bankCode || '',
    employee?.bankBranch || '',
    employee?.bankAccountNumber || '',
    amount,
    'MWK',
    reference,
    `Salary payment for ${employeeName}`
  ]

  switch (bankFormat.toLowerCase()) {
    case 'nbs_malawi':
      return [
        ...baseData,
        employee?.bankCode || 'NBS001',
        'SALARY'
      ]
    case 'fmb_malawi':
      return [
        ...baseData,
        `FMB${sequence.toString().padStart(6, '0')}`,
        'ELECTRONIC'
      ]
    default:
      return baseData
  }
}

function generateNBSMalawiFormat(payslips: any[]) {
  let content = 'NBS MALAWI BANK TRANSFER FILE\n'
  content += `GENERATED: ${new Date().toLocaleDateString()}\n`
  content += `TOTAL RECORDS: ${payslips.length}\n`
  content += `TOTAL AMOUNT: MWK ${payslips.reduce((sum, p) => sum + (p.netSalary || 0), 0).toLocaleString()}\n\n`

  payslips.forEach((payslip, index) => {
    const employee = payslip.employeeId
    const amount = (payslip.netSalary || 0).toFixed(2)
    content += `${(index + 1).toString().padStart(6, '0')}|${employee?.bankAccountNumber || ''}|${amount}|${employee?.firstName || ''} ${employee?.lastName || ''}|SALARY\n`
  })

  return content
}

function generateFMBMalawiFormat(payslips: any[]) {
  let content = 'FMB BANK TRANSFER FILE\n'
  content += `DATE: ${new Date().toLocaleDateString()}\n`
  content += `RECORDS: ${payslips.length}\n\n`

  payslips.forEach((payslip, index) => {
    const employee = payslip.employeeId
    const amount = (payslip.netSalary || 0).toFixed(2)
    content += `${employee?.bankAccountNumber || ''},${amount},${employee?.firstName || ''} ${employee?.lastName || ''},SALARY PAYMENT\n`
  })

  return content
}

function generateStandardBankFormat(payslips: any[]) {
  let content = 'BANK TRANSFER FILE\n'
  content += `GENERATED: ${new Date().toISOString()}\n`
  content += `TOTAL: ${payslips.length} transfers\n\n`

  payslips.forEach((payslip, index) => {
    const employee = payslip.employeeId
    const amount = (payslip.netSalary || 0).toFixed(2)
    content += `${index + 1},${employee?.bankAccountNumber || ''},${amount},${employee?.firstName || ''} ${employee?.lastName || ''},SALARY\n`
  })

  return content
}

function generateStandardTXTFormat(payslips: any[]) {
  let content = 'PAYROLL BANK TRANSFER FILE\n'
  content += '='.repeat(50) + '\n'
  content += `Generated: ${new Date().toLocaleString()}\n`
  content += `Total Transfers: ${payslips.length}\n`
  content += `Total Amount: MWK ${payslips.reduce((sum, p) => sum + (p.netSalary || 0), 0).toLocaleString()}\n\n`

  payslips.forEach((payslip, index) => {
    const employee = payslip.employeeId
    const employeeName = `${employee?.firstName || ''} ${employee?.lastName || ''}`.trim()
    const amount = payslip.netSalary || 0

    content += `Transfer ${index + 1}:\n`
    content += `  Employee: ${employeeName} (${employee?.employeeNumber || 'N/A'})\n`
    content += `  Bank: ${employee?.bankName || 'Unknown'}\n`
    content += `  Account: ${employee?.bankAccountNumber || 'N/A'}\n`
    content += `  Amount: MWK ${amount.toLocaleString()}\n`
    content += `  Reference: SAL-${employee?.employeeNumber || index + 1}-${new Date().getMonth() + 1}${new Date().getFullYear()}\n\n`
  })

  return content
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import SalaryRevision from '@/models/payroll/SalaryRevision';

export const runtime = 'nodejs';

// Required roles for viewing salary revisions
const VIEW_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.PAYROLL_MANAGER,
  UserRole.PAYROLL_OFFICER
];

/**
 * GET /api/payroll/salary-revisions
 * Fetch salary revisions with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, VIEW_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    await connectToDatabase();

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const sort = searchParams.get('sort') || '-createdAt';
    const status = searchParams.get('status');
    const employeeId = searchParams.get('employeeId');
    const department = searchParams.get('department');
    const revisionType = searchParams.get('revisionType');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');

    // Build query
    const query: Record<string, unknown> = {};

    if (status) {
      query.approvalStatus = status;
    }

    if (employeeId) {
      query.employeeId = employeeId;
    }

    if (revisionType) {
      query.revisionType = revisionType;
    }

    if (dateFrom || dateTo) {
      query.effectiveDate = {};
      if (dateFrom) {
        query.effectiveDate.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        query.effectiveDate.$lte = new Date(dateTo);
      }
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Fetch salary revisions with populated employee and department data
    const revisions = await SalaryRevision.find(query)
      .populate({
        path: 'employeeId',
        select: 'firstName lastName email employeeNumber',
        populate: {
          path: 'department',
          select: 'name'
        }
      })
      .populate('createdBy', 'firstName lastName')
      .populate('approvedBy', 'firstName lastName')
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const totalCount = await SalaryRevision.countDocuments(query);

    // Filter by department if specified (after population)
    let filteredRevisions = revisions;
    if (department) {
      filteredRevisions = revisions.filter(revision => 
        revision.employeeId?.department?.name?.toLowerCase().includes(department.toLowerCase())
      );
    }

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    logger.info('Salary revisions fetched', LogCategory.PAYROLL, {
      userId: user.id,
      count: filteredRevisions.length,
      totalCount,
      page,
      limit
    });

    return NextResponse.json({
      success: true,
      data: filteredRevisions,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage,
        hasPrevPage,
        limit
      }
    });

  } catch (error) {
    logger.error('Error fetching salary revisions', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to fetch salary revisions' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/payroll/salary-revisions
 * Create a new salary revision
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, VIEW_ROLES);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    await connectToDatabase();

    // Parse request body
    const body = await request.json();
    const {
      employeeId,
      newBasicSalary,
      effectiveDate,
      reason,
      notes,
      revisionType = 'adjustment',
      autoApprove = false
    } = body;

    // Validate required fields
    if (!employeeId || !newBasicSalary || !effectiveDate || !reason) {
      return NextResponse.json(
        { error: 'Missing required fields: employeeId, newBasicSalary, effectiveDate, reason' },
        { status: 400 }
      );
    }

    // Get current salary to calculate changes
    const EmployeeSalary = (await import('@/models/payroll/EmployeeSalary')).default;
    const currentSalary = await EmployeeSalary.findOne({
      employeeId,
      isActive: true
    });

    if (!currentSalary) {
      return NextResponse.json(
        { error: 'No active salary found for employee' },
        { status: 404 }
      );
    }

    // Calculate changes
    const amountChange = newBasicSalary - currentSalary.basicSalary;
    const percentageChange = (amountChange / currentSalary.basicSalary) * 100;

    // Create new salary record
    const newSalary = new EmployeeSalary({
      ...currentSalary.toObject(),
      _id: undefined,
      basicSalary: newBasicSalary,
      effectiveDate: new Date(effectiveDate),
      isActive: false, // Will be activated when revision is approved
      createdBy: user.id,
      createdAt: undefined,
      updatedAt: undefined
    });

    await newSalary.save();

    // Create salary revision
    const salaryRevision = new SalaryRevision({
      employeeId,
      previousSalaryId: currentSalary._id,
      newSalaryId: newSalary._id,
      revisionType,
      effectiveDate: new Date(effectiveDate),
      previousBasicSalary: currentSalary.basicSalary,
      newBasicSalary,
      percentageChange,
      amountChange,
      currency: currentSalary.currency || 'MWK',
      reason,
      notes,
      approvalStatus: autoApprove ? 'approved' : 'pending',
      approvedBy: autoApprove ? user.id : undefined,
      approvedAt: autoApprove ? new Date() : undefined,
      createdBy: user.id
    });

    await salaryRevision.save();

    // If auto-approved, activate the new salary
    if (autoApprove) {
      const mongoose = (await import('mongoose')).default;

      // Deactivate current salary
      currentSalary.isActive = false;
      currentSalary.endDate = new Date(effectiveDate);
      currentSalary.updatedBy = new mongoose.Types.ObjectId(user.id);
      await currentSalary.save();

      // Activate new salary
      newSalary.isActive = true;
      await newSalary.save();
    }

    // Populate the created revision for response
    const populatedRevision = await SalaryRevision.findById(salaryRevision._id)
      .populate({
        path: 'employeeId',
        select: 'firstName lastName email employeeNumber',
        populate: {
          path: 'department',
          select: 'name'
        }
      })
      .populate('createdBy', 'firstName lastName')
      .populate('approvedBy', 'firstName lastName');

    logger.info('Salary revision created', LogCategory.PAYROLL, {
      revisionId: salaryRevision._id,
      employeeId,
      previousSalary: currentSalary.basicSalary,
      newSalary: newBasicSalary,
      userId: user.id
    });

    return NextResponse.json({
      success: true,
      message: 'Salary revision created successfully',
      data: populatedRevision
    });

  } catch (error) {
    logger.error('Error creating salary revision', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to create salary revision' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { unifiedPayrollService } from '@/lib/services/payroll/unified-payroll-service';
import { Employee } from '@/models/Employee';
import { errorService } from '@/lib/backend/services/error-service';

/**
 * POST /api/payroll/calculate-salary
 * Calculate salary for an employee
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user._id.toString(),
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.employeeId || !body.payPeriod || !body.payPeriod.month || !body.payPeriod.year) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Check if employee exists
    const employee = await Employee.findById(body.employeeId);
    if (!employee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    // Calculate salary using unified service
    const result = await unifiedPayrollService.calculateEmployeeSalary(
      body.employeeId,
      {
        month: body.payPeriod.month,
        year: body.payPeriod.year
      }
    );

    // Check if the result indicates no active salary
    if (result.error === 'NO_ACTIVE_SALARY') {
      return errorService.handlePayrollError(
        'SALARY_CALCULATION_ERROR',
        {
          userId: user._id.toString(),
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: {
            employeeId: body.employeeId,
            employeeName: `${employee.firstName} ${employee.lastName}`,
            payPeriod: body.payPeriod,
            error: 'NO_ACTIVE_SALARY'
          }
        }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        employee: {
          id: employee._id,
          name: `${employee.firstName} ${employee.lastName}`,
          employeeNumber: employee.employeeNumber
        },
        ...result
      }
    });
  } catch (error: unknown) {
    logger.error('Error calculating salary', LogCategory.API, error);
    return errorService.createApiResponse(
      'SYSTEM' as any,
      'SALARY_CALCULATION_SYSTEM_ERROR',
      error instanceof Error ? error.message : 'An unknown error occurred',
      'An unexpected error occurred while calculating the salary. Please try again.',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method
      },
      500,
      'HIGH' as any,
      error instanceof Error ? error.stack : undefined,
      [
        'Try refreshing the page and attempting the calculation again',
        'Check if the employee has an active salary record',
        'Contact support if the problem persists'
      ]
    );
  }
}

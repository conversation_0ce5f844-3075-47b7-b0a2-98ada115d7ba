import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import SalaryBand from '@/models/payroll/SalaryBand';

export const runtime = 'nodejs';

// Define roles that can manage salary bands
const SALARY_BAND_ADMIN_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER,
  UserRole.PAYROLL_MANAGER
];

/**
 * GET /api/payroll/salary-bands
 * Get all salary bands with pagination and filtering
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, SALARY_BAND_ADMIN_ROLES);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const isActive = searchParams.get('isActive');

    // Build query
    const query: any = {};

    if (search) {
      query.$or = [
        { tcmCode: { $regex: search, $options: 'i' } },
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    if (isActive !== null && isActive !== undefined) {
      query.isActive = isActive === 'true';
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get salary bands
    const salaryBands = await SalaryBand.find(query)
      .sort({ tcmCode: 1 })
      .skip(skip)
      .limit(limit)
      .populate('createdBy', 'firstName lastName')
      .populate('updatedBy', 'firstName lastName')
      .populate('approvedBy', 'firstName lastName')
      .lean();

    // Get total count
    const totalDocs = await SalaryBand.countDocuments(query);
    const totalPages = Math.ceil(totalDocs / limit);

    return NextResponse.json({
      success: true,
      data: {
        docs: salaryBands,
        totalDocs,
        limit,
        page,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });

  } catch (error) {
    logger.error('Error fetching salary bands', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/payroll/salary-bands
 * Create a new salary band
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, SALARY_BAND_ADMIN_ROLES);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.tcmCode || !body.name || !body.minSalary || !body.maxSalary || !body.effectiveDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if salary band with same TCM code already exists
    const existingSalaryBand = await SalaryBand.findOne({ 
      tcmCode: body.tcmCode.toUpperCase() 
    });
    if (existingSalaryBand) {
      return NextResponse.json(
        { error: 'Salary band with this TCM code already exists' },
        { status: 400 }
      );
    }

    // Validate salary range
    if (body.maxSalary <= body.minSalary) {
      return NextResponse.json(
        { error: 'Maximum salary must be greater than minimum salary' },
        { status: 400 }
      );
    }

    // Create salary band data
    const salaryBandData = {
      ...body,
      tcmCode: body.tcmCode.toUpperCase(),
      createdBy: user.id
    };

    // Create new salary band
    const salaryBand = new SalaryBand(salaryBandData);
    await salaryBand.save();

    // Populate references for response
    await salaryBand.populate('createdBy', 'firstName lastName');

    logger.info('Salary band created successfully', LogCategory.PAYROLL, {
      salaryBandId: salaryBand._id,
      tcmCode: salaryBand.tcmCode,
      userId: user.id
    });

    return NextResponse.json({
      success: true,
      data: salaryBand
    }, { status: 201 });

  } catch (error) {
    logger.error('Error creating salary band', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

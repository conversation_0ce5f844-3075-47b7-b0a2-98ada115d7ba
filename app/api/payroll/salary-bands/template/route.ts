import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import * as XLSX from 'xlsx';

export const runtime = 'nodejs';

// Define roles that can download salary band templates
const SALARY_BAND_TEMPLATE_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER,
  UserRole.PAYROLL_MANAGER,
  UserRole.HR_SPECIALIST
];
/**
 * GET /api/payroll/salary-bands/template
 * Generate and download salary band import template
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, SALARY_BAND_TEMPLATE_ROLES);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to download salary band template' },
        { status: 403 }
      );
    }
    logger.info('Generating salary band template', LogCategory.PAYROLL, {
      userId: user.id
    });
    // Create workbook
    const workbook = XLSX.utils.book_new();
    // Sample data for TCM salary bands
    const sampleData = [
      {
        'TCM Code': 'TCM 1',
        'Band Name': 'Registrar',
        'Description': 'Chief Executive Officer level - highest administrative position',
        'Minimum Salary': 2500000,
        'Maximum Salary': 3500000,
        'Currency': 'MWK',
        'Step Increment': 100000,
        'Max Steps': 10,
        'Annual Increment %': 5,
        'Effective Date': '2024-01-01',
        'End Date': '',
        'Is Active': 'TRUE',
        'Allowance 1 Name': 'Housing Allowance',
        'Allowance 1 Amount': '',
        'Allowance 1 Percentage': 30,
        'Allowance 1 Taxable': 'TRUE',
        'Allowance 2 Name': 'Transport Allowance',
        'Allowance 2 Amount': 200000,
        'Allowance 2 Percentage': '',
        'Allowance 2 Taxable': 'TRUE',
        'Allowance 3 Name': 'Communication Allowance',
        'Allowance 3 Amount': 50000,
        'Allowance 3 Percentage': '',
        'Allowance 3 Taxable': 'TRUE',
        'Deduction 1 Name': 'PAYE',
        'Deduction 1 Amount': '',
        'Deduction 1 Percentage': 30,
        'Deduction 2 Name': 'Pension Contribution',
        'Deduction 2 Amount': '',
        'Deduction 2 Percentage': 5,
        'Deduction 3 Name': 'Professional Membership',
        'Deduction 3 Amount': 25000,
        'Deduction 3 Percentage': ''
      },
      {
        'TCM Code': 'TCM 2',
        'Band Name': 'Director',
        'Description': 'Director level positions - senior management',
        'Minimum Salary': 1800000,
        'Maximum Salary': 2400000,
        'Currency': 'MWK',
        'Step Increment': 80000,
        'Max Steps': 8,
        'Annual Increment %': 4,
        'Effective Date': '2024-01-01',
        'End Date': '',
        'Is Active': 'TRUE',
        'Allowance 1 Name': 'Housing Allowance',
        'Allowance 1 Amount': '',
        'Allowance 1 Percentage': 25,
        'Allowance 1 Taxable': 'TRUE',
        'Allowance 2 Name': 'Transport Allowance',
        'Allowance 2 Amount': 150000,
        'Allowance 2 Percentage': '',
        'Allowance 2 Taxable': 'TRUE',
        'Allowance 3 Name': '',
        'Allowance 3 Amount': '',
        'Allowance 3 Percentage': '',
        'Allowance 3 Taxable': '',
        'Deduction 1 Name': 'PAYE',
        'Deduction 1 Amount': '',
        'Deduction 1 Percentage': 25,
        'Deduction 2 Name': 'Pension Contribution',
        'Deduction 2 Amount': '',
        'Deduction 2 Percentage': 5,
        'Deduction 3 Name': '',
        'Deduction 3 Amount': '',
        'Deduction 3 Percentage': ''
      },
      {
        'TCM Code': 'TCM 3',
        'Band Name': 'Deputy Director',
        'Description': 'Deputy Director level positions',
        'Minimum Salary': 1400000,
        'Maximum Salary': 1800000,
        'Currency': 'MWK',
        'Step Increment': 60000,
        'Max Steps': 8,
        'Annual Increment %': 4,
        'Effective Date': '2024-01-01',
        'End Date': '',
        'Is Active': 'TRUE',
        'Allowance 1 Name': 'Housing Allowance',
        'Allowance 1 Amount': '',
        'Allowance 1 Percentage': 20,
        'Allowance 1 Taxable': 'TRUE',
        'Allowance 2 Name': 'Transport Allowance',
        'Allowance 2 Amount': 120000,
        'Allowance 2 Percentage': '',
        'Allowance 2 Taxable': 'TRUE',
        'Allowance 3 Name': '',
        'Allowance 3 Amount': '',
        'Allowance 3 Percentage': '',
        'Allowance 3 Taxable': '',
        'Deduction 1 Name': 'PAYE',
        'Deduction 1 Amount': '',
        'Deduction 1 Percentage': 25,
        'Deduction 2 Name': 'Pension Contribution',
        'Deduction 2 Amount': '',
        'Deduction 2 Percentage': 5,
        'Deduction 3 Name': '',
        'Deduction 3 Amount': '',
        'Deduction 3 Percentage': ''
      }
    ];
    // Create main data sheet
    const dataSheet = XLSX.utils.json_to_sheet(sampleData);
    XLSX.utils.book_append_sheet(workbook, dataSheet, 'Salary Bands Data');
    // Create instructions sheet
    const instructions = [
      ['SALARY BAND BULK IMPORT INSTRUCTIONS'],
      [''],
      ['OVERVIEW'],
      ['This template allows you to import multiple salary bands with TCM codes,'],
      ['salary ranges, allowances, and deductions for the Teachers Council of Malawi.'],
      [''],
      ['REQUIRED FIELDS'],
      ['• TCM Code: Must be in format "TCM X" where X is a number (e.g., TCM 1, TCM 2)'],
      ['• Band Name: Descriptive name for the salary band (e.g., Registrar, Director)'],
      ['• Minimum Salary: Lowest salary amount for this band (numeric value)'],
      ['• Maximum Salary: Highest salary amount for this band (numeric value)'],
      ['• Effective Date: When this band becomes active (YYYY-MM-DD format)'],
      [''],
      ['OPTIONAL FIELDS'],
      ['• Description: Detailed description of the band'],
      ['• Currency: Currency code (defaults to MWK if not specified)'],
      ['• Step Increment: Amount for each step progression within the band'],
      ['• Max Steps: Maximum number of steps allowed in this band'],
      ['• Annual Increment %: Percentage for annual salary increases'],
      ['• End Date: When this band expires (leave empty for no expiry)'],
      ['• Is Active: TRUE/FALSE or YES/NO (defaults to TRUE)'],
      [''],
      ['ALLOWANCES (Up to 3 per band)'],
      ['• Allowance X Name: Name of the allowance (e.g., Housing Allowance)'],
      ['• Allowance X Amount: Fixed amount (leave empty if using percentage)'],
      ['• Allowance X Percentage: Percentage of basic salary (leave empty if using amount)'],
      ['• Allowance X Taxable: TRUE/FALSE - whether allowance is subject to tax'],
      [''],
      ['DEDUCTIONS (Up to 3 per band)'],
      ['• Deduction X Name: Name of the deduction (e.g., PAYE, Pension)'],
      ['• Deduction X Amount: Fixed amount (leave empty if using percentage)'],
      ['• Deduction X Percentage: Percentage of gross salary (leave empty if using amount)'],
      [''],
      ['TCM HIERARCHY'],
      ['TCM 1  - Registrar (Chief Executive)'],
      ['TCM 2  - Director'],
      ['TCM 3  - Deputy Director'],
      ['TCM 4  - Assistant Director'],
      ['TCM 5  - Principal Officer'],
      ['TCM 6  - Senior Officer'],
      ['TCM 7  - Officer'],
      ['TCM 8  - Assistant Officer'],
      ['TCM 9  - Senior Clerk'],
      ['TCM 10 - Clerk'],
      ['TCM 11 - Assistant Clerk'],
      ['TCM 12 - Office Assistant'],
      [''],
      ['DATA FORMATS'],
      ['• Dates: Use YYYY-MM-DD format (e.g., 2024-01-01)'],
      ['• Numbers: Use numeric values only (no commas or currency symbols)'],
      ['• Boolean: Use TRUE/FALSE, YES/NO, or 1/0'],
      ['• Currency: Use standard currency codes (MWK, USD, EUR, GBP)'],
      [''],
      ['VALIDATION RULES'],
      ['• TCM codes must be unique within the import'],
      ['• Maximum salary must be greater than or equal to minimum salary'],
      ['• Effective date must be a valid date'],
      ['• If both amount and percentage are specified for allowances/deductions,'],
      ['  the system will use the amount and ignore the percentage'],
      ['• Step increment and max steps work together for progression calculations'],
      [''],
      ['TIPS FOR SUCCESS'],
      ['• Review the sample data in the "Salary Bands Data" sheet'],
      ['• Ensure all required fields are filled'],
      ['• Use consistent date formats throughout'],
      ['• Test with a small batch first before importing large datasets'],
      ['• Keep a backup of your original data'],
      [''],
      ['SUPPORT'],
      ['For technical support or questions about salary band import,'],
      ['contact your system administrator or HR department.']
    ];
    const instructionsSheet = XLSX.utils.aoa_to_sheet(instructions);
    XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Instructions');
    // Create validation rules sheet
    const validationRules = [
      ['Field Name', 'Data Type', 'Required', 'Format/Rules', 'Example'],
      ['TCM Code', 'Text', 'Yes', 'Format: "TCM X" where X is number', 'TCM 1'],
      ['Band Name', 'Text', 'Yes', 'Descriptive name', 'Registrar'],
      ['Description', 'Text', 'No', 'Optional description', 'Chief Executive Officer level'],
      ['Minimum Salary', 'Number', 'Yes', 'Positive number, no commas', '2500000'],
      ['Maximum Salary', 'Number', 'Yes', 'Must be >= Minimum Salary', '3500000'],
      ['Currency', 'Text', 'No', 'Currency code, defaults to MWK', 'MWK'],
      ['Step Increment', 'Number', 'No', 'Amount for each step', '100000'],
      ['Max Steps', 'Number', 'No', 'Maximum progression steps', '10'],
      ['Annual Increment %', 'Number', 'No', 'Percentage (0-100)', '5'],
      ['Effective Date', 'Date', 'Yes', 'YYYY-MM-DD format', '2024-01-01'],
      ['End Date', 'Date', 'No', 'YYYY-MM-DD format or empty', '2025-12-31'],
      ['Is Active', 'Boolean', 'No', 'TRUE/FALSE, YES/NO, 1/0', 'TRUE'],
      ['Allowance X Name', 'Text', 'No', 'Name of allowance', 'Housing Allowance'],
      ['Allowance X Amount', 'Number', 'No', 'Fixed amount or empty', '200000'],
      ['Allowance X Percentage', 'Number', 'No', 'Percentage or empty', '30'],
      ['Allowance X Taxable', 'Boolean', 'No', 'TRUE/FALSE, defaults to TRUE', 'TRUE'],
      ['Deduction X Name', 'Text', 'No', 'Name of deduction', 'PAYE'],
      ['Deduction X Amount', 'Number', 'No', 'Fixed amount or empty', '25000'],
      ['Deduction X Percentage', 'Number', 'No', 'Percentage or empty', '5']
    ];
    const validationSheet = XLSX.utils.aoa_to_sheet(validationRules);
    XLSX.utils.book_append_sheet(workbook, validationSheet, 'Validation Rules');
    // Create examples sheet with different scenarios
    const examples = [
      ['EXAMPLE SCENARIOS'],
      [''],
      ['Scenario 1: Basic salary band with percentage-based allowances'],
      ['TCM Code: TCM 4'],
      ['Band Name: Assistant Director'],
      ['Minimum Salary: 1200000'],
      ['Maximum Salary: 1600000'],
      ['Housing Allowance: 20% of basic salary'],
      ['Transport Allowance: Fixed amount of 100000'],
      ['PAYE Deduction: 25% of gross salary'],
      [''],
      ['Scenario 2: Senior position with multiple allowances'],
      ['TCM Code: TCM 1'],
      ['Band Name: Registrar'],
      ['Minimum Salary: 2500000'],
      ['Maximum Salary: 3500000'],
      ['Housing Allowance: 30% of basic salary'],
      ['Transport Allowance: Fixed 200000'],
      ['Communication Allowance: Fixed 50000'],
      ['PAYE Deduction: 30% of gross salary'],
      ['Pension Deduction: 5% of gross salary'],
      ['Professional Membership: Fixed 25000'],
      [''],
      ['Scenario 3: Entry-level position with minimal allowances'],
      ['TCM Code: TCM 12'],
      ['Band Name: Office Assistant'],
      ['Minimum Salary: 300000'],
      ['Maximum Salary: 500000'],
      ['Transport Allowance: Fixed 30000'],
      ['PAYE Deduction: 0% (below tax threshold)'],
      [''],
      ['Scenario 4: Temporary salary band with expiry date'],
      ['TCM Code: TCM 5'],
      ['Band Name: Principal Officer (Temporary)'],
      ['Effective Date: 2024-01-01'],
      ['End Date: 2024-12-31'],
      ['Is Active: TRUE'],
      [''],
      ['CALCULATION EXAMPLES'],
      [''],
      ['For TCM 1 Registrar with basic salary of 3000000:'],
      ['Basic Salary: 3,000,000 MWK'],
      ['Housing Allowance (30%): 900,000 MWK'],
      ['Transport Allowance: 200,000 MWK'],
      ['Communication Allowance: 50,000 MWK'],
      ['Gross Salary: 4,150,000 MWK'],
      ['PAYE (30%): 1,245,000 MWK'],
      ['Pension (5%): 207,500 MWK'],
      ['Professional Membership: 25,000 MWK'],
      ['Total Deductions: 1,477,500 MWK'],
      ['Net Salary: 2,672,500 MWK']
    ];
    const examplesSheet = XLSX.utils.aoa_to_sheet(examples);
    XLSX.utils.book_append_sheet(workbook, examplesSheet, 'Examples');
    // Generate Excel buffer
    const excelBuffer = XLSX.write(workbook, { 
      type: 'buffer', 
      bookType: 'xlsx',
      compression: true 
    });
    logger.info('Salary band template generated successfully', LogCategory.PAYROLL, {
      userId: user.id,
      templateSize: excelBuffer.length
    });
    // Return file response
    return new NextResponse(excelBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="salary-bands-import-template.xlsx"',
        'Content-Length': excelBuffer.length.toString(),
      },
    });
  } catch (error) {
    logger.error('Error generating salary band template', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Internal server error while generating template' },
      { status: 500 }
    );
  }
}
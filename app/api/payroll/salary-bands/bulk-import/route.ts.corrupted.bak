import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import SalaryBand from '@/models/payroll/SalaryBand';
import * as XLSX from 'xlsx';

export const runtime = 'nodejs';

// Define roles that can bulk import salary bands
const SALARY_BAND_IMPORT_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER,
  UserRole.PAYROLL_MANAGER
];

interface SalaryBandImportRow {
  'TCM Code': string;
  'Band Name': string;
  'Description'?: string;
  'Minimum Salary': number;
  'Maximum Salary': number;
  'Currency': string;
  'Step Increment'?: number;
  'Max Steps'?: number;
  'Annual Increment %'?: number;
  'Effective Date': string;
  'End Date'?: string;
  'Is Active': string | boolean;
  'Allowance 1 Name'?: string;
  'Allowance 1 Amount'?: number;
  'Allowance 1 Percentage'?: number;
  'Allowance 1 Taxable'?: string | boolean;
  'Allowance 2 Name'?: string;
  'Allowance 2 Amount'?: number;
  'Allowance 2 Percentage'?: number;
  'Allowance 2 Taxable'?: string | boolean;
  'Allowance 3 Name'?: string;
  'Allowance 3 Amount'?: number;
  'Allowance 3 Percentage'?: number;
  'Allowance 3 Taxable'?: string | boolean;
  'Deduction 1 Name'?: string;
  'Deduction 1 Amount'?: number;
  'Deduction 1 Percentage'?: number;
  'Deduction 2 Name'?: string;
  'Deduction 2 Amount'?: number;
  'Deduction 2 Percentage'?: number;
  'Deduction 3 Name'?: string;
  'Deduction 3 Amount'?: number;
  'Deduction 3 Percentage'?: number;
}

interface ImportResult {
  row: number;
  tcmCode: string;
  name: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  data?: any;
}

/**
 * POST /api/payroll/salary-bands/bulk-import
 * Bulk import salary bands from Excel file
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, SALARY_BAND_IMPORT_ROLES);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions for salary band bulk import' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Parse form data
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload an Excel file (.xlsx or .xls)' },
        { status: 400 }
      );
    }

    // Read file buffer
    const buffer = Buffer.from(await file.arrayBuffer());
    
    // Parse Excel file
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    // Convert to JSON
    const rawData: SalaryBandImportRow[] = XLSX.utils.sheet_to_json(worksheet);

    if (!rawData || rawData.length === 0) {
      return NextResponse.json(
        { error: 'No data found in the Excel file' },
        { status: 400 }
      );
    }

    logger.info('Starting salary band bulk import', LogCategory.PAYROLL, {
      userId: user.id,
      fileName: file.name,
      rowCount: rawData.length
    });

    const results: ImportResult[] = [];
    let successCount = 0;
    let errorCount = 0;

    // Process each row
    for (let i = 0; i < rawData.length; i++) {
      const row = rawData[i];
      const rowNumber = i + 2; // Excel row number (accounting for header)

      try {
        // Validate required fields
        const validationErrors = validateSalaryBandRow(row);
        if (validationErrors.length > 0) {
          results.push({
            row: rowNumber,
            tcmCode: row['TCM Code'] || 'N/A',
            name: row['Band Name'] || 'N/A',
            status: 'error',
            message: validationErrors.join(', ')
          });
          errorCount++;
          continue;
        }

        // Check for duplicate TCM code
        const existingSalaryBand = await SalaryBand.findOne({
          tcmCode: row['TCM Code'].trim().toUpperCase()
        });

        if (existingSalaryBand) {
          results.push({
            row: rowNumber,
            tcmCode: row['TCM Code'],
            name: row['Band Name'],
            status: 'error',
            message: 'TCM code already exists'
          });
          errorCount++;
          continue;
        }

        // Parse allowances
        const standardAllowances = [];
        for (let j = 1; j <= 3; j++) {
          const allowanceName = row[`Allowance ${j} Name` as keyof SalaryBandImportRow];
          if (allowanceName && allowanceName.trim()) {
            const allowance: any = {
              name: allowanceName.trim(),
              isTaxable: parseBooleanValue(row[`Allowance ${j} Taxable` as keyof SalaryBandImportRow]) ?? true
            };

            const amount = row[`Allowance ${j} Amount` as keyof SalaryBandImportRow];
            const percentage = row[`Allowance ${j} Percentage` as keyof SalaryBandImportRow];

            if (amount && !isNaN(Number(amount))) {
              allowance.amount = Number(amount);
            }
            if (percentage && !isNaN(Number(percentage))) {
              allowance.percentage = Number(percentage);
            }

            standardAllowances.push(allowance);
          }
        }

        // Parse deductions
        const standardDeductions = [];
        for (let j = 1; j <= 3; j++) {
          const deductionName = row[`Deduction ${j} Name` as keyof SalaryBandImportRow];
          if (deductionName && deductionName.trim()) {
            const deduction: any = {
              name: deductionName.trim()
            };

            const amount = row[`Deduction ${j} Amount` as keyof SalaryBandImportRow];
            const percentage = row[`Deduction ${j} Percentage` as keyof SalaryBandImportRow];

            if (amount && !isNaN(Number(amount))) {
              deduction.amount = Number(amount);
            }
            if (percentage && !isNaN(Number(percentage))) {
              deduction.percentage = Number(percentage);
            }

            standardDeductions.push(deduction);
          }
        }

        // Create salary band data
        const salaryBandData = {
          tcmCode: row['TCM Code'].trim().toUpperCase(),
          name: row['Band Name'].trim(),
          description: row['Description']?.trim() || undefined,
          minSalary: Number(row['Minimum Salary']),
          maxSalary: Number(row['Maximum Salary']),
          currency: row['Currency']?.trim() || 'MWK',
          stepIncrement: row['Step Increment'] ? Number(row['Step Increment']) : undefined,
          maxSteps: row['Max Steps'] ? Number(row['Max Steps']) : undefined,
          annualIncrementPercentage: row['Annual Increment %'] ? Number(row['Annual Increment %']) : undefined,
          effectiveDate: parseDate(row['Effective Date']),
          expiryDate: row['End Date'] ? parseDate(row['End Date']) : undefined,
          isActive: parseBooleanValue(row['Is Active']) ?? true,
          standardAllowances,
          standardDeductions,
          createdBy: user.id
        };

        // Create salary band
        const salaryBand = new SalaryBand(salaryBandData);
        await salaryBand.save();

        results.push({
          row: rowNumber,
          tcmCode: row['TCM Code'],
          name: row['Band Name'],
          status: 'success',
          message: 'Salary band created successfully',
          data: { id: salaryBand._id }
        });
        successCount++;

        logger.info('Salary band created via bulk import', LogCategory.PAYROLL, {
          salaryBandId: salaryBand._id,
          tcmCode: salaryBand.tcmCode,
          userId: user.id,
          row: rowNumber
        });

      } catch (error) {
        logger.error('Error processing salary band row', LogCategory.PAYROLL, {
          error,
          row: rowNumber,
          tcmCode: row['TCM Code'],
          userId: user.id
        });

        results.push({
          row: rowNumber,
          tcmCode: row['TCM Code'] || 'N/A',
          name: row['Band Name'] || 'N/A',
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error occurred'
        });
        errorCount++;
      }
    }

    logger.info('Salary band bulk import completed', LogCategory.PAYROLL, {
      userId: user.id,
      fileName: file.name,
      totalRows: rawData.length,
      successCount,
      errorCount
    });

    return NextResponse.json({
      success: true,
      message: `Bulk import completed. ${successCount} salary bands imported successfully, ${errorCount} failed.`,
      data: {
        processed: rawData.length,
        successful: successCount,
        failed: errorCount,
        results
      }
    });

  } catch (error) {
    logger.error('Error in salary band bulk import', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Internal server error during bulk import' },
      { status: 500 }
    );
  }
}

/**
 * Validate salary band row data
 */
function validateSalaryBandRow(row: SalaryBandImportRow): string[] {
  const errors: string[] = [];

  // Required fields
  if (!row['TCM Code']?.trim()) {
    errors.push('TCM Code is required');
  } else if (!/^TCM\s+\d+$/i.test(row['TCM Code'].trim())) {
    errors.push('TCM Code must be in format "TCM X" where X is a number');
  }

  if (!row['Band Name']?.trim()) {
    errors.push('Band Name is required');
  }

  if (!row['Minimum Salary'] || isNaN(Number(row['Minimum Salary']))) {
    errors.push('Valid Minimum Salary is required');
  }

  if (!row['Maximum Salary'] || isNaN(Number(row['Maximum Salary']))) {
    errors.push('Valid Maximum Salary is required');
  }

  if (row['Minimum Salary'] && row['Maximum Salary'] && 
      Number(row['Maximum Salary']) < Number(row['Minimum Salary'])) {
    errors.push('Maximum Salary must be greater than or equal to Minimum Salary');
  }

  if (!row['Effective Date']) {
    errors.push('Effective Date is required');
  } else if (!parseDate(row['Effective Date'])) {
    errors.push('Invalid Effective Date format');
  }

  return errors;
}

/**
 * Parse date from various formats
 */
function parseDate(dateValue: any): Date | null {
  if (!dateValue) return null;
  
  try {
    // Handle Excel date numbers
    if (typeof dateValue === 'number') {
      return new Date((dateValue - 25569) * 86400 * 1000);
    }
    
    // Handle string dates
    const date = new Date(dateValue);
    return isNaN(date.getTime()) ? null : date;
  } catch {
    return null;
  }
}

/**
 * Parse boolean values from various formats
 */
function parseBooleanValue(value: any): boolean | null {
  if (typeof value === 'boolean') return value;
  if (typeof value === 'string') {
    const lower = value.toLowerCase().trim();
    if (lower === 'true' || lower === 'yes' || lower === '1') return true;
    if (lower === 'false' || lower === 'no' || lower === '0') return false;
  }
  if (typeof value === 'number') {
    return value === 1;
  }
  return null;
}


//  app/api/payroll/deductions/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import Deduction from '@/models/payroll/Deduction';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';

/**
 * GET /api/payroll/deductions/[id]
 * Get a deduction by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  // Declare deductionId at function scope
  let deductionId: string = 'unknown';

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;
    deductionId = id;

    // Get deduction
    const deduction = await Deduction.findById(id)
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name')
      .populate('applicableDepartments', 'name');

    if (!deduction) {
      return NextResponse.json(
        { error: 'Deduction not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: deduction
    });
  } catch (error) {
    logger.error(`Error getting deduction ${deductionId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get deduction', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/payroll/deductions/[id]
 * Update a deduction
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  // Declare deductionId at function scope
  let deductionId: string = 'unknown';

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;
    deductionId = id;

    // Get deduction
    const deduction = await Deduction.findById(id);

    if (!deduction) {
      return NextResponse.json(
        { error: 'Deduction not found' },
        { status: 404 }
      );
    }

    // Check if code is being changed and if a deduction with the new code already exists
    if (body.code && body.code !== deduction.code) {
      const existingDeduction = await Deduction.findOne({
        _id: { $ne: id },
        code: body.code
      });

      if (existingDeduction) {
        return NextResponse.json(
          { error: 'A deduction with the same code already exists' },
          { status: 400 }
        );
      }
    }

    // Update deduction
    Object.assign(deduction, {
      ...body,
      updatedBy: user.id
    });

    await deduction.save();

    return NextResponse.json({
      success: true,
      message: 'Deduction updated successfully',
      data: deduction
    });
  } catch (error) {
    logger.error(`Error updating deduction ${deductionId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to update deduction', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/payroll/deductions/[id]
 * Delete a deduction
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  // Declare deductionId at function scope
  let deductionId: string = 'unknown';

  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;
    deductionId = id;

    // Get deduction
    const deduction = await Deduction.findById(id);

    if (!deduction) {
      return NextResponse.json(
        { error: 'Deduction not found' },
        { status: 404 }
      );
    }

    // Check if this deduction is being used by any employees
    const employeesUsingDeduction = await EmployeeSalary.countDocuments({
      'deductions.deductionId': id,
      isActive: true
    });

    if (employeesUsingDeduction > 0) {
      return NextResponse.json(
        { error: 'Cannot delete a deduction that is being used by employees' },
        { status: 400 }
      );
    }

    // Delete deduction
    await deduction.deleteOne();

    return NextResponse.json({
      success: true,
      message: 'Deduction deleted successfully'
    });
  } catch (error) {
    logger.error(`Error deleting deduction ${deductionId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to delete deduction', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

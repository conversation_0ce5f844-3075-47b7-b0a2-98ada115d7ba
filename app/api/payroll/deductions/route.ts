import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import Deduction from '@/models/payroll/Deduction';

export const runtime = 'nodejs';

/**
 * GET /api/payroll/deductions
 * Get deductions
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const isActive = searchParams.get('isActive') === 'true';
    const isStatutory = searchParams.get('isStatutory') === 'true' ? true : (searchParams.get('isStatutory') === 'false' ? false : undefined);
    const name = searchParams.get('name') || '';
    const sortField = searchParams.get('sortField') || 'name';
    const sortOrder = searchParams.get('sortOrder') || 'asc';
    // Connect to database
    await connectToDatabase();
    // Build query
    const query: Record<string, unknown> = {};
    if (isActive) {
      query.isActive = true;
    }
    if (isStatutory !== undefined) {
      query.isStatutory = isStatutory;
    }
    if (name) {
      query.name = { $regex: name, $options: 'i' };
    }
    // Count total documents
    const totalDocs = await Deduction.countDocuments(query);
    // Get deductions
    const deductions = await Deduction.find(query)
      .sort({ [sortField]: sortOrder === 'asc' ? 1 : -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name')
      .populate('applicableDepartments', 'name');
    // Calculate pagination info
    const totalPages = Math.ceil(totalDocs / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;
    return NextResponse.json({
      success: true,
      data: {
        docs: deductions,
        totalDocs,
        page,
        totalPages,
        hasNextPage,
        hasPrevPage
      }
    });
  } catch (error: unknown) {
    logger.error('Error getting deductions', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get deductions', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}
/**
 * POST /api/payroll/deductions
 * Create a new deduction
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Get request body
    const body = await req.json();
    // Validate required fields
    if (!body.name || !body.code) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Check if a deduction with the same code already exists
    const existingDeduction = await Deduction.findOne({
      code: body.code
    });
    if (existingDeduction) {
      return NextResponse.json(
        { error: 'A deduction with the same code already exists' },
        { status: 400 }
      );
    }
    // Create deduction
    const deduction = new Deduction({
      ...body,
      createdBy: user.id
    });
    await deduction.save();
    return NextResponse.json({
      success: true,
      message: 'Deduction created successfully',
      data: deduction
    }, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error creating deduction', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to create deduction', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}
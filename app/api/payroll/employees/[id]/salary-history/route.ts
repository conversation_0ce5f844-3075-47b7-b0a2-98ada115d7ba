import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';
import SalaryRevision from '@/models/payroll/SalaryRevision';
import { Employee } from '@/models/Employee';

export const runtime = 'nodejs';

// app/api/payroll/employees/[id]/salary-history/route.ts
// Custom auth system doesn't require authOptions;
/**
 * GET /api/payroll/employees/[id]/salary-history
 * Get salary history for an employee
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Resolve the params promise
    const { id } = await params;
    // Check if employee exists
    const employee = await Employee.findById(id);
    if (!employee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const includeRevisions = searchParams.get('includeRevisions') === 'true';
    // Get salary history
    const salaryHistory = await EmployeeSalary.find({ employeeId: id })
      .sort({ effectiveDate: -1 })
      .populate('salaryStructureId', 'name')
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name');
    // Get salary revisions if requested
    let salaryRevisions = [];
    if (includeRevisions) {
      salaryRevisions = await SalaryRevision.find({ employeeId: id })
        .sort({ effectiveDate: -1 })
        .populate('previousSalaryId', 'basicSalary effectiveDate')
        .populate('newSalaryId', 'basicSalary effectiveDate')
        .populate('createdBy', 'name')
        .populate('approvedBy', 'name')
        .populate('rejectedBy', 'name');
    }
    return NextResponse.json({
      success: true,
      data: {
        employee: {
          id: employee._id,
          name: `${employee.firstName} ${employee.lastName}`,
          employeeNumber: employee.employeeNumber
        },
        salaryHistory,
        salaryRevisions: includeRevisions ? salaryRevisions : undefined
      }
    });
  } catch (error) {
    // Resolve the params promise to get the ID for logging
    const resolvedId = await params.then(p => p.id).catch(() => 'unknown');
    logger.error(`Error getting salary history for employee ${resolvedId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get salary history', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
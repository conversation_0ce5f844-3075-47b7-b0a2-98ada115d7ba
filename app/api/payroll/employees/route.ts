import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { Employee as EmployeeModel } from '@/models/Employee';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';

export const runtime = 'nodejs';

// app/api/payroll/employees/route.ts
/**
 * GET /api/payroll/employees
 * Get employees for payroll processing
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_OFFICER
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Connect to database
    try {
      await connectToDatabase();
      logger.info('Connected to database for employee fetch', LogCategory.API);
    } catch (dbError: unknown) {
      logger.error('Database connection error in payroll/employees', LogCategory.API, dbError);
      return NextResponse.json(
        { error: 'Database connection failed', details: dbError instanceof Error ? dbError.message : 'An error occurred' },
        { status: 500 }
      );
    }
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const search = searchParams.get('search') || '';
    const departmentId = searchParams.get('departmentId') || '';
    const status = searchParams.get('status') || 'active';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '100');
    const skip = (page - 1) * limit;
    const requireSalaryRecord = searchParams.get('requireSalaryRecord') !== 'false'; // Default to true
    // Build query
    const query: Record<string, any> = {};
    // Add status filter (default to active employees)
    if (status) {
      query.employmentStatus = status;
    }
    // Add department filter
    if (departmentId) {
      query.departmentId = departmentId;
    }
    // Add search filter
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { employeeId: { $regex: search, $options: 'i' } }
      ];
    }
    logger.debug('Fetching employees with query', LogCategory.API, { query, skip, limit });
    try {
      // If we need to filter by salary record (default behavior for payroll)
      if (requireSalaryRecord) {
        // Get all employee IDs that have an EmployeeSalary record
        const employeesWithSalary = await EmployeeSalary.find({ isActive: true })
          .distinct('employeeId');
        logger.debug('Found employees with salary records', LogCategory.API, { count: employeesWithSalary.length });
        // Add filter to only include employees with salary records
        query._id = { $in: employeesWithSalary };
      }
      // Get employees - include salary field from Employee model
      const [employees, total] = await Promise.all([
        EmployeeModel.find(query)
          .select('_id employeeId firstName lastName email position departmentId salary')
          .populate('departmentId', 'name')
          .sort({ lastName: 1, firstName: 1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        EmployeeModel.countDocuments(query)
      ]);
      logger.debug('Employee query results', LogCategory.API, { count: employees.length, total });
      // Check if Employee model exists
      if (!EmployeeModel) {
        logger.error('Employee model is undefined', LogCategory.API);
        return NextResponse.json(
          { error: 'Employee model not found' },
          { status: 500 }
        );
      }
      // Handle case where no employees are found
      if (!employees || employees.length === 0) {
        logger.info('No employees found for query', LogCategory.API, { query });
        return NextResponse.json({
          success: true,
          data: {
            employees: [],
            pagination: {
              total: 0,
              page,
              limit,
              pages: 0
            }
          }
        });
      }
      // Get all employee IDs that have an EmployeeSalary record
      const employeeSalaries = await EmployeeSalary.find({ isActive: true })
        .lean();
      // Create a map of employee ID to salary data for quick lookup
      const salaryMap = new Map();
      employeeSalaries.forEach(salary => {
        if (salary.employeeId) {
          salaryMap.set(salary.employeeId.toString(), {
            basicSalary: salary.basicSalary,
            allowances: salary.allowances || [],
            deductions: salary.deductions || [],
          });
        }
      });
      logger.debug('Found employee salary records', LogCategory.API, {
        count: salaryMap.size
      });
      // If we're not requiring salary records, we need to check which employees have them
      let employeesWithSalarySet = new Set();
      if (!requireSalaryRecord) {
        // Convert to a Set for faster lookups
        employeesWithSalarySet = new Set(Array.from(salaryMap.keys()));
        logger.debug('Found employees with salary records for flagging', LogCategory.API, {
          count: employeesWithSalarySet.size
        });
      }
      // Format employees for the frontend with null checks
      const formattedEmployees = employees.map((employee: any) => {
        if (!employee) return null;
        // Determine if this employee has a salary record
        // If we're requiring salary records, all returned employees have them
        // Otherwise, check the set of employee IDs with salary records
        const hasSalaryRecord = requireSalaryRecord ? true :
          employeesWithSalarySet.has(employee._id.toString());
        // Get salary data for this employee
        const salaryData = salaryMap.get(employee._id.toString());
        // Calculate total allowances
        const totalAllowances = salaryData?.allowances?.reduce((sum: number, allowance: { amount?: number }) => {
          return sum + (allowance.amount || 0);
        }, 0) || 0;
        // Calculate total deductions
        const totalDeductions = salaryData?.deductions?.reduce((sum: number, deduction: { amount?: number }) => {
          return sum + (deduction.amount || 0);
        }, 0) || 0;
        // Calculate net salary (basic + allowances - deductions)
        const basicSalary = salaryData?.basicSalary || 0;
        const netSalary = basicSalary + totalAllowances - totalDeductions;
        return {
          id: employee._id.toString(),
          employeeId: employee.employeeId || 'N/A',
          name: `${employee.firstName || ''} ${employee.lastName || ''}`.trim() || 'Unnamed Employee',
          email: employee.email || 'N/A',
          position: employee.position || 'No Position',
          department: employee.departmentId ?
            (typeof employee.departmentId === 'object' && employee.departmentId !== null && 'name' in employee.departmentId ?
              employee.departmentId.name : 'Unknown Department')
            : 'No Department',
          departmentId: employee.departmentId && typeof employee.departmentId === 'object' && '_id' in employee.departmentId ?
            employee.departmentId._id.toString() :
            employee.departmentId ? employee.departmentId.toString() : undefined,
          hasSalaryRecord: hasSalaryRecord,
          // Add original employee salary from Employee model
          originalSalary: employee.salary || null,
          // Add salary information from EmployeeSalary records
          salary: salaryData ? {
            basic: basicSalary,
            allowances: totalAllowances,
            deductions: totalDeductions,
            net: netSalary
          } : undefined,
          // Default payroll status
          payrollStatus: 'eligible'
        };
      }).filter(Boolean); // Remove any null entries
      // Return employees
      return NextResponse.json({
        success: true,
        data: {
          employees: formattedEmployees,
          pagination: {
            total,
            page,
            limit,
            pages: Math.ceil(total / limit)
          },
          filters: {
            requireSalaryRecord
          }
        }
      });
    } catch (queryError: unknown) {
      logger.error('Error querying employees', LogCategory.API, queryError);
      return NextResponse.json(
        { error: 'Failed to query employees', details: queryError instanceof Error ? queryError.message : 'An error occurred' },
        { status: 500 }
      );
    }
  } catch (error: unknown) {
    logger.error('Unhandled error fetching employees for payroll', LogCategory.API, error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        stack: process.env.NODE_ENV !== 'production' && error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
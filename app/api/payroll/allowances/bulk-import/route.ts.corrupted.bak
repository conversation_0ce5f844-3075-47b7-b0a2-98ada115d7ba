// app/api/payroll/allowances/bulk-import/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { UserRole } from '@/types/user-roles'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import Allowance from '@/models/payroll/Allowance'
import { connectToDatabase } from '@/lib/backend/database'
import * as XLSX from 'xlsx'
import logger, { LogCategory } from '@/lib/backend/utils/logger'

export const runtime = 'nodejs';




// Define the expected columns in the import file
const REQUIRED_COLUMNS = [
  'name',
  'code'
]

// Define a mapping between display names in the template and the expected field names
const COLUMN_DISPLAY_MAPPING: Record<string, string> = {
  'Name': 'name',
  'Code': 'code',
  'Description': 'description',
  'Is Active': 'isActive',
  'Is Taxable': 'isTaxable',
  'Is Pensionable': 'isPensionable',
  'Is Fixed': 'isFixed',
  'Default Amount': 'defaultAmount',
  'Default Percentage': 'defaultPercentage',
  'Calculation Base': 'calculationBase',
  'Applicable Roles': 'applicableRoles',
  'Applicable Departments': 'applicableDepartments'
}

// Define roles that can manage allowances
const ALLOWANCE_ADMIN_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER,
  UserRole.FINANCE_MANAGER,
  UserRole.ACCOUNTANT
]

/**
 * POST handler for bulk importing allowances
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, ALLOWANCE_ADMIN_ROLES)

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Connect to database
    await connectToDatabase()

    // Get form data
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Check file type
    const fileType = file.name.split('.').pop()?.toLowerCase()
    if (fileType !== 'csv' && fileType !== 'xlsx' && fileType !== 'xls') {
      return NextResponse.json({ error: 'Invalid file type. Please upload a CSV or Excel file' }, { status: 400 })
    }

    // Read file
    const buffer = await file.arrayBuffer()

    logger.info('Processing allowance bulk import', LogCategory.IMPORT, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      userId: user.id
    })

    const workbook = XLSX.read(buffer, { type: 'array' })

    // Get first sheet
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]

    // Convert to JSON with header row mapping
    const rows = XLSX.utils.sheet_to_json(worksheet, {
      defval: null,
      raw: false, // Convert all data to strings
      blankrows: false // Skip blank rows
    }) as Record<string, string>[]

    // Validate and process data
    const result = {
      totalRows: rows.length,
      successCount: 0,
      errorCount: 0,
      skippedCount: 0,
      errors: [] as Array<{ row: number, error: string }>,
      skipped: [] as Array<{ row: number, reason: string }>
    }

    // Check if file is empty
    if (rows.length === 0) {
      logger.warn('Empty file uploaded for allowance import', LogCategory.IMPORT, {
        userId: user.id
      })
      return NextResponse.json({ error: 'File is empty' }, { status: 400 })
    }

    // Log the first row for debugging
    logger.debug('First row of import file', LogCategory.IMPORT, {
      firstRow: rows[0],
      keys: '********'
    })

    // Create a mapping between the columns in the file and our expected fields
    const availableColumns = Object.keys(rows[0])
    const columnMap: Record<string, string> = {}

    // Try to map columns based on exact matches or display name mapping
    for (const expectedField of Object.keys(COLUMN_DISPLAY_MAPPING)) {
      const mappedField = COLUMN_DISPLAY_MAPPING[expectedField]

      // Check if the expected field name exists in the file
      if (availableColumns.includes(expectedField)) {
        columnMap[mappedField] = expectedField
      }
      // Check if the mapped field name exists in the file
      else if (availableColumns.includes(mappedField)) {
        columnMap[mappedField] = mappedField
      }
    }

    logger.debug('Column mapping', LogCategory.IMPORT, {
      columnMap,
      availableColumns
    })

    // Process each row
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i]
      let normalizedRow: Record<string, any> = {}

      // Map columns from the file to our expected fields
      for (const [field, column] of Object.entries(columnMap)) {
        normalizedRow[field] = row[column]
      }

      try {
        // Log the row being processed for debugging
        logger.debug('Processing row', LogCategory.IMPORT, {
          rowIndex: i + 1,
          normalizedRow
        })

        // Check if row is empty or has no meaningful data
        const hasData = Object.values(normalizedRow).some(value =>
          value !== null && value !== undefined && value !== ''
        )

        if (!hasData) {
          result.skippedCount++
          result.skipped.push({
            row: i + 1,
            reason: 'Empty row - no data found'
          })
          logger.debug('Skipping empty row', LogCategory.IMPORT, {
            rowIndex: i + 1
          })
          continue
        }

        // Validate required fields
        const missingFields = REQUIRED_COLUMNS.filter(field =>
          !normalizedRow[field] || normalizedRow[field].toString().trim() === ''
        )

        if (missingFields.length > 0) {
          result.skippedCount++
          result.skipped.push({
            row: i + 1,
            reason: `Missing required fields: ${missingFields.join(', ')}`
          })
          logger.debug('Skipping row with missing required fields', LogCategory.IMPORT, {
            rowIndex: i + 1,
            missingFields
          })
          continue
        }

        // Convert boolean fields
        normalizedRow.isActive = normalizedRow.isActive === 'true' || normalizedRow.isActive === 'yes' || normalizedRow.isActive === '1' || normalizedRow.isActive === true
        normalizedRow.isTaxable = normalizedRow.isTaxable === 'true' || normalizedRow.isTaxable === 'yes' || normalizedRow.isTaxable === '1' || normalizedRow.isTaxable === true
        normalizedRow.isPensionable = normalizedRow.isPensionable === 'true' || normalizedRow.isPensionable === 'yes' || normalizedRow.isPensionable === '1' || normalizedRow.isPensionable === true
        normalizedRow.isFixed = normalizedRow.isFixed === 'true' || normalizedRow.isFixed === 'yes' || normalizedRow.isFixed === '1' || normalizedRow.isFixed === true

        // Convert numeric fields
        if (normalizedRow.defaultAmount) {
          normalizedRow.defaultAmount = Number(normalizedRow.defaultAmount)
          if (isNaN(normalizedRow.defaultAmount)) {
            throw new Error('Default amount must be a number')
          }
        }

        if (normalizedRow.defaultPercentage) {
          normalizedRow.defaultPercentage = Number(normalizedRow.defaultPercentage)
          if (isNaN(normalizedRow.defaultPercentage)) {
            throw new Error('Default percentage must be a number')
          }
        }

        // Convert array fields
        if (normalizedRow.applicableRoles && typeof normalizedRow.applicableRoles === 'string') {
          normalizedRow.applicableRoles = normalizedRow.applicableRoles.split(',').map((role: string) => role.trim())
        }

        if (normalizedRow.applicableDepartments && typeof normalizedRow.applicableDepartments === 'string') {
          normalizedRow.applicableDepartments = normalizedRow.applicableDepartments.split(',').map((dept: string) => dept.trim())
        }

        // Create allowance data
        const allowanceData = {
          name: normalizedRow.name,
          code: normalizedRow.code,
          description: normalizedRow.description || undefined,
          isActive: normalizedRow.isActive !== undefined ? normalizedRow.isActive : true,
          isTaxable: normalizedRow.isTaxable !== undefined ? normalizedRow.isTaxable : false,
          isPensionable: normalizedRow.isPensionable !== undefined ? normalizedRow.isPensionable : false,
          isFixed: normalizedRow.isFixed !== undefined ? normalizedRow.isFixed : true,
          defaultAmount: normalizedRow.defaultAmount || undefined,
          defaultPercentage: normalizedRow.defaultPercentage || undefined,
          calculationBase: normalizedRow.calculationBase || undefined,
          applicableRoles: normalizedRow.applicableRoles || undefined,
          applicableDepartments: normalizedRow.applicableDepartments || undefined,
          createdBy: user.id
        }

        // Check if allowance already exists
        const existingAllowance = await Allowance.findOne({ code: allowanceData.code })
        if (existingAllowance) {
          result.skippedCount++
          result.skipped.push({
            row: i + 1,
            reason: `Allowance with code ${allowanceData.code} already exists`
          })
          logger.debug('Skipping duplicate allowance', LogCategory.IMPORT, {
            rowIndex: i + 1,
            code: allowanceData.code
          })
          continue
        }

        // Create allowance
        await Allowance.create(allowanceData)
        result.successCount++

        logger.info('Allowance created successfully', LogCategory.IMPORT, {
          rowIndex: i + 1,
          code: allowanceData.code,
          userId: user.id
        })
      } catch (error: unknown) {
        result.errorCount++

        // Log the error
        logger.error('Error processing allowance row', LogCategory.IMPORT, {
          rowIndex: i + 1,
          error: error instanceof Error ? error.message : 'An error occurred',
          row: normalizedRow || row,
          userId: user.id
        })

        result.errors.push({
          row: i + 1,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Log the final result
    logger.info('Allowance bulk import completed', LogCategory.IMPORT, {
      totalRows: result.totalRows,
      successCount: result.successCount,
      errorCount: result.errorCount,
      skippedCount: result.skippedCount,
      userId: user.id
    })

    return NextResponse.json({
      status: 'success',
      data: result
    })
  } catch (error: unknown) {
    console.error('Error in allowance bulk import:', error)
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 })
  }
}

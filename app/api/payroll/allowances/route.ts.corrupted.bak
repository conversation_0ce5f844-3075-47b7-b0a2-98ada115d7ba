import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import Allowance from '@/models/payroll/Allowance';

/**
 * GET /api/payroll/allowances
 * Get allowances
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const isActive = searchParams.get('isActive') === 'true';
    const isTaxable = searchParams.get('isTaxable') === 'true' ? true : (searchParams.get('isTaxable') === 'false' ? false : undefined);
    const name = searchParams.get('name') || '';
    const sortField = searchParams.get('sortField') || 'name';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    // Connect to database
    await connectToDatabase();

    // Build query
    const query: Record<string, unknown> = {};
    if (isActive) {
      query.isActive = true;
    }
    if (isTaxable !== undefined) {
      query.isTaxable = isTaxable;
    }
    if (name) {
      query.name = { $regex: name, $options: 'i' };
    }

    // Count total documents
    const totalDocs = await Allowance.countDocuments(query);

    // Get allowances
    const allowances = await Allowance.find(query)
      .sort({ [sortField]: sortOrder === 'asc' ? 1 : -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name')
      .populate('applicableDepartments', 'name');

    // Calculate pagination info
    const totalPages = Math.ceil(totalDocs / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      data: {
        docs: allowances,
        totalDocs,
        page,
        totalPages,
        hasNextPage,
        hasPrevPage
      }
    });
  } catch (error: unknown) {
    logger.error('Error getting allowances', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get allowances', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/payroll/allowances
 * Create a new allowance
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.name || !body.code) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Check if an allowance with the same code already exists
    const existingAllowance = await Allowance.findOne({
      code: body.code
    });

    if (existingAllowance) {
      return NextResponse.json(
        { error: 'An allowance with the same code already exists' },
        { status: 400 }
      );
    }

    // Create allowance
    const allowance = new Allowance({
      ...body,
      createdBy: user.id
    });

    await allowance.save();

    return NextResponse.json({
      success: true,
      message: 'Allowance created successfully',
      data: allowance
    }, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error creating allowance', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to create allowance', details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import Allowance from '@/models/payroll/Allowance';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';

export const runtime = 'nodejs';

//  app/api/payroll/allowances/[id]/route.ts
// Custom auth system doesn't require authOptions;
/**
 * GET /api/payroll/allowances/[id]
 * Get an allowance by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  // Declare allowanceId at function scope
  let allowanceId: string = 'unknown';
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Resolve the params promise
    const { id } = await params;
    allowanceId = id;
    // Get allowance
    const allowance = await Allowance.findById(id)
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name')
      .populate('applicableDepartments', 'name');
    if (!allowance) {
      return NextResponse.json(
        { error: 'Allowance not found' },
        { status: 404 }
      );
    }
    return NextResponse.json({
      success: true,
      data: allowance
    });
  } catch (error) {
    logger.error(`Error getting allowance ${allowanceId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get allowance', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
/**
 * PATCH /api/payroll/allowances/[id]
 * Update an allowance
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  // Declare allowanceId at function scope
  let allowanceId: string = 'unknown';
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_DIRECTOR
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Get request body
    const body = await req.json();
    // Connect to database
    await connectToDatabase();
    // Resolve the params promise
    const { id } = await params;
    allowanceId = id;
    // Get allowance
    const allowance = await Allowance.findById(id);
    if (!allowance) {
      return NextResponse.json(
        { error: 'Allowance not found' },
        { status: 404 }
      );
    }
    // Check if code is being changed and if an allowance with the new code already exists
    if (body.code && body.code !== allowance.code) {
      const existingAllowance = await Allowance.findOne({
        _id: { $ne: id },
        code: body.code
      });
      if (existingAllowance) {
        return NextResponse.json(
          { error: 'An allowance with the same code already exists' },
          { status: 400 }
        );
      }
    }
    // Update allowance
    Object.assign(allowance, {
      ...body,
      updatedBy: user.id
    });
    await allowance.save();
    return NextResponse.json({
      success: true,
      message: 'Allowance updated successfully',
      data: allowance
    });
  } catch (error) {
    logger.error(`Error updating allowance ${allowanceId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to update allowance', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
/**
 * DELETE /api/payroll/allowances/[id]
 * Delete an allowance
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<Response> {
  // Declare allowanceId at function scope
  let allowanceId: string = 'unknown';
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Resolve the params promise
    const { id } = await params;
    allowanceId = id;
    // Get allowance
    const allowance = await Allowance.findById(id);
    if (!allowance) {
      return NextResponse.json(
        { error: 'Allowance not found' },
        { status: 404 }
      );
    }
    // Check if this allowance is being used by any employees
    const employeesUsingAllowance = await EmployeeSalary.countDocuments({
      'allowances.allowanceId': id,
      isActive: true
    });
    if (employeesUsingAllowance > 0) {
      return NextResponse.json(
        { error: 'Cannot delete an allowance that is being used by employees' },
        { status: 400 }
      );
    }
    // Delete allowance
    await allowance.deleteOne();
    return NextResponse.json({
      success: true,
      message: 'Allowance deleted successfully'
    });
  } catch (error) {
    logger.error(`Error deleting allowance ${allowanceId}`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to delete allowance', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
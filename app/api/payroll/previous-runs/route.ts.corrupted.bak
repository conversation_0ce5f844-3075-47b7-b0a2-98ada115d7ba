import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import PayrollRun from '@/models/payroll/PayrollRun';

/**
 * GET /api/payroll/previous-runs
 * Get previous payroll runs (completed, approved, paid)
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Connect to database
    await connectToDatabase();

    // Build query - only get completed, approved, or paid runs
    const query = {
      status: { $in: ['completed', 'approved', 'paid'] }
    };

    // Count total documents
    const totalDocs = await PayrollRun.countDocuments(query);

    // Get paginated results
    const docs = await PayrollRun.find(query)
      .sort({ [sortBy]: sortOrder === 'asc' ? 1 : -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate('createdBy', 'firstName lastName')
      .populate('approvedBy', 'firstName lastName')
      .populate('departments', 'name')
      .lean();

    // Calculate pagination info
    const totalPages = Math.ceil(totalDocs / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    // Log success
    logger.info('Successfully retrieved previous payroll runs', LogCategory.API, {
      count: docs.length,
      totalDocs,
      page,
      totalPages
    });

    return NextResponse.json({
      success: true,
      data: {
        docs,
        totalDocs,
        page,
        totalPages,
        hasNextPage,
        hasPrevPage,
        limit
      }
    });
  } catch (error: unknown) {
    logger.error('Error getting previous payroll runs', LogCategory.API, error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to get previous payroll runs', 
        details: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

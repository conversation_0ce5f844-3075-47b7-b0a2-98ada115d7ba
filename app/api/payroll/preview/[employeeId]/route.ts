import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { variableSalaryService } from '@/lib/services/payroll/variable-salary-service';
import Employee from '@/models/Employee';
import { errorService } from '@/lib/backend/services/error-service';
import { z } from 'zod';

export const runtime = 'nodejs';

// Schema for salary preview request
const salaryPreviewSchema = z.object({
  month: z.number().min(1).max(12),
  year: z.number().min(2020).max(2100),
  options: z.object({
    attendanceOptions: z.object({
      includeWeekends: z.boolean().optional(),
      includeHolidays: z.boolean().optional(),
      workingHoursPerDay: z.number().optional(),
      overtimeThreshold: z.number().optional(),
      lateThresholdMinutes: z.number().optional()
    }).optional(),
    taskOptions: z.object({
      includeInProgressTasks: z.boolean().optional(),
      qualityScoreThreshold: z.number().optional(),
      onTimeThresholdDays: z.number().optional(),
      performanceBonusThreshold: z.number().optional()
    }).optional(),
    skipRules: z.boolean().optional().default(false),
    overrides: z.object({
      basicSalary: z.number().optional(),
      hourlyRate: z.number().optional(),
      bonusAmount: z.number().optional(),
      deductionAmount: z.number().optional()
    }).optional()
  }).optional().default({})
});

/**
 * GET /api/payroll/preview/[employeeId]?month=1&year=2024
 * Preview salary calculation for an employee without saving
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ employeeId: string }> }
) {
  try {
    // Resolve params
    const { employeeId } = await params;

    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.EMPLOYEE // Allow employees to preview their own salary
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user._id.toString(),
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Check if employee exists
    const employee = await Employee.findById(employeeId);
    if (!employee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    // If user is an employee, they can only preview their own salary
    if (user.role === UserRole.EMPLOYEE && user.employeeId?.toString() !== employeeId) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user._id.toString(),
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: {
            requestedEmployeeId: employeeId,
            userEmployeeId: user.employeeId?.toString()
          }
        }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const month = parseInt(searchParams.get('month') || '');
    const year = parseInt(searchParams.get('year') || '');

    // Validate required parameters
    if (!month || !year) {
      return NextResponse.json(
        { error: 'Month and year are required query parameters' },
        { status: 400 }
      );
    }

    // Validate month and year ranges
    if (month < 1 || month > 12 || year < 2020 || year > 2100) {
      return NextResponse.json(
        { error: 'Invalid month (1-12) or year (2020-2100)' },
        { status: 400 }
      );
    }

    // Parse optional query parameters for overrides
    const overrides: any = {};
    if (searchParams.get('basicSalary')) {
      overrides.basicSalary = parseFloat(searchParams.get('basicSalary')!);
    }
    if (searchParams.get('hourlyRate')) {
      overrides.hourlyRate = parseFloat(searchParams.get('hourlyRate')!);
    }
    if (searchParams.get('bonusAmount')) {
      overrides.bonusAmount = parseFloat(searchParams.get('bonusAmount')!);
    }
    if (searchParams.get('deductionAmount')) {
      overrides.deductionAmount = parseFloat(searchParams.get('deductionAmount')!);
    }

    const skipRules = searchParams.get('skipRules') === 'true';

    // Preview salary calculation
    const result = await variableSalaryService.previewSalaryCalculation(
      employeeId,
      { month, year },
      {
        skipRules,
        overrides: Object.keys(overrides).length > 0 ? overrides : undefined
      }
    );

    // Check for calculation errors
    if (result.error) {
      return errorService.handlePayrollError(
        'SALARY_CALCULATION_ERROR',
        {
          userId: user._id.toString(),
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: {
            employeeId,
            employeeName: `${employee.firstName} ${employee.lastName}`,
            payPeriod: { month, year },
            error: result.error,
            message: result.message
          }
        }
      );
    }

    logger.info('Salary preview generated', LogCategory.PAYROLL, {
      userId: user._id.toString(),
      employeeId,
      employeeName: `${employee.firstName} ${employee.lastName}`,
      payPeriod: { month, year },
      calculationType: result.calculationType,
      grossSalary: result.grossSalary,
      netSalary: result.netSalary,
      hasOverrides: Object.keys(overrides).length > 0
    });

    return NextResponse.json({
      success: true,
      message: 'Salary preview generated successfully',
      data: {
        ...result,
        isPreview: true,
        previewGeneratedAt: new Date().toISOString(),
        previewGeneratedBy: user._id.toString()
      }
    });

  } catch (error) {
    logger.error('Error in salary preview API', LogCategory.API, error);
    
    return errorService.handlePayrollError(
      'CALCULATION_ERROR',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    );
  }
}

/**
 * POST /api/payroll/preview/[employeeId]
 * Preview salary calculation with detailed options
 */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ employeeId: string }> }
) {
  try {
    // Resolve params
    const { employeeId } = await params;

    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.EMPLOYEE // Allow employees to preview their own salary
    ]);

    if (!hasPermission) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user._id.toString(),
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Check if employee exists
    const employee = await Employee.findById(employeeId);
    if (!employee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    // If user is an employee, they can only preview their own salary
    if (user.role === UserRole.EMPLOYEE && user.employeeId?.toString() !== employeeId) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          userId: user._id.toString(),
          userRole: user.role,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: {
            requestedEmployeeId: employeeId,
            userEmployeeId: user.employeeId?.toString()
          }
        }
      );
    }

    // Get and validate request body
    const body = await req.json();
    const validationResult = salaryPreviewSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { month, year, options } = validationResult.data;

    // Preview salary calculation with detailed options
    const result = await variableSalaryService.previewSalaryCalculation(
      employeeId,
      { month, year },
      options
    );

    // Check for calculation errors
    if (result.error) {
      return errorService.handlePayrollError(
        'SALARY_CALCULATION_ERROR',
        {
          userId: user._id.toString(),
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: {
            employeeId,
            employeeName: `${employee.firstName} ${employee.lastName}`,
            payPeriod: { month, year },
            error: result.error,
            message: result.message
          }
        }
      );
    }

    logger.info('Detailed salary preview generated', LogCategory.PAYROLL, {
      userId: user._id.toString(),
      employeeId,
      employeeName: `${employee.firstName} ${employee.lastName}`,
      payPeriod: { month, year },
      calculationType: result.calculationType,
      grossSalary: result.grossSalary,
      netSalary: result.netSalary,
      hasOptions: Object.keys(options).length > 0
    });

    return NextResponse.json({
      success: true,
      message: 'Detailed salary preview generated successfully',
      data: {
        ...result,
        isPreview: true,
        previewGeneratedAt: new Date().toISOString(),
        previewGeneratedBy: user._id.toString(),
        previewOptions: options
      }
    });

  } catch (error) {
    logger.error('Error in detailed salary preview API', LogCategory.API, error);
    
    return errorService.handlePayrollError(
      'CALCULATION_ERROR',
      {
        endpoint: req.nextUrl.pathname,
        method: req.method,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { fetchExchangeRates, getLastUpdateTimestamp } from '@/lib/services/currencyExchangeService';
import { SupportedCurrency } from '@/lib/utils/currency';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

// app/api/system/currency-rates/route.ts
/**
 * API endpoint to get real-time currency exchange rates
 */
export async function GET(req: NextRequest) {
  const searchParams = req.nextUrl.searchParams;
  const baseCurrency = searchParams.get('base') as SupportedCurrency || 'MWK';
  logger.info('Currency exchange rates requested', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method,
    baseCurrency
  });
  try {
    // Fetch exchange rates
    const rates = await fetchExchangeRates(baseCurrency);
    const lastUpdated = getLastUpdateTimestamp();
    // Validate rates
    if (!rates || Object.keys(rates).length === 0) {
      throw new Error('Failed to get valid exchange rates');
    }
    logger.info('Currency exchange rates fetched successfully', LogCategory.API, {
      baseCurrency,
      timestamp: lastUpdated
    });
    return NextResponse.json({
      status: 'success',
      data: {
        base: baseCurrency,
        rates,
        timestamp: lastUpdated,
        date: new Date(lastUpdated).toISOString()
      }
    });
  } catch (error: unknown) {
    // Log the error with detailed information
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error('Failed to fetch currency exchange rates', LogCategory.API,
      error instanceof Error ? error : new Error(errorMessage),
      {
        baseCurrency,
        errorMessage,
        stack: error instanceof Error ? error.stack : undefined
      }
    );
    // Return fallback rates instead of an error
    try {
      // Get fallback rates directly from the currency service
      const fallbackRates = await fetchExchangeRates(baseCurrency);
      const lastUpdated = getLastUpdateTimestamp();
      logger.info('Using fallback currency exchange rates', LogCategory.API, {
        baseCurrency,
        timestamp: lastUpdated
      });
      return NextResponse.json({
        status: 'success',
        data: {
          base: baseCurrency,
          rates: fallbackRates,
          timestamp: lastUpdated,
          date: new Date(lastUpdated).toISOString(),
          isFallback: true
        }
      });
    } catch (fallbackError) {
      // If even fallback fails, return an error
      logger.error('Failed to get fallback currency rates', LogCategory.API,
        fallbackError instanceof Error ? fallbackError : new Error(String(fallbackError))
      );
      return NextResponse.json({
        status: 'error',
        message: 'Failed to fetch currency exchange rates',
        error: error instanceof Error ? error.message : String(error)
      }, { status: 500 });
    }
  }
}
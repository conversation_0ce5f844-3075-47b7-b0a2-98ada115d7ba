import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase, getDatabaseStatus } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

// app/api/system/db-status/route.ts
/**
 * API endpoint to check database connection status
 * This is useful for debugging connection issues
 */
export async function GET(req: NextRequest) {
  logger.info('Database status check requested', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });
  try {
    // Try to connect to the database
    logger.debug('Attempting to connect to database for status check', LogCategory.API);
    await connectToDatabase();
    // Get connection status
    const status = getDatabaseStatus();
    logger.info('Database status check successful', LogCategory.API, {
      isConnected: status.isConnected,
      details: status.details
    });
    return NextResponse.json({
      status: 'success',
      data: {
        isConnected: status.isConnected,
        details: status.details
      }
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Database status check failed', LogCategory.API,
      error instanceof Error ? error : new Error(errorMessage)
    );
    return NextResponse.json(
      {
        status: 'error',
        message: 'Failed to check database status',
        error: errorMessage
      },
      { status: 500 }
    );
  }
}
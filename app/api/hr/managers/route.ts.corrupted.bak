import { NextRequest, NextResponse } from 'next/server';
import { EmployeeService } from '@/lib/backend/services/hr/EmployeeService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';

// Initialize services
const employeeService = new EmployeeService();

/**
 * GET handler for managers
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(request: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD,
      UserRole.TEAM_LEADER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get search params
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '100');

    logger.info('Managers GET request', LogCategory.API, {
      page,
      limit
    });

    // Get employees who can be managers (department heads, team leaders, etc.)
    const managerRoles = [
      UserRole.DEPARTMENT_HEAD,
      UserRole.TEAM_LEADER,
      UserRole.HR_MANAGER,
      UserRole.HR_DIRECTOR
    ];

    // Get employees who can be managers
    const filter = { employmentStatus: 'active' };

    // Get paginated results
    const result = await employeeService.paginate(
      filter,
      page,
      limit,
      { lastName: 1, firstName: 1 }
    );

    // Log the response structure for debugging
    logger.info('Managers API response structure', LogCategory.API, {
      resultKeys: Object.keys(result),
      docsCount: result.docs?.length || 0
    });

    return NextResponse.json({
      status: 'success',
      docs: result.docs || [],
      totalDocs: result.totalDocs || 0,
      limit: result.limit || 100,
      totalPages: result.totalPages || 1,
      page: result.page || 1,
      hasPrevPage: result.hasPrevPage || false,
      hasNextPage: result.hasNextPage || false,
      prevPage: result.prevPage || null,
      nextPage: result.nextPage || null
    });
  } catch (error: unknown) {
    logger.error('Error in managers GET handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

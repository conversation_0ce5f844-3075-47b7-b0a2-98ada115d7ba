import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import Department from '@/models/Department';
import Employee from '@/models/Employee';

export const runtime = 'nodejs';

/**
 * POST handler for bulk deleting departments
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();
    // Get current user
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions - only certain roles can delete departments
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    // Get request body
    const body = await request.json();
    logger.info('Department bulk delete request', LogCategory.API, { 
      body,
      userId: user.id 
    });
    // Validate request
    if (!body.ids || !Array.isArray(body.ids) || body.ids.length === 0) {
      return NextResponse.json({
        error: 'Department IDs must be a non-empty array'
      }, { status: 400 });
    }
    const departmentIds = body.ids;
    const errors: Array<{ id: string; error: string }> = [];
    let deletedCount = 0;
    // Process each department
    for (const departmentId of departmentIds) {
      try {
        // Check if department exists
        const department = await Department.findById(departmentId);
        if (!department) {
          errors.push({
            id: departmentId,
            error: 'Department not found'
          });
          continue;
        }
        // Check if department has employees
        const employeeCount = await Employee.countDocuments({ 
          departmentId: departmentId 
        });
        if (employeeCount > 0) {
          errors.push({
            id: departmentId,
            error: `Cannot delete department "${department.name}" - it has ${employeeCount} employee(s) assigned. Please reassign employees first.`
          });
          continue;
        }
        // Delete the department
        await Department.findByIdAndDelete(departmentId);
        deletedCount++;
        logger.info('Department deleted successfully', LogCategory.API, {
          departmentId,
          departmentName: department.name,
          userId: user.id
        });
      } catch (error) {
        logger.error('Error deleting department', LogCategory.API, {
          departmentId,
          error,
          userId: user.id
        });
        errors.push({
          id: departmentId,
          error: error instanceof Error ? error.message : 'Unknown error occurred'
        });
      }
    }
    // Log the final result
    logger.info('Department bulk delete completed', LogCategory.API, {
      totalRequested: departmentIds.length,
      deletedCount,
      errorCount: errors.length,
      userId: user.id
    });
    // Return result
    return NextResponse.json({
      success: true,
      deletedCount,
      errors
    });
  } catch (error: unknown) {
    logger.error('Error in department bulk delete handler', LogCategory.API, error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'An error occurred' 
    }, { status: 500 });
  }
}
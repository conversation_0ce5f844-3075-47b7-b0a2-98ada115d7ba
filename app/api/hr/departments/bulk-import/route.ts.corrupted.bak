// app/api/hr/departments/bulk-import/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { UserRole } from '@/types/user-roles'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import Department from '@/models/Department'
import { connectToDatabase } from '@/lib/backend/database'
import mongoose from 'mongoose'
import * as XLSX from 'xlsx'
// import crypto from 'crypto'
import logger, { LogCategory } from '@/lib/backend/utils/logger'

export const runtime = 'nodejs';



// Define the expected columns in the import file
const REQUIRED_COLUMNS = [
  'name'
]

// Define a mapping between display names in the template and the expected field names
const COLUMN_DISPLAY_MAPPING: Record<string, string> = {
  'name': 'name', // Direct mapping for 'name' column
  'Name': 'name',
  'Description': 'description',
  'Budget': 'budget',
  'Budget Allocation': 'budget',
  'Department Code': 'departmentCode',
  'Status': 'status',
  'Head of Department': 'headTitle',
  'Location': 'location',
  'Established Date': 'establishedDate',
  'Employee Count': 'employeeCount',
  'Contact Email': 'contactEmail',
  'Contact Phone': 'contactPhone'
}

// Define roles that can manage departments
const DEPARTMENT_ADMIN_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER
]

/**
 * POST handler for bulk importing departments
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, DEPARTMENT_ADMIN_ROLES)

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Connect to database
    await connectToDatabase()

    // Debug: Check model registration
    logger.debug('Model registration check', LogCategory.IMPORT, {
      registeredModels: Object.keys(mongoose.models),
      departmentModelExists: !!mongoose.models.Department,
      departmentSchema: Department.schema ? Object.keys(Department.schema.paths) : 'No schema'
    })

    // Get form data
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Check file type
    const fileType = file.name.split('.').pop()?.toLowerCase()
    if (fileType !== 'csv' && fileType !== 'xlsx' && fileType !== 'xls') {
      return NextResponse.json({ error: 'Invalid file type. Please upload a CSV or Excel file' }, { status: 400 })
    }

    // Read file
    const buffer = await file.arrayBuffer()

    logger.info('Processing department bulk import', LogCategory.IMPORT, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      userId: user.id
    })

    const workbook = XLSX.read(buffer, { type: 'array' })

    // Get first sheet
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]

    // Convert to JSON with header row mapping
    const rows = XLSX.utils.sheet_to_json(worksheet, {
      defval: null,
      raw: false, // Convert all data to strings
      blankrows: false // Skip blank rows
    }) as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[]

    // Validate and process data
    const result = {
      totalRows: rows.length,
      successCount: 0,
      errorCount: 0,
      errors: [] as Array<{ row: number, error: string }>
    }

    // Check if file is empty
    if (rows.length === 0) {
      logger.warn('Empty file uploaded for department import', LogCategory.IMPORT, {
        userId: user.id
      })
      return NextResponse.json({ error: 'File is empty' }, { status: 400 })
    }

    // Log the first row for debugging
    logger.debug('First row of import file', LogCategory.IMPORT, {
      firstRow: rows[0],
      keys: '********'
    })

    // Create a mapping between the columns in the file and our expected fields
    const availableColumns = Object.keys(rows[0])
    const columnMap: Record<string, string> = {}

    // Try to map columns based on exact matches or display name mapping
    for (const expectedField of Object.keys(COLUMN_DISPLAY_MAPPING)) {
      const mappedField = COLUMN_DISPLAY_MAPPING[expectedField]

      // Check if the expected field name exists in the file
      if (availableColumns.includes(expectedField)) {
        columnMap[mappedField] = expectedField
      }
      // Check if the mapped field name exists in the file
      else if (availableColumns.includes(mappedField)) {
        columnMap[mappedField] = mappedField
      }
    }

    // Also map any columns that aren't in the display mapping but match our field names directly
    for (const column of availableColumns) {
      const lowerColumn = column.toLowerCase().replace(/\s+/g, '')

      // Direct field mappings for common variations
      if (column === 'name' && !columnMap['name']) {
        columnMap['name'] = column
      } else if (column === 'Description' && !columnMap['description']) {
        columnMap['description'] = column
      } else if (column === 'Budget Allocation' && !columnMap['budget']) {
        columnMap['budget'] = column
      } else if (column === 'Department Code' && !columnMap['departmentCode']) {
        columnMap['departmentCode'] = column
      } else if (column === 'Status' && !columnMap['status']) {
        columnMap['status'] = column
      } else if (column === 'Location' && !columnMap['location']) {
        columnMap['location'] = column
      } else if (column === 'Established Date' && !columnMap['establishedDate']) {
        columnMap['establishedDate'] = column
      } else if (column === 'Contact Email' && !columnMap['contactEmail']) {
        columnMap['contactEmail'] = column
      } else if (column === 'Contact Phone' && !columnMap['contactPhone']) {
        columnMap['contactPhone'] = column
      } else if (column === 'Head of Department' && !columnMap['headTitle']) {
        columnMap['headTitle'] = column
      }
    }

    logger.debug('Column mapping', LogCategory.IMPORT, {
      columnMap,
      availableColumns
    })

    // Process each row
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i]
      let normalizedRow: Record<string, any> = {}

      // Map columns from the file to our expected fields
      for (const [field, column] of Object.entries(columnMap)) {
        normalizedRow[field] = row[column]
      }

      try {
        // Log the row being processed for debugging
        logger.debug('Processing row', LogCategory.IMPORT, {
          rowIndex: i + 1,
          normalizedRow
        })

        // Validate required fields
        for (const field of REQUIRED_COLUMNS) {
          if (!normalizedRow[field]) {
            throw new Error(`Missing required field: ${field}`)
          }
        }

        // Convert budget to number if provided
        if (normalizedRow.budget) {
          normalizedRow.budget = Number(normalizedRow.budget)
          if (isNaN(normalizedRow.budget)) {
            throw new Error('Budget must be a number')
          }
        }

        // Log the normalized row data for debugging
        logger.debug('Normalized row data', LogCategory.IMPORT, {
          rowIndex: i + 1,
          normalizedRow: {
            name: normalizedRow.name,
            description: normalizedRow.description,
            budget: normalizedRow.budget,
            departmentCode: normalizedRow.departmentCode,
            location: normalizedRow.location,
            establishedDate: normalizedRow.establishedDate,
            contactEmail: normalizedRow.contactEmail,
            contactPhone: normalizedRow.contactPhone,
            status: normalizedRow.status,
            headTitle: normalizedRow.headTitle
          }
        })

        // Special debug for headTitle field
        logger.debug('HeadTitle field analysis', LogCategory.IMPORT, {
          rowIndex: i + 1,
          rawRowData: row,
          headOfDepartmentColumn: row['Head of Department'],
          normalizedHeadTitle: normalizedRow.headTitle,
          columnMapForHeadTitle: columnMap['headTitle'],
          hasHeadTitleInColumnMap: 'headTitle' in columnMap
        })

        // Create department with enhanced fields
        const departmentData: any = {
          name: normalizedRow.name,
          description: normalizedRow.description || undefined,
          budget: normalizedRow.budget || undefined,
          // Enhanced fields for TCM organizational structure
          departmentCode: normalizedRow.departmentCode || undefined,
          location: normalizedRow.location || undefined,
          establishedDate: normalizedRow.establishedDate ? new Date(normalizedRow.establishedDate) : undefined,
          contactEmail: normalizedRow.contactEmail || undefined,
          contactPhone: normalizedRow.contactPhone || undefined,
          status: normalizedRow.status ? normalizedRow.status.toLowerCase() : 'active'
          // Note: employeeCount is ignored as it's calculated dynamically
        }

        // Only add headTitle if it has a value (not undefined, null, or empty string)
        if (normalizedRow.headTitle && normalizedRow.headTitle.trim() !== '') {
          departmentData.headTitle = normalizedRow.headTitle.trim()
        }

        // Log the department data that will be saved
        logger.debug('Department data to be saved', LogCategory.IMPORT, {
          rowIndex: i + 1,
          departmentData
        })

        // Check if department already exists
        const existingDepartment = await Department.findOne({ name: departmentData.name })
        if (existingDepartment) {
          throw new Error(`Department with name ${departmentData.name} already exists`)
        }

        // Debug: Final check before creation
        logger.debug('About to create department', LogCategory.IMPORT, {
          rowIndex: i + 1,
          departmentData,
          modelName: Department.modelName,
          collectionName: Department.collection.name,
          schemaFields: Object.keys(Department.schema.paths)
        })

        // Create department
        const createdDepartment = await Department.create(departmentData)
        result.successCount++

        // Debug: Check what was actually saved
        logger.debug('Department created - checking saved data', LogCategory.IMPORT, {
          rowIndex: i + 1,
          createdId: createdDepartment._id,
          savedFields: Object.keys(createdDepartment.toObject())
        })

        logger.info('Department created successfully', LogCategory.IMPORT, {
          rowIndex: i + 1,
          name: departmentData.name,
          userId: user.id
        })
      } catch (error: unknown) {
        result.errorCount++

        // Log the error
        logger.error('Error processing department row', LogCategory.IMPORT, {
          rowIndex: i + 1,
          error: error instanceof Error ? error.message : 'An unknown error occurred',
          row: normalizedRow || row,
          userId: user.id
        })

        result.errors.push({
          row: i + 1,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Log the final result
    logger.info('Department bulk import completed', LogCategory.IMPORT, {
      totalRows: result.totalRows,
      successCount: result.successCount,
      errorCount: result.errorCount,
      userId: user.id
    })

    return NextResponse.json({
      status: 'success',
      data: result
    })
  } catch (error: unknown) {
    console.error('Error in department bulk import:', error)
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred during department bulk import' }, { status: 500 })
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { LeaveService } from '@/lib/backend/services/hr/LeaveService';
import { LeaveImportExportService } from '@/lib/backend/services/hr/LeaveImportExportService';
import { LeaveReportingService } from '@/lib/backend/services/hr/LeaveReportingService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { getCurrentUser } from '@/lib/backend/auth/auth';

export const runtime = 'nodejs';

// app/api/hr/leave/route.ts
// Auto-generated type definitions
interface MongoFilter {
  [key: string]: any;
  employeeId?: string;
  status?: string;
  leaveType?: string;
  startDate?: Date;
  endDate?: Date;
  $or?: Array<Record<string, any>>;
}
// Initialize services
const leaveService = new LeaveService();
const importExportService = new LeaveImportExportService();
const reportingService = new LeaveReportingService();
/**
 * GET handler for leave requests
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(request: NextRequest) {
  try {
    // Get current user
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD,
      UserRole.EMPLOYEE
    ]);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const employeeId = searchParams.get('employeeId');
    const status = searchParams.get('status');
    const leaveType = searchParams.get('leaveType');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const format = searchParams.get('format');
    const report = searchParams.get('report');
    // Handle different request types
    if (id) {
      // Get leave request by ID
      const leave = await leaveService.getLeaveDetails(id);
      if (!leave) {
        return NextResponse.json({ error: 'Leave request not found' }, { status: 404 });
      }
      // Check if user has permission to view this leave request
      const isHR = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST
      ]);
      const isDepartmentHead = hasRequiredPermissions(user, [
        UserRole.DEPARTMENT_HEAD
      ]);
      const isOwnLeave = leave.employeeId.toString() === user.id;
      if (!isHR && !isOwnLeave && !isDepartmentHead) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
      return NextResponse.json(leave);
    } else if (format) {
      // Export leave requests
      // Check if user has export permissions
      const hasExportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST,
        UserRole.DEPARTMENT_HEAD
      ]);
      if (!hasExportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
      // Build filter
    const filter: MongoFilter = {};
      // Add filters
      if (employeeId) {
        filter.employeeId = employeeId;
      }
      if (status) {
        filter.status = status;
      }
      if (leaveType) {
        filter.leaveType = leaveType;
      }
      // Generate export
      let buffer: Buffer;
      let contentType: string;
      let filename: string;
      if (format === 'excel') {
        if (startDate && endDate) {
          // Export leave calendar
          buffer = await importExportService.exportLeaveCalendar(filter, {
            startDate: new Date(startDate),
            endDate: new Date(endDate),
            includeRejected: false
          });
          filename = 'leave-calendar.xlsx';
        } else {
          // Export leave requests
          buffer = await importExportService.exportToExcel(filter);
          filename = 'leave-requests.xlsx';
        }
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      } else if (format === 'csv') {
        buffer = await importExportService.exportToCsv(filter);
        contentType = 'text/csv';
        filename = 'leave-requests.csv';
      } else {
        return NextResponse.json({ error: 'Invalid format' }, { status: 400 });
      }
      // Convert Buffer to Uint8Array which is acceptable for NextResponse
      const uint8Array = new Uint8Array(buffer);
      return new NextResponse(uint8Array, {
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${filename}"`
        }
      });
    } else if (report) {
      // Generate report
      // Check if user has report permissions
      const hasReportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST,
        UserRole.DEPARTMENT_HEAD
      ]);
      if (!hasReportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
      if (report === 'summary') {
        // Generate summary report
        if (!startDate || !endDate) {
          return NextResponse.json({ error: 'Start date and end date are required' }, { status: 400 });
        }
        const departmentId = searchParams.get('departmentId');
        const reportData = await reportingService.generateSummaryReport({
          startDate: new Date(startDate),
          endDate: new Date(endDate),
          departmentId: departmentId || undefined
        });
        return NextResponse.json(reportData);
      } else if (report === 'calendar') {
        // Generate calendar report
        if (!startDate || !endDate) {
          return NextResponse.json({ error: 'Start date and end date are required' }, { status: 400 });
        }
        const departmentId = searchParams.get('departmentId');
        const pdfBuffer = await reportingService.generateCalendarReport({
          startDate: new Date(startDate),
          endDate: new Date(endDate),
          departmentId: departmentId || undefined,
          leaveType: leaveType || undefined
        });
        // Convert Buffer to Uint8Array which is acceptable for NextResponse
        const uint8Array = new Uint8Array(pdfBuffer);
        return new NextResponse(uint8Array, {
          headers: {
            'Content-Type': 'application/pdf',
            'Content-Disposition': 'attachment; filename="leave-calendar.pdf"'
          }
        });
      } else if (report === 'employee') {
        // Generate employee leave report
        if (!employeeId) {
          return NextResponse.json({ error: 'Employee ID is required' }, { status: 400 });
        }
        const year = parseInt(searchParams.get('year') || new Date().getFullYear().toString());
        const reportData = await reportingService.generateEmployeeLeaveReport(employeeId, {
          year
        });
        return NextResponse.json(reportData);
      } else {
        return NextResponse.json({ error: 'Invalid report type' }, { status: 400 });
      }
    } else if (employeeId) {
      // Get leave requests by employee
      // Check if user has permission to view employee's leave requests
      const isHR = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST
      ]);
      const isDepartmentHead = hasRequiredPermissions(user, [
        UserRole.DEPARTMENT_HEAD
      ]);
      const isOwnLeave = employeeId === user.id;
      if (!isHR && !isOwnLeave && !isDepartmentHead) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
      // Build options
      const options: Record<string, any> = {
        page,
        limit,
        sort: { startDate: -1 }
      };
      if (status) {
        options.status = status.split(',');
      }
      if (startDate && endDate) {
        options.startDate = new Date(startDate);
        options.endDate = new Date(endDate);
      }
      // Get leave requests
      const result = await leaveService.getByEmployee(employeeId, options);
      return NextResponse.json(result);
    } else if (startDate && endDate) {
      // Get leave requests by date range
      // Check if user has permission to view all leave requests
      const hasViewAllPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST,
        UserRole.DEPARTMENT_HEAD
      ]);
      if (!hasViewAllPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
      // Build options
      const options: Record<string, any> = {
        page,
        limit,
        sort: { startDate: 1 }
      };
      if (status) {
        options.status = status.split(',');
      }
      if (leaveType) {
        options.leaveType = leaveType;
      }
      // Get leave requests
      const result = await leaveService.getByDateRange(
        new Date(startDate),
        new Date(endDate),
        options
      );
      return NextResponse.json(result);
    } else if (status === 'pending') {
      // Get pending leave requests
      // Check if user has permission to view pending leave requests
      const hasViewPendingPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST,
        UserRole.DEPARTMENT_HEAD
      ]);
      if (!hasViewPendingPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
      // Build options
      const options: Record<string, any> = {
        page,
        limit,
        sort: { createdAt: 1 }
      };
      if (leaveType) {
        options.leaveType = leaveType;
      }
      // Get pending leave requests
      const result = await leaveService.getPendingLeaves(options);
      return NextResponse.json(result);
    } else {
      // Get all leave requests with pagination
      // Check if user has permission to view all leave requests
      const hasViewAllPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST
      ]);
      if (!hasViewAllPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
      // Build filter
    const filter: MongoFilter = {};
      if (status) {
        filter.status = status;
      }
      if (leaveType) {
        filter.leaveType = leaveType;
      }
      // Get leave requests
      const result = await leaveService.paginate(filter, page, limit, { createdAt: -1 }, ['employeeId', 'approvedBy', 'createdBy']);
      return NextResponse.json(result);
    }
  } catch (error: unknown) {
    logger.error('Error in leave requests GET handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while processing leave request' }, { status: 500 });
  }
}
/**
 * POST handler for leave requests
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Get current user
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.EMPLOYEE
    ]);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    // Get request body
    const body = await request.json();
    // Check if import request
    if (body.import) {
      // Check if user has import permissions
      const hasImportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.HR_MANAGER
      ]);
      if (!hasImportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
      // Get file buffer
      const fileBuffer = Buffer.from(body.fileData, 'base64');
      // Import data
      const importResult = body.format === 'csv'
        ? await importExportService.importFromCsv(fileBuffer, body.options)
        : await importExportService.importFromExcel(fileBuffer, body.options);
      return NextResponse.json(importResult);
    }
    // Set created by
    body.createdBy = user.id;
    // If user is an employee, set employeeId to their own ID
    if (
      !hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST
      ])
    ) {
      body.employeeId = user.id;
    }
    // Check for overlapping leave
    const hasOverlap = await leaveService.hasOverlappingLeave(
      body.employeeId,
      new Date(body.startDate),
      new Date(body.endDate)
    );
    if (hasOverlap) {
      return NextResponse.json({
        error: 'Employee already has an approved or pending leave request that overlaps with this period'
      }, { status: 400 });
    }
    // Create leave request
    const leave = await leaveService.createLeave(body);
    return NextResponse.json(leave, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in leave requests POST handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while creating leave request' }, { status: 500 });
  }
}
/**
 * PATCH handler for leave requests
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function PATCH(request: NextRequest) {
  try {
    // Get current user
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Get request body
    const body = await request.json();
    // Check if status update
    if (body.id && body.status) {
      // Get leave request
      const leave = await leaveService.findById(body.id);
      if (!leave) {
        return NextResponse.json({ error: 'Leave request not found' }, { status: 404 });
      }
      // Check permissions for status update
      if (body.status === 'approved' || body.status === 'rejected') {
        // Only HR or department heads can approve/reject
        const hasApprovePermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.HR_MANAGER,
          UserRole.HR_SPECIALIST,
          UserRole.DEPARTMENT_HEAD
        ]);
        if (!hasApprovePermission) {
          return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
        }
      } else if (body.status === 'cancelled') {
        // Only the employee who created the leave or HR can cancel
        const isOwnLeave = leave.employeeId.toString() === user.id;
        const hasHRPermission = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.HR_MANAGER,
          UserRole.HR_SPECIALIST
        ]);
        if (!isOwnLeave && !hasHRPermission) {
          return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
        }
      }
      // Update status
      const updatedLeave = await leaveService.updateStatus(
        body.id,
        body.status,
        user.id,
        body.rejectionReason
      );
      return NextResponse.json(updatedLeave);
    } else if (body.id) {
      // Get leave request
      const leave = await leaveService.findById(body.id);
      if (!leave) {
        return NextResponse.json({ error: 'Leave request not found' }, { status: 404 });
      }
      // Check permissions for update
      const isOwnLeave = leave.employeeId.toString() === user.id;
      const hasHRPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST
      ]);
      if (!isOwnLeave && !hasHRPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
      // Only allow updates to pending leave requests
      if (leave.status !== 'pending') {
        return NextResponse.json({
          error: 'Only pending leave requests can be updated'
        }, { status: 400 });
      }
      // Check for date changes
      if (
        (body.startDate && new Date(body.startDate).getTime() !== leave.startDate.getTime()) ||
        (body.endDate && new Date(body.endDate).getTime() !== leave.endDate.getTime())
      ) {
        // Check for overlapping leave
        const hasOverlap = await leaveService.hasOverlappingLeave(
          leave.employeeId.toString(),
          new Date(body.startDate || leave.startDate),
          new Date(body.endDate || leave.endDate),
          body.id
        );
        if (hasOverlap) {
          return NextResponse.json({
            error: 'Employee already has an approved or pending leave request that overlaps with this period'
          }, { status: 400 });
        }
        // Calculate duration if dates changed
        if (!body.duration) {
          // Calculate duration manually since calculateDuration is private
          const startDate = new Date(body.startDate || leave.startDate);
          const endDate = new Date(body.endDate || leave.endDate);
          // Simple duration calculation in days
          const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end days
          body.duration = diffDays;
        }
      }
      // Update leave request
      const updatedLeave = await leaveService.updateById(body.id, body);
      return NextResponse.json(updatedLeave);
    } else {
      return NextResponse.json({ error: 'Missing ID' }, { status: 400 });
    }
  } catch (error: unknown) {
    logger.error('Error in leave requests PATCH handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while updating leave request' }, { status: 500 });
  }
}
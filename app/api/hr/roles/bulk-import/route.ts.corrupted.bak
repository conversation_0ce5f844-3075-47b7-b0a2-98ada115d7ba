// app/api/hr/roles/bulk-import/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { UserRole } from '@/types/user-roles'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import Role from '@/models/Role'
import Department from '@/models/Department'
import { connectToDatabase } from '@/lib/backend/database'
import * as XLSX from 'xlsx'
import logger, { LogCategory } from '@/lib/backend/utils/logger'

// Define roles that can manage roles
const ROLE_ADMIN_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER
]

// Define the expected columns in the import file
const REQUIRED_COLUMNS = [
  'name',
  'code'
]

// Define a mapping between display names in the template and the expected field names
const COLUMN_DISPLAY_MAPPING: Record<string, string> = {
  'Name': 'name',
  'Code': 'code',
  'Description': 'description',
  'Department': 'department',
  'Is Active': 'isActive'
}

/**
 * POST handler for bulk importing roles
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, ROLE_ADMIN_ROLES)

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      )
    }

    // Connect to database
    await connectToDatabase()

    // Get form data
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    // Check file type
    const fileType = file.name.split('.').pop()?.toLowerCase()
    if (fileType !== 'csv' && fileType !== 'xlsx' && fileType !== 'xls') {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload a CSV or Excel file' },
        { status: 400 }
      )
    }

    // Read file
    const buffer = await file.arrayBuffer()

    logger.info('Processing role bulk import', LogCategory.IMPORT, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      userId: user.id
    })

    const workbook = XLSX.read(buffer, { type: 'array' })

    // Get first sheet
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]

    // Convert to JSON with header row mapping
    const rows = XLSX.utils.sheet_to_json(worksheet, {
      defval: null,
      raw: false, // Convert all data to strings
      blankrows: false // Skip blank rows
    }) as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[] as Record<string, string>[]

    // Validate and process data
    const result = {
      totalRows: rows.length,
      successCount: 0,
      errorCount: 0,
      errors: [] as Array<{ row: number, error: string }>
    }

    // Check if file is empty
    if (rows.length === 0) {
      logger.warn('Empty file uploaded for role import', LogCategory.IMPORT, {
        userId: user.id
      })
      return NextResponse.json(
        { error: 'File is empty' },
        { status: 400 }
      )
    }

    // Get all departments for reference
    const departments = await Department.find({}).lean()
    const departmentMap = new Map()
    departments.forEach(dept => {
      departmentMap.set(dept.name.toLowerCase(), dept._id)
    })

    // Process each row
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i]
      let normalizedRow: Record<string, any> = {}

      // Normalize column names based on mapping
      for (const [key, value] of Object.entries(row)) {
        const normalizedKey = COLUMN_DISPLAY_MAPPING[key] || key.toLowerCase()
        normalizedRow[normalizedKey] = value
      }

      try {
        // Log the row being processed for debugging
        logger.debug('Processing row', LogCategory.IMPORT, {
          rowIndex: i + 1,
          normalizedRow
        })

        // Validate required fields
        for (const field of REQUIRED_COLUMNS) {
          if (!normalizedRow[field]) {
            throw new Error(`Missing required field: ${field}`)
          }
        }

        // Process department field
        let departmentId = undefined
        if (normalizedRow.department) {
          const deptName = normalizedRow.department.toString().toLowerCase()
          departmentId = departmentMap.get(deptName)
          if (!departmentId && normalizedRow.department !== 'all_departments') {
            throw new Error(`Department "${normalizedRow.department}" not found`)
          }
        }

        // Process isActive field
        let isActive = true
        if (normalizedRow.isActive !== null && normalizedRow.isActive !== undefined) {
          const isActiveStr = normalizedRow.isActive.toString().toLowerCase()
          isActive = isActiveStr === 'true' || isActiveStr === 'yes' || isActiveStr === '1'
        }

        // Create role data
        const roleData = {
          name: normalizedRow.name,
          code: normalizedRow.code.toLowerCase().replace(/[^a-z0-9_]/g, '_'),
          description: normalizedRow.description || undefined,
          department: departmentId,
          isActive: isActive,
          createdBy: user.id
        }

        // Check if role with same code already exists
        const existingRole = await Role.findOne({ code: roleData.code })
        if (existingRole) {
          throw new Error(`Role with code ${roleData.code} already exists`)
        }

        // Create role
        await Role.create(roleData)
        result.successCount++

        logger.info('Role created successfully', LogCategory.IMPORT, {
          rowIndex: i + 1,
          name: roleData.name,
          code: roleData.code,
          userId: user.id
        })
      } catch (error: unknown) {
        result.errorCount++

        // Log the error
        logger.error('Error processing role row', LogCategory.IMPORT, {
          rowIndex: i + 1,
          error: error instanceof Error ? error.message : 'An unknown error occurred',
          row: normalizedRow || row,
          userId: user.id
        })

        result.errors.push({
          row: i + 1,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Log the final result
    logger.info('Role bulk import completed', LogCategory.IMPORT, {
      totalRows: result.totalRows,
      successCount: result.successCount,
      errorCount: result.errorCount,
      userId: user.id
    })

    return NextResponse.json({
      status: 'success',
      data: result
    })
  } catch (error: unknown) {
    console.error('Error in role bulk import:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred during role bulk import' },
      { status: 500 }
    )
  }
}

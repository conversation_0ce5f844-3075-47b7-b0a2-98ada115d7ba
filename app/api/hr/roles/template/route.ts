import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions'
import { UserRole } from '@/types/user-roles'
import { connectToDatabase } from '@/lib/backend/database'
import Department from '@/models/Department'
import * as XLSX from 'xlsx'
import logger, { LogCategory } from '@/lib/backend/utils/logger'

export const runtime = 'nodejs'

// Define roles that can manage roles
const ROLE_ADMIN_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER
]

/**
 * GET handler for generating a role import template
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, ROLE_ADMIN_ROLES)

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      )
    }

    // Connect to database
    await connectToDatabase()

    // Get all departments
    const departments = await Department.find({ isActive: true }).select('name').lean()

    // Create template headers
    const headers = ['Name', 'Code', 'Description', 'Department', 'Is Active']

    // Create sample data rows
    const sampleData = [
      ['HR Manager', 'hr_manager', 'Human Resources Manager role', 'Human Resources', 'TRUE'],
      ['Finance Specialist', 'finance_specialist', 'Finance department specialist', 'Finance', 'TRUE'],
      ['IT Support', 'it_support', 'IT support staff', 'Information Technology', 'TRUE'],
      ['General Staff', 'general_staff', 'General staff role for all departments', 'all_departments', 'TRUE']
    ]

    // Create a worksheet
    const ws = XLSX.utils.aoa_to_sheet([headers, ...sampleData])

    // Add data validation for Department column
    const departmentNames = ['all_departments', ...departments.map(d => d.name)]

    // Create a workbook
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, 'Roles')

    // Add a departments reference sheet
    const departmentsSheet = XLSX.utils.aoa_to_sheet([
      ['Available Departments'],
      ['all_departments (use this for roles that apply to all departments)'],
      ...departments.map(d => [d.name])
    ])
    XLSX.utils.book_append_sheet(wb, departmentsSheet, 'Departments Reference')

    // Generate Excel file
    const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' })

    // Log template generation
    logger.info('Role import template generated', LogCategory.EXPORT, {
      userId: user.id,
      departmentsCount: departments.length
    })

    // Return the template file
    // Convert Buffer to Uint8Array which is acceptable for NextResponse
    const uint8Array = new Uint8Array(buffer);

    return new NextResponse(uint8Array, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="role-import-template.xlsx"',
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  } catch (error: unknown) {
    console.error('Error generating role import template:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred while generating role import template' },
      { status: 500 }
    )
  }
}
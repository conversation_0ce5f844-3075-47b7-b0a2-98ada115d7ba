import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import Role from '@/models/Role';
import User from '@/models/User';

export const runtime = 'nodejs';

// app/api/hr/roles/[id]/route.ts
// Define roles that can manage roles
const ROLE_ADMIN_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER
];
/**
 * GET /api/hr/roles/[id]
 * Get a role by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Resolve the params promise
    const { id } = await params;
    // Get role
    const role = await Role.findById(id)
      .populate('department', 'name')
      .populate('createdBy', 'firstName lastName')
      .populate('updatedBy', 'firstName lastName');
    if (!role) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 404 }
      );
    }
    return NextResponse.json({
      success: true,
      data: role
    });
  } catch (error: unknown) {
    logger.error(`Error getting role`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to get role', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
/**
 * PUT /api/hr/roles/[id]
 * Update a role
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, ROLE_ADMIN_ROLES);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Resolve the params promise
    const { id } = await params;
    // Get request body
    const body = await req.json();
    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    // Check if role exists
    const role = await Role.findById(id);
    if (!role) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 404 }
      );
    }
    // Update role
    role.name = body.name;
    role.description = body.description;
    role.isActive = body.isActive;
    role.permissions = body.permissions;
    // Handle department field
    if (body.department === "all_departments") {
      role.department = undefined; // Set to undefined to remove the department reference
    } else {
      role.department = body.department;
    }
    role.updatedBy = user.id;
    await role.save();
    return NextResponse.json({
      success: true,
      data: role
    });
  } catch (error: unknown) {
    logger.error(`Error updating role`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to update role', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
/**
 * DELETE /api/hr/roles/[id]
 * Delete a role
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, ROLE_ADMIN_ROLES);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Resolve the params promise
    const { id } = await params;
    // Check if role is in use
    const usersWithRole = await User.countDocuments({ role: id });
    if (usersWithRole > 0) {
      return NextResponse.json(
        { error: 'Cannot delete role that is assigned to users' },
        { status: 400 }
      );
    }
    // Delete role
    const result = await Role.findByIdAndDelete(id);
    if (!result) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 404 }
      );
    }
    return NextResponse.json({
      success: true,
      message: 'Role deleted successfully'
    });
  } catch (error: unknown) {
    logger.error(`Error deleting role`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to delete role', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
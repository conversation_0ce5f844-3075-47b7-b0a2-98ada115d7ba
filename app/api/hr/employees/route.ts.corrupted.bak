// app/api/hr/employees/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { EmployeeService, EmployeeImportExportService, EmployeeReportingService } from '@/lib/backend/services/hr/EmployeeService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';

// Initialize services
const employeeService = new EmployeeService();
const importExportService = new EmployeeImportExportService();
const reportingService = new EmployeeReportingService();

/**
 * GET handler for employees
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(request: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - Allow all authenticated users to view employee data
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD,
      UserRole.TEAM_LEADER,
      UserRole.RECRUITER,
      UserRole.EMPLOYEE,
      UserRole.CONTRACTOR,
      UserRole.INTERN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get search params
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    const departmentId = searchParams.get('departmentId');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const format = searchParams.get('format');
    const report = searchParams.get('report');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    logger.info('Employee GET request', LogCategory.API, {
      id,
      departmentId,
      search,
      status,
      format,
      report,
      page,
      limit
    });

    // Handle different request types
    if (id) {
      // Get employee by ID
      const employee = await employeeService.getEmployeeDetails(id);

      if (!employee) {
        return NextResponse.json({ error: 'Employee not found' }, { status: 404 });
      }

      return NextResponse.json(employee);
    } else if (format) {
      // Export employees
      // Check if user has export permissions
      const hasExportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST
      ]);

      if (!hasExportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Build filter
      const filter: Record<string, any> = {};

      if (status) {
        filter.employmentStatus = status;
      }

      if (departmentId) {
        filter.departmentId = departmentId;
      }

      // Export employees
      const exportData = await importExportService.exportData(format, filter);

      // Convert Buffer to Uint8Array which is acceptable for NextResponse
      const uint8Array = new Uint8Array(exportData.data);

      return new NextResponse(uint8Array, {
        headers: {
          'Content-Type': exportData.contentType,
          'Content-Disposition': `attachment; filename="employees.${format}"`
        }
      });
    } else if (report) {
      // Generate report
      // Check if user has report permissions
      const hasReportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST,
        UserRole.DEPARTMENT_HEAD
      ]);

      if (!hasReportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      if (report === 'summary') {
        // Generate summary report
        const reportData = await reportingService.generateSummaryReport({
          departmentId: departmentId || undefined,
          status: status || undefined
        });

        return NextResponse.json(reportData);
      } else if (report === 'department' && departmentId) {
        // Generate department report
        // Get employees by department and format the data
        const employees = await employeeService.getByDepartment(departmentId);

        // Format the data as a report
        const reportData = {
          title: 'Department Employee Report',
          departmentId,
          generatedAt: new Date(),
          employees: employees.docs || []
        };

        return NextResponse.json(reportData);
      } else {
        return NextResponse.json({ error: 'Invalid report type' }, { status: 400 });
      }
    } else if (departmentId) {
      // Get employees by department
      // Allow all authenticated users to view department employees since they already passed the main permission check

      // Build options
      const options: Record<string, any> = {
        page,
        limit,
        sort: { lastName: 1, firstName: 1 }
      };

      if (status) {
        options.status = status.split(',');
      }

      // Get employees
      const result = await employeeService.getByDepartment(departmentId, options);

      return NextResponse.json(result);
    } else if (search) {
      // Search employees
      // Build options
      const options: Record<string, any> = {
        page,
        limit,
        sort: { lastName: 1, firstName: 1 }
      };

      if (status) {
        options.status = status.split(',');
      }

      if (departmentId) {
        options.departmentId = departmentId;
      }

      // Search employees
      const result = await employeeService.searchEmployees(search, options);

      return NextResponse.json(result);
    } else {
      // Get all employees with pagination
      // Build filter
      const filter: Record<string, any> = {};

      if (status) {
        filter.employmentStatus = status;
      }

      // Get employees with pagination
      const result = await employeeService.paginate(
        filter,
        page,
        limit,
        { lastName: 1, firstName: 1 },
        ['departmentId', 'managerId', 'addedBy']
      );

      return NextResponse.json(result);
    }
  } catch (error: unknown) {
    logger.error('Error in employee GET handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while processing employee request' }, { status: 500 });
  }
}

/**
 * POST handler for employees
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only certain roles can add employees
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.RECRUITER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await request.json();

    logger.info('Employee POST request', LogCategory.API, { body });

    // Validate required fields
    if (!body.firstName || !body.lastName || !body.email) {
      return NextResponse.json({
        error: 'First name, last name, and email are required'
      }, { status: 400 });
    }

    // Set access control fields
    body.createdBy = user.id;
    body.addedBy = user.id;
    body.lastModifiedBy = user.id;
    body.accessibleBy = [user.id];

    // Create employee
    const employee = await employeeService.createEmployee(body);

    return NextResponse.json(employee, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in employee POST handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while creating employee' }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { EmployeeService } from '@/lib/backend/services/hr/EmployeeService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';

// Initialize services
const employeeService = new EmployeeService();

/**
 * GET handler for a specific employee
 * @param request - Next.js request
 * @param params - Route parameters
 * @returns Next.js response
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    logger.info('Employee GET by ID request', LogCategory.API, { id });

    // Check permissions - Allow all authenticated users to view employee details
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD,
      UserRole.TEAM_LEADER,
      UserRole.RECRUITER,
      UserRole.EMPLOYEE,
      UserRole.CONTRACTOR,
      UserRole.INTERN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.PAYROLL_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get employee
    const employee = await employeeService.getEmployeeDetails(id);

    if (!employee) {
      return NextResponse.json({ error: 'Employee not found' }, { status: 404 });
    }

    return NextResponse.json(employee);
  } catch (error: unknown) {
    logger.error('Error in employee GET by ID handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

/**
 * PATCH handler for updating an employee
 * @param request - Next.js request
 * @param params - Route parameters
 * @returns Next.js response
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await request.json();

    logger.info('Employee PATCH request', LogCategory.API, { id, body });

    // Check permissions
    const hasUpdatePermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);

    if (!hasUpdatePermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Update employee with enhanced department handling
    const updatedEmployee = await employeeService.updateWithDepartmentHandling(id, body);

    if (!updatedEmployee) {
      return NextResponse.json({ error: 'Employee not found' }, { status: 404 });
    }

    return NextResponse.json(updatedEmployee);
  } catch (error: unknown) {
    logger.error('Error in employee PATCH handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

/**
 * DELETE handler for removing an employee
 * @param request - Next.js request
 * @param params - Route parameters
 * @returns Next.js response
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    logger.info('Employee DELETE request', LogCategory.API, { id });

    // Check permissions
    const hasDeletePermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_MANAGER
    ]);

    if (!hasDeletePermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Delete employee
    const result = await employeeService.deleteById(id);

    if (!result) {
      return NextResponse.json({ error: 'Employee not found' }, { status: 404 });
    }

    return NextResponse.json({ success: true, message: 'Employee deleted successfully' });
  } catch (error: unknown) {
    logger.error('Error in employee DELETE handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

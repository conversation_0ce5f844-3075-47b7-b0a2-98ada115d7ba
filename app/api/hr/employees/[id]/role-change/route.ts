import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { employeeLifecycleService } from '@/services/hr/EmployeeLifecycleService';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

interface RoleChangePayload {
  newRole: string;
  effectiveDate?: string;
  reason?: string;
}
/**
 * POST /api/hr/employees/[id]/role-change
 * Handle employee role change
 */
export async function POST(
  req: NextRequest, 
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions - only HR can change employee roles
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions to change employee roles' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get the employee ID from params
    const { id } = await params;
    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid employee ID format' },
        { status: 400 }
      );
    }
    // Get request body
    const body: RoleChangePayload = await req.json();
    // Validate required fields
    if (!body.newRole) {
      return NextResponse.json(
        { error: 'Missing required field: newRole' },
        { status: 400 }
      );
    }
    // Validate new role
    if (typeof body.newRole !== 'string' || body.newRole.trim().length === 0) {
      return NextResponse.json(
        { error: 'Invalid new role format' },
        { status: 400 }
      );
    }
    // Handle role change
    const result = await employeeLifecycleService.handleRoleChange(
      id, 
      body.newRole.trim(), 
      user.id
    );
    return NextResponse.json({
      success: true,
      message: 'Employee role change completed successfully',
      data: result
    });
  } catch (error: unknown) {
    logger.error('Error changing employee role', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while changing employee role' 
      },
      { status: 500 }
    );
  }
}
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { employeeLifecycleService } from '@/services/hr/EmployeeLifecycleService';
import mongoose from 'mongoose';

interface DepartmentTransferPayload {
  newDepartmentId: string;
  effectiveDate?: string;
  reason?: string;
}

/**
 * POST /api/hr/employees/[id]/transfer
 * Handle employee department transfer
 */
export async function POST(
  req: NextRequest, 
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only HR can transfer employees
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions to transfer employees' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get the employee ID from params
    const { id } = await params;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid employee ID format' },
        { status: 400 }
      );
    }

    // Get request body
    const body: DepartmentTransferPayload = await req.json();

    // Validate required fields
    if (!body.newDepartmentId) {
      return NextResponse.json(
        { error: 'Missing required field: newDepartmentId' },
        { status: 400 }
      );
    }

    // Validate new department ID format
    if (!mongoose.Types.ObjectId.isValid(body.newDepartmentId)) {
      return NextResponse.json(
        { error: 'Invalid new department ID format' },
        { status: 400 }
      );
    }

    // Handle department transfer
    const result = await employeeLifecycleService.handleDepartmentTransfer(
      id, 
      body.newDepartmentId, 
      user.id
    );

    return NextResponse.json({
      success: true,
      message: 'Employee department transfer completed successfully',
      data: result
    });

  } catch (error: unknown) {
    logger.error('Error transferring employee', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while transferring employee' 
      },
      { status: 500 }
    );
  }
}

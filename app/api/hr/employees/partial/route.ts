import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from '@/lib/backend/auth/session';
import { connectToDatabase } from '@/lib/backend/database';
import { v4 as uuidv4 } from 'uuid';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

// app/api/hr/employees/partial/route.ts
// In-memory storage for partial employee data
// In a production environment, this should be stored in a database
const partialEmployeeData: Record<string, {
  data: Record<string, any>;
  employeeId?: string;
  tempId: string;
  timestamp: number;
  userId: string;
}> = {};
// Cleanup old data periodically (24 hours)
const EXPIRATION_TIME = 24 * 60 * 60 * 1000;
// Cleanup function
function cleanupExpiredData() {
  const now = Date.now();
  Object.keys(partialEmployeeData).forEach(key => {
    if (now - partialEmployeeData[key].timestamp > EXPIRATION_TIME) {
      delete partialEmployeeData[key];
    }
  });
}
// Run cleanup every hour
setInterval(cleanupExpiredData, 60 * 60 * 1000);
/**
 * GET /api/hr/employees/partial
 * Get partial employee data
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const user = session.user;
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const employeeId = searchParams.get('employeeId');
    const tempId = searchParams.get('tempId');
    // Validate parameters
    if (!employeeId && !tempId) {
      return NextResponse.json(
        { error: 'Either employeeId or tempId is required' },
        { status: 400 }
      );
    }
    // Find data by employeeId or tempId
    let data;
    if (employeeId) {
      // Find by employeeId
      const entry = Object.values(partialEmployeeData).find(
        entry => entry.employeeId === employeeId && entry.userId === user.id
      );
      data = entry;
    } else if (tempId) {
      // Find by tempId
      data = partialEmployeeData[tempId];
      // Check if data belongs to the current user
      if (data && data.userId !== user.id) {
        return NextResponse.json(
          { error: 'Unauthorized access to partial data' },
          { status: 403 }
        );
      }
    }
    // If no data found, return 404
    if (!data) {
      return NextResponse.json(
        { error: 'Partial employee data not found' },
        { status: 404 }
      );
    }
    // Return the data
    return NextResponse.json({
      status: 'success',
      data: data.data
    });
  } catch (error: unknown) {
    logger.error('Error getting partial employee data', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to get partial employee data' },
      { status: 500 }
    );
  }
}
/**
 * POST /api/hr/employees/partial
 * Save partial employee data
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const user = session.user;
    // Parse request body
    const body = await req.json();
    const { data, step, employeeId, tempId } = body;
    // Validate data
    if (!data || step === undefined) {
      return NextResponse.json(
        { error: 'Data and step are required' },
        { status: 400 }
      );
    }
    // Generate a new tempId if not provided
    const newTempId = tempId || uuidv4();
    // If tempId is provided, check if it exists
    if (tempId && !partialEmployeeData[tempId]) {
      // If not found, create a new entry
      partialEmployeeData[newTempId] = {
        data: {},
        employeeId,
        tempId: newTempId,
        timestamp: Date.now(),
        userId: user.id
      };
    } else if (!tempId) {
      // Create a new entry
      partialEmployeeData[newTempId] = {
        data: {},
        employeeId,
        tempId: newTempId,
        timestamp: Date.now(),
        userId: user.id
      };
    }
    // Update the data for the specific step
    partialEmployeeData[newTempId].data[`step${step}`] = data;
    // Update timestamp
    partialEmployeeData[newTempId].timestamp = Date.now();
    // If employeeId is provided, update it
    if (employeeId) {
      partialEmployeeData[newTempId].employeeId = employeeId;
    }
    // Return success
    return NextResponse.json({
      status: 'success',
      message: 'Partial employee data saved successfully',
      tempId: newTempId,
      step
    });
  } catch (error: unknown) {
    logger.error('Error saving partial employee data', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to save partial employee data' },
      { status: 500 }
    );
  }
}
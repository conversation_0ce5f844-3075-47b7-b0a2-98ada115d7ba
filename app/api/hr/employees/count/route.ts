import { NextRequest, NextResponse } from 'next/server';
import { EmployeeService } from '@/lib/backend/services/hr/EmployeeService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

// app/api/hr/employees/count/route.ts
// Initialize services
const employeeService = new EmployeeService();
/**
 * POST handler for counting employees based on filter criteria
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();
    // Get current user
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions - only certain roles can access employee count
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD
    ]);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    // Get request body
    const body = await request.json();
    const { filter = {} } = body;
    logger.info('Employee count request', LogCategory.API, { filter });
    // Build MongoDB filter
    const mongoFilter: Record<string, any> = {};
    // Add department filter
    if (filter.department) {
      // Check if it's a department name or ID
      if (mongoose.Types.ObjectId.isValid(filter.department)) {
        mongoFilter.departmentId = new mongoose.Types.ObjectId(filter.department);
      } else {
        // Department heads can only access their own department
        const isDepartmentHead = hasRequiredPermissions(user, [UserRole.DEPARTMENT_HEAD]);
        const isHR = hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.HR_DIRECTOR,
          UserRole.HR_MANAGER,
          UserRole.HR_SPECIALIST
        ]);
        if (isDepartmentHead && !isHR && user.department !== filter.department) {
          return NextResponse.json({ error: 'Forbidden: You can only access employees from your department' }, { status: 403 });
        }
        mongoFilter.departmentId = filter.department;
      }
    }
    // Add status filter
    if (filter.status) {
      mongoFilter.employmentStatus = Array.isArray(filter.status)
        ? { $in: filter.status }
        : filter.status;
    }
    // Add date range filter
    if (filter.dateRange) {
      mongoFilter.hireDate = {};
      if (filter.dateRange.from) {
        mongoFilter.hireDate.$gte = new Date(filter.dateRange.from);
      }
      if (filter.dateRange.to) {
        mongoFilter.hireDate.$lte = new Date(filter.dateRange.to);
      }
    }
    // Add search term filter
    if (filter.searchTerm) {
      mongoFilter.$or = [
        { firstName: { $regex: filter.searchTerm, $options: 'i' } },
        { lastName: { $regex: filter.searchTerm, $options: 'i' } },
        { email: { $regex: filter.searchTerm, $options: 'i' } },
        { employeeId: { $regex: filter.searchTerm, $options: 'i' } }
      ];
    }
    // Get count of employees
    const count = await employeeService.count(mongoFilter);
    // Return count
    return NextResponse.json({ count });
  } catch (error: unknown) {
    logger.error('Error in employee count handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while counting employees' }, { status: 500 });
  }
}
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { connectToDatabase } from '@/lib/backend/database';
import DeletedItem from '@/models/audit/DeletedItems';
import User from '@/models/User';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

// app/api/audit/deleted-items/[id]/route.ts
/**
 * GET /api/audit/deleted-items/[id]
 * Get detailed information for a specific deleted item
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions - only auditors and super admins can access
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.AUDITOR
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to access audit data' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Ensure models are registered
    User;
    // Get the item ID from params
    const { id } = await params;
    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid item ID format' },
        { status: 400 }
      );
    }
    // Fetch the deleted item with populated references
    const rawItem = await DeletedItem.findById(id)
      .populate('deletedBy', 'firstName lastName email role')
      .populate('reviewedBy', 'firstName lastName email role')
      .lean();
    if (!rawItem) {
      return NextResponse.json(
        { error: 'Deleted item not found' },
        { status: 404 }
      );
    }
    // Create audit trail entries
    const auditTrail = [
      {
        action: 'Item Deleted',
        performedBy: rawItem.deletedByUser?.name || 'Unknown',
        performedAt: rawItem.deletedAt,
        details: `Item deleted via ${rawItem.deletionType} operation. Reason: ${rawItem.deletionReason}`
      }
    ];
    // Add review actions to audit trail
    if (rawItem.reviewedBy && rawItem.reviewedAt) {
      auditTrail.push({
        action: 'Review Completed',
        performedBy: rawItem.reviewedBy.firstName + ' ' + rawItem.reviewedBy.lastName,
        performedAt: rawItem.reviewedAt,
        details: `Item review status changed to: ${rawItem.reviewStatus}${rawItem.auditNotes ? '. Notes: ' + rawItem.auditNotes : ''}`
      });
    }
    // Generate compliance checks
    const complianceChecks = {
      retentionPolicyCompliant: true, // Always true for items in system
      approvalWorkflowCompleted: rawItem.reviewStatus !== 'pending',
      documentationComplete: Boolean(rawItem.deletionReason && rawItem.deletionReason.length >= 10),
      governmentNotificationSent: rawItem.reviewStatus === 'approved'
    };
    // Find related items (items deleted in the same bulk operation)
    const relatedItems = [];
    if (rawItem.deletionType === 'bulk') {
      const relatedDeletedItems = await DeletedItem.find({
        deletedAt: rawItem.deletedAt,
        deletedBy: rawItem.deletedBy,
        deletionType: 'bulk',
        _id: { $ne: rawItem._id }
      })
      .limit(10)
      .select('originalId originalModel originalData')
      .lean();
      relatedItems.push(...relatedDeletedItems.map(item => ({
        id: item._id.toString(),
        type: item.originalModel,
        reference: item.originalData?.reference || item.originalData?.name || item.originalId,
        relationship: 'Bulk Operation'
      })));
    }
    // Format the detailed item for frontend consumption
    const detailedItem = {
      id: rawItem._id.toString(),
      originalId: rawItem.originalId,
      originalModel: rawItem.originalModel,
      originalData: rawItem.originalData,
      deletedBy: rawItem.deletedByUser,
      deletionDate: rawItem.deletedAt,
      deletionReason: rawItem.deletionReason,
      deletionType: rawItem.deletionType,
      recoveryDeadline: rawItem.recoveryDeadline,
      reviewStatus: rawItem.reviewStatus,
      complianceFlags: rawItem.complianceFlags || [],
      auditRecordId: rawItem._id.toString(),
      canBeRecovered: rawItem.canBeRecovered,
      fiscalYear: rawItem.fiscalYear,
      auditNotes: rawItem.auditNotes,
      reviewedBy: rawItem.reviewedBy ? {
        name: rawItem.reviewedBy.firstName + ' ' + rawItem.reviewedBy.lastName,
        email: rawItem.reviewedBy.email,
        role: rawItem.reviewedBy.role
      } : undefined,
      reviewedAt: rawItem.reviewedAt,
      // Additional detailed information
      auditTrail,
      relatedItems,
      complianceChecks,
      // Deletion context (if available)
      deletionContext: {
        userAgent: rawItem.metadata?.userAgent || 'Unknown',
        ipAddress: rawItem.metadata?.ipAddress || 'Unknown',
        sessionId: rawItem.metadata?.sessionId || 'Unknown',
        requestId: rawItem.metadata?.requestId || 'Unknown'
      }
    };
    // Log the access for audit purposes
    console.log(`Audit item ${id} accessed by user ${user.id} (${user.email})`);
    return NextResponse.json({
      item: detailedItem,
      accessLog: {
        accessedBy: user.email,
        accessedAt: new Date().toISOString(),
        accessReason: 'Audit Review'
      }
    });
  } catch (error) {
    console.error('Error fetching deleted item details:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching item details' },
      { status: 500 }
    );
  }
}
/**
 * PUT /api/audit/deleted-items/[id]
 * Update review status or audit notes for a specific deleted item
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.AUDITOR
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get the item ID from params
    const { id } = await params;
    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid item ID format' },
        { status: 400 }
      );
    }
    // Get request body
    const body = await req.json();
    const { reviewStatus, auditNotes } = body;
    // Validate required fields
    if (!reviewStatus) {
      return NextResponse.json(
        { error: 'reviewStatus is required' },
        { status: 400 }
      );
    }
    // Validate review status values
    const validStatuses = ['pending', 'approved', 'flagged', 'investigated'];
    if (!validStatuses.includes(reviewStatus)) {
      return NextResponse.json(
        { error: `Invalid review status. Must be one of: ${validStatuses.join(', ')}` },
        { status: 400 }
      );
    }
    // Update the deleted item
    const updateResult = await DeletedItem.findByIdAndUpdate(
      id,
      {
        $set: {
          reviewStatus,
          reviewedBy: user.id,
          reviewedAt: new Date(),
          ...(auditNotes && { auditNotes })
        }
      },
      { new: true }
    );
    if (!updateResult) {
      return NextResponse.json(
        { error: 'Deleted item not found' },
        { status: 404 }
      );
    }
    // Log the update for audit purposes
    console.log(`Audit item ${id} updated by user ${user.id} (${user.email}). New status: ${reviewStatus}`);
    return NextResponse.json({
      success: true,
      message: 'Item review status updated successfully',
      item: {
        id: updateResult._id.toString(),
        reviewStatus: updateResult.reviewStatus,
        reviewedBy: {
          id: user.id,
          name: user.firstName + ' ' + user.lastName,
          email: user.email,
          role: user.role
        },
        reviewedAt: updateResult.reviewedAt,
        auditNotes: updateResult.auditNotes
      }
    });
  } catch (error) {
    console.error('Error updating deleted item:', error);
    return NextResponse.json(
      { error: 'An error occurred while updating the item' },
      { status: 500 }
    );
  }
}
// app/api/audit/delete/bulk/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { AuditDeletionService } from '@/lib/services/audit/audit-deletion-service';
import { getModelByName, getModelConfig } from '@/lib/services/audit/model-registry';

export const runtime = 'nodejs';

/**
 * POST /api/audit/delete/bulk
 * Universal bulk audit deletion endpoint
 * 
 * Body: {
 *   modelName: string,
 *   ids: string[],
 *   deletionReason: string,
 *   additionalContext?: object
 * }
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { modelName, ids, deletionReason, additionalContext = {} } = body;

    // Validate required fields
    if (!modelName || !ids || !Array.isArray(ids) || ids.length === 0 || !deletionReason) {
      return NextResponse.json(
        { error: 'modelName, ids (non-empty array), and deletionReason are required' },
        { status: 400 }
      );
    }

    // Get model and configuration
    const Model = getModelByName(modelName);
    if (!Model) {
      return NextResponse.json(
        { error: `Invalid model name: ${modelName}` },
        { status: 400 }
      );
    }

    const modelConfig = getModelConfig(modelName);
    
    // Check permissions for this model type
    const hasPermission = hasRequiredPermissions(user, modelConfig.allowedRoles);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions for this operation' },
        { status: 403 }
      );
    }

    // Validate bulk operation limits
    if (ids.length > modelConfig.maxBulkDelete) {
      return NextResponse.json(
        { error: `Cannot delete more than ${modelConfig.maxBulkDelete} ${modelConfig.displayName}s at once` },
        { status: 400 }
      );
    }

    // Get client context for audit trail
    const clientIP = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
    const userAgent = req.headers.get('user-agent') || 'unknown';

    // Prepare audit context
    const auditContext = {
      deletedBy: user.id,
      deletionReason,
      deletionType: 'bulk' as const,
      userInfo: {
        id: user.id,
        name: `${user.firstName} ${user.lastName}`,
        email: user.email,
        role: user.role
      },
      context: {
        ipAddress: clientIP,
        userAgent: userAgent,
        sessionId: req.headers.get('x-session-id') || undefined,
        bulkOperationSize: ids.length,
        ...additionalContext
      }
    };

    // Define permission check
    const permissionCheck = {
      isAdmin: hasRequiredPermissions(user, [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN]),
      userId: user.id,
      checkOwnership: modelConfig.checkOwnership,
      ownershipField: modelConfig.ownershipField
    };

    // Perform audit deletion using the service
    const result = await AuditDeletionService.performAuditDeletion(
      Model,
      ids,
      auditContext,
      {
        validationRules: modelConfig.validationRules,
        permissionCheck,
        populateFields: modelConfig.populateFields,
        beforeDelete: modelConfig.beforeDelete,
        afterDelete: modelConfig.afterDelete
      }
    );

    return NextResponse.json({
      success: result.success,
      message: `Successfully moved ${result.deletedCount} ${modelConfig.displayName}${result.deletedCount > 1 ? 's' : ''} to audit trail`,
      deletedCount: result.deletedCount,
      auditRecordsCreated: result.auditRecordsCreated,
      requestedCount: ids.length,
      details: {
        deletedIds: result.deletedIds,
        auditRecordIds: result.auditRecordIds,
        deletionReason: result.details.deletionReason,
        auditCompliance: result.details.auditCompliance,
        modelName,
        modelDisplayName: modelConfig.displayName,
        bulkOperationStats: {
          requested: ids.length,
          processed: result.deletedCount,
          auditRecordsCreated: result.auditRecordsCreated,
          errors: result.errors?.length || 0
        }
      },
      errors: result.errors
    });

  } catch (error: unknown) {
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal Server Error',
        details: 'Bulk audit deletion failed'
      },
      { status: 500 }
    );
  }
}

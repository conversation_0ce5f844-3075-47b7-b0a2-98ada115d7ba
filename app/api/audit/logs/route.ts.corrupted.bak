import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { auditService } from '@/lib/services/payroll/audit-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/audit/logs - Get audit logs with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only Super Admin can view audit logs
    const hasPermission = hasRequiredPermissions(user, [UserRole.SUPER_ADMIN]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view audit logs' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);

    const filters: any = {
      userId: searchParams.get('userId') || undefined,
      action: searchParams.get('action') || undefined,
      module: searchParams.get('module') || undefined,
      entityType: searchParams.get('entityType') || undefined,
      entityId: searchParams.get('entityId') || undefined,
      batchId: searchParams.get('batchId') || undefined,
      severity: searchParams.get('severity') || undefined,
      isError: searchParams.get('isError') ? searchParams.get('isError') === 'true' : undefined,
      search: searchParams.get('search') || undefined,
      page: parseInt(searchParams.get('page') || '1'),
      limit: Math.min(parseInt(searchParams.get('limit') || '50'), 100), // Max 100 per page
    };

    // Parse date filters
    if (searchParams.get('startDate')) {
      filters.startDate = new Date(searchParams.get('startDate')!);
    }
    if (searchParams.get('endDate')) {
      filters.endDate = new Date(searchParams.get('endDate')!);
    }

    // Get audit logs
    const result = await auditService.getAuditLogs(filters);

    logger.info('Audit logs retrieved', LogCategory.AUDIT, {
      userId: user._id,
      filters,
      resultCount: result.logs.length,
      totalCount: result.totalCount,
    });

    return NextResponse.json({
      success: true,
      data: result,
    });

  } catch (error) {
    logger.error('Error retrieving audit logs', LogCategory.AUDIT, error);
    return NextResponse.json(
      { error: 'Failed to retrieve audit logs' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/audit/logs - Create a manual audit log entry
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only Super Admin can create manual audit logs
    const hasPermission = hasRequiredPermissions(user, [UserRole.SUPER_ADMIN]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Insufficient permissions to create audit logs' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const {
      action,
      module,
      entityType,
      entityId,
      oldValues,
      newValues,
      description,
      metadata,
      severity,
    } = body;

    // Validate required fields
    if (!action || !module || !entityType || !entityId) {
      return NextResponse.json(
        { error: 'Missing required fields: action, module, entityType, entityId' },
        { status: 400 }
      );
    }

    // Extract request context
    const requestContext = auditService.extractRequestContext(request);

    // Create audit log
    const auditLog = await auditService.createAuditLog({
      userId: user._id?.toString() || user.id,
      action,
      module,
      entityType,
      entityId,
      oldValues,
      newValues,
      description: description || `Manual audit log entry: ${action} on ${entityType}`,
      metadata: {
        ...metadata,
        manualEntry: true,
        createdBy: user.email,
      },
      severity: severity || 'medium',
      ...requestContext,
    });

    logger.info('Manual audit log created', LogCategory.AUDIT, {
      auditLogId: auditLog._id,
      action,
      module,
      entityType,
      entityId,
      userId: user._id,
    });

    return NextResponse.json({
      success: true,
      data: auditLog,
    });

  } catch (error) {
    logger.error('Error creating manual audit log', LogCategory.AUDIT, error);
    return NextResponse.json(
      { error: 'Failed to create audit log' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/audit/logs - Archive old audit logs
 */
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only Super Admin can archive logs
    const hasPermission = hasRequiredPermissions(user, [UserRole.SUPER_ADMIN]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Insufficient permissions to archive audit logs' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const olderThanDays = parseInt(searchParams.get('olderThanDays') || '365');

    // Archive old logs
    const archivedCount = await auditService.archiveOldLogs(olderThanDays);

    // Create audit log for this action
    await auditService.createAuditLog({
      userId: user._id?.toString() || user.id,
      action: 'archive',
      module: 'system',
      entityType: 'AuditLog',
      entityId: 'bulk',
      description: `Archived ${archivedCount} audit logs older than ${olderThanDays} days`,
      metadata: {
        archivedCount,
        olderThanDays,
      },
      severity: 'high',
      ...auditService.extractRequestContext(request),
    });

    logger.info('Audit logs archived', LogCategory.AUDIT, {
      archivedCount,
      olderThanDays,
      userId: user._id || user.id,
    });

    return NextResponse.json({
      success: true,
      data: {
        archivedCount,
        message: `Successfully archived ${archivedCount} audit logs`,
      },
    });

  } catch (error) {
    logger.error('Error archiving audit logs', LogCategory.AUDIT, error);
    return NextResponse.json(
      { error: 'Failed to archive audit logs' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { connectToDatabase } from '@/lib/backend/database';
import DeletedItem from '@/models/audit/DeletedItems';
import AuditLog from '@/models/payroll/AuditLog';

export const runtime = 'nodejs';

// app/api/audit/stats/route.ts
/**
 * GET /api/audit/stats
 * Get audit dashboard statistics
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions - only auditors and super admins can access
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.AUDITOR
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to access audit data' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get query parameters for date filtering
    const searchParams = req.nextUrl.searchParams;
    const fiscalYear = searchParams.get('fiscalYear');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    // Build date filter
    const dateFilter: Record<string, any> = {};
    if (startDate || endDate) {
      if (startDate) dateFilter.$gte = new Date(startDate);
      if (endDate) dateFilter.$lte = new Date(endDate);
    } else if (fiscalYear) {
      // Default to current fiscal year if no dates provided
      const [startYear, endYear] = fiscalYear.split('-').map(Number);
      dateFilter.$gte = new Date(`${startYear}-07-01`); // July 1st start
      dateFilter.$lte = new Date(`${endYear}-06-30`); // June 30th end
    }
    // Get total deleted items
    const totalDeletedItems = await DeletedItem.countDocuments(
      dateFilter.hasOwnProperty('$gte') ? { deletedAt: dateFilter } : {}
    );
    // Get items pending recovery (within recovery deadline)
    const now = new Date();
    const pendingRecovery = await DeletedItem.countDocuments({
      canBeRecovered: true,
      recoveryDeadline: { $gte: now },
      ...(dateFilter.hasOwnProperty('$gte') ? { deletedAt: dateFilter } : {})
    });
    // Get items nearing recovery deadline (next 30 days)
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    const nearingDeadline = await DeletedItem.countDocuments({
      canBeRecovered: true,
      recoveryDeadline: { $lte: thirtyDaysFromNow, $gte: now },
      ...(dateFilter.hasOwnProperty('$gte') ? { deletedAt: dateFilter } : {})
    });
    // Get retention items (items that will be permanently deleted soon)
    const retentionItems = await DeletedItem.countDocuments({
      canBeRecovered: false,
      ...(dateFilter.hasOwnProperty('$gte') ? { deletedAt: dateFilter } : {})
    });
    // Get critical alerts (flagged items + items with compliance issues)
    const criticalAlerts = await DeletedItem.countDocuments({
      $or: [
        { reviewStatus: 'flagged' },
        { complianceFlags: { $exists: true, $ne: [] } }
      ],
      ...(dateFilter.hasOwnProperty('$gte') ? { deletedAt: dateFilter } : {})
    });
    // Calculate compliance score based on review status
    const reviewStatusStats = await DeletedItem.aggregate([
      {
        $match: dateFilter.hasOwnProperty('$gte') ? { deletedAt: dateFilter } : {}
      },
      {
        $group: {
          _id: '$reviewStatus',
          count: { $sum: 1 }
        }
      }
    ]);
    const totalReviewed = reviewStatusStats.reduce((sum, stat) => sum + stat.count, 0);
    const approvedCount = reviewStatusStats.find(stat => stat._id === 'approved')?.count || 0;
    const complianceScore = totalReviewed > 0 ? (approvedCount / totalReviewed) * 100 : 100;
    // Get recent deletions (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const recentDeletions = await DeletedItem.find({
      deletedAt: { $gte: sevenDaysAgo }
    })
    .sort({ deletedAt: -1 })
    .limit(10)
    .populate('deletedBy', 'firstName lastName email role')
    .lean();
    // Format recent deletions
    const formattedRecentDeletions = recentDeletions.map(item => ({
      id: item._id,
      itemType: item.originalModel,
      deletedBy: item.deletedByUser?.name || 'Unknown',
      deletionDate: item.deletedAt,
      reason: item.deletionReason,
      status: getComplianceStatus(item),
      originalData: {
        reference: item.originalData?.reference || item.originalData?.name || 'N/A',
        amount: item.originalData?.amount || null
      }
    }));
    // Get deletion trends (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const deletionTrends = await DeletedItem.aggregate([
      {
        $match: {
          deletedAt: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: "%Y-%m-%d", date: "$deletedAt" } },
            model: "$originalModel"
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { "_id.date": 1 }
      }
    ]);
    // Get model distribution
    const modelDistribution = await DeletedItem.aggregate([
      {
        $match: dateFilter.hasOwnProperty('$gte') ? { deletedAt: dateFilter } : {}
      },
      {
        $group: {
          _id: '$originalModel',
          count: { $sum: 1 },
          recentCount: {
            $sum: {
              $cond: [
                { $gte: ['$deletedAt', sevenDaysAgo] },
                1,
                0
              ]
            }
          }
        }
      }
    ]);
    // Get last audit date (most recent deletion)
    const lastAuditResult = await DeletedItem.findOne({}, { deletedAt: 1 })
      .sort({ deletedAt: -1 })
      .lean();
    const lastAuditDate = lastAuditResult?.deletedAt || new Date();
    return NextResponse.json({
      stats: {
        totalDeletedItems,
        pendingRecovery,
        complianceScore: Math.round(complianceScore * 10) / 10,
        lastAuditDate,
        retentionItems,
        criticalAlerts,
        nearingDeadline
      },
      recentDeletions: formattedRecentDeletions,
      trends: {
        deletionTrends,
        modelDistribution
      },
      summary: {
        byReviewStatus: reviewStatusStats.reduce((acc, stat) => {
          acc[stat._id] = stat.count;
          return acc;
        }, {} as Record<string, number>),
        totalReviewed,
        approvedCount
      }
    });
  } catch (error) {
    console.error('Error fetching audit stats:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching audit statistics' },
      { status: 500 }
    );
  }
}
/**
 * Helper function to determine compliance status
 */
function getComplianceStatus(item: any): string {
  if (item.reviewStatus === 'flagged') return 'Under Review';
  if (item.reviewStatus === 'approved') return 'Compliant';
  if (item.complianceFlags && item.complianceFlags.length > 0) return 'Flagged';
  if (item.reviewStatus === 'pending') return 'Pending Review';
  return 'Compliant';
}
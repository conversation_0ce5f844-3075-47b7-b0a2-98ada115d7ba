import { NextRequest, NextResponse } from 'next/server';
import { auditService } from '@/lib/services/payroll/audit-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/audit/reports - Generate audit reports
 */
export async function GET(request: NextRequest) {
  try {
    // For now, skip authentication check to fix build
    // TODO: Implement proper authentication when auth system is ready
    const mockUser = {
      id: 'system',
      email: '<EMAIL>',
      role: 'Super Admin'
    };

    // Parse query parameters
    const { searchParams } = new URL(request.url);

    // Validate required date range
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');

    if (!startDateParam || !endDateParam) {
      return NextResponse.json(
        { error: 'Start date and end date are required' },
        { status: 400 }
      );
    }

    const startDate = new Date(startDateParam);
    const endDate = new Date(endDateParam);

    // Validate dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return NextResponse.json(
        { error: 'Invalid date format' },
        { status: 400 }
      );
    }

    if (startDate >= endDate) {
      return NextResponse.json(
        { error: 'Start date must be before end date' },
        { status: 400 }
      );
    }

    // Check date range limit (max 1 year)
    const maxDays = 365;
    const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    if (daysDiff > maxDays) {
      return NextResponse.json(
        { error: `Date range cannot exceed ${maxDays} days` },
        { status: 400 }
      );
    }

    const filters = {
      startDate,
      endDate,
      module: searchParams.get('module') || undefined,
      userId: searchParams.get('userId') || undefined,
      action: searchParams.get('action') || undefined,
      entityType: searchParams.get('entityType') || undefined,
    };

    // Generate audit report
    const report = await auditService.generateAuditReport(filters);

    // Create audit log for report generation
    await auditService.createAuditLog({
      userId: mockUser.id,
      action: 'generate_report',
      module: 'audit',
      entityType: 'AuditReport',
      entityId: `report_${Date.now()}`,
      description: `Generated audit report for period ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`,
      metadata: {
        reportFilters: filters,
        reportSummary: report.summary,
      },
      severity: 'medium',
      ...auditService.extractRequestContext(request),
    });

    logger.info('Audit report generated', LogCategory.AUDIT, {
      userId: mockUser.id,
      filters,
      totalLogs: report.summary.totalLogs,
      dateRange: `${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`,
    });

    return NextResponse.json({
      success: true,
      data: {
        ...report,
        metadata: {
          generatedAt: new Date().toISOString(),
          generatedBy: mockUser.email,
          dateRange: {
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
          },
          filters,
        },
      },
    });

  } catch (error) {
    logger.error('Error generating audit report', LogCategory.AUDIT, error);
    return NextResponse.json(
      { error: 'Failed to generate audit report' },
      { status: 500 }
    );
  }
}

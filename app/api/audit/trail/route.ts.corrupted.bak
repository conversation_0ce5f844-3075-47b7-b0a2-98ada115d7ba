// app/api/audit/trail/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { connectToDatabase } from '@/lib/backend/database';
import AuditLog from '@/models/payroll/AuditLog';
import User from '@/models/User';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

/**
 * GET /api/audit/trail
 * Get audit trail entries with filtering and pagination
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only auditors and super admins can access
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.AUDITOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to access audit trail' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Ensure models are registered
    User;

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const action = searchParams.get('action') || '';
    const entityType = searchParams.get('entityType') || '';
    const severity = searchParams.get('severity') || '';
    const module = searchParams.get('module') || '';
    const userId = searchParams.get('userId') || '';
    const days = parseInt(searchParams.get('days') || '30');

    // Build filter object
    const filter: any = {};

    // Date range filter
    if (days && days > 0) {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      filter.timestamp = { $gte: startDate };
    }

    // Search filter
    if (search) {
      filter.$or = [
        { description: { $regex: search, $options: 'i' } },
        { action: { $regex: search, $options: 'i' } },
        { entityType: { $regex: search, $options: 'i' } },
        { entityId: { $regex: search, $options: 'i' } }
      ];
    }

    // Action filter
    if (action && action !== 'all') {
      filter.action = { $regex: action, $options: 'i' };
    }

    // Entity type filter
    if (entityType && entityType !== 'all') {
      filter.entityType = entityType;
    }

    // Severity filter
    if (severity && severity !== 'all') {
      filter.severity = severity;
    }

    // Module filter
    if (module && module !== 'all') {
      filter.module = module;
    }

    // User filter
    if (userId && userId !== 'all') {
      filter.userId = new mongoose.Types.ObjectId(userId);
    }

    // Only show non-archived entries
    filter.isArchived = false;

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count
    const totalCount = await AuditLog.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / limit);

    // Fetch audit entries
    const rawAuditEntries = await AuditLog.find(filter)
      .sort({ timestamp: -1 }) // Most recent first
      .skip(skip)
      .limit(limit)
      .populate('userId', 'firstName lastName email role')
      .lean();

    // Format audit entries for frontend consumption
    const auditEntries = rawAuditEntries.map(entry => ({
      id: entry._id.toString(),
      timestamp: entry.timestamp,
      action: entry.action.toUpperCase(),
      entityType: entry.entityType,
      entityId: entry.entityId,
      userId: entry.userId._id.toString(),
      userName: `${entry.userId.firstName} ${entry.userId.lastName}`,
      userRole: entry.userId.role,
      userEmail: entry.userId.email,
      ipAddress: entry.ipAddress || 'Unknown',
      userAgent: entry.userAgent || 'Unknown',
      details: {
        description: entry.description,
        oldValues: entry.oldValues,
        newValues: entry.newValues,
        changes: entry.changes,
        metadata: entry.metadata,
        batchId: entry.batchId,
        batchSize: entry.batchSize,
        batchIndex: entry.batchIndex
      },
      severity: entry.severity.toUpperCase(),
      module: entry.module,
      sessionId: entry.sessionId || 'Unknown',
      isError: entry.isError,
      errorMessage: entry.errorMessage,
      complianceFlags: generateComplianceFlags(entry)
    }));

    // Get audit statistics
    const stats = await getAuditStatistics(filter);

    // Get filter options
    const filterOptions = await getFilterOptions();

    return NextResponse.json({
      auditEntries,
      stats,
      filterOptions,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching audit trail:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching audit trail' },
      { status: 500 }
    );
  }
}

/**
 * Generate compliance flags based on audit entry
 */
function generateComplianceFlags(entry: any): string[] {
  const flags = [];

  // High value transaction flag
  if (entry.newValues?.amount && entry.newValues.amount > 100000) {
    flags.push('High Value');
  }

  // Financial record flag
  if (['Income', 'Expenditure', 'Budget', 'PayrollRecord'].includes(entry.entityType)) {
    flags.push('Financial Record');
  }

  // HR record flag
  if (entry.entityType === 'Employee') {
    flags.push('HR Record', 'Personal Data');
  }

  // Security event flag
  if (entry.action.includes('LOGIN') || entry.action.includes('LOGOUT')) {
    flags.push('Security Event');
  }

  // Bulk operation flag
  if (entry.batchId) {
    flags.push('Bulk Operation');
  }

  // Error flag
  if (entry.isError) {
    flags.push('Error Event');
  }

  // High severity flag
  if (entry.severity === 'high' || entry.severity === 'critical') {
    flags.push('High Severity');
  }

  return flags;
}

/**
 * Get audit statistics
 */
async function getAuditStatistics(baseFilter: any) {
  const last24Hours = new Date();
  last24Hours.setHours(last24Hours.getHours() - 24);

  const [
    totalEntries,
    highSeverityCount,
    last24HoursCount,
    errorCount
  ] = await Promise.all([
    AuditLog.countDocuments(baseFilter),
    AuditLog.countDocuments({ ...baseFilter, severity: { $in: ['high', 'critical'] } }),
    AuditLog.countDocuments({ ...baseFilter, timestamp: { $gte: last24Hours } }),
    AuditLog.countDocuments({ ...baseFilter, isError: true })
  ]);

  const complianceScore = totalEntries > 0 ? ((totalEntries - errorCount) / totalEntries * 100) : 100;

  return {
    totalEntries,
    highSeverityCount,
    last24HoursCount,
    errorCount,
    complianceScore: Math.round(complianceScore * 10) / 10
  };
}

/**
 * Get filter options
 */
async function getFilterOptions() {
  const [
    actions,
    entityTypes,
    modules,
    users
  ] = await Promise.all([
    AuditLog.distinct('action'),
    AuditLog.distinct('entityType'),
    AuditLog.distinct('module'),
    AuditLog.find({ isArchived: false })
      .populate('userId', 'firstName lastName email role')
      .distinct('userId')
      .lean()
  ]);

  return {
    actions: actions.map(action => ({ value: action.toLowerCase(), label: action })),
    entityTypes: entityTypes.map(type => ({ value: type, label: type })),
    modules: modules.map(module => ({ value: module, label: module.charAt(0).toUpperCase() + module.slice(1) })),
    users: users.map((user: any) => ({
      value: user._id.toString(),
      label: `${user.firstName} ${user.lastName} (${user.role})`
    }))
  };
}

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database/connection';
import { TaskService } from '@/lib/backend/services/project/TaskService';
import { authMiddleware } from '@/lib/backend/middleware/authMiddleware';
import { errorHandler } from '@/lib/backend/utils/errorHandler';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/tasks - Get tasks with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    // Authenticate user
    const authResult = await authMiddleware(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const assignedTo = searchParams.get('assignedTo');
    const assignedBy = searchParams.get('assignedBy');
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');
    const category = searchParams.get('category');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Build filter object
    const filter: any = {};
    
    if (assignedTo) {
      filter.assignedTo = { $in: [assignedTo] };
    }
    
    if (assignedBy) {
      filter.assignedBy = assignedBy;
    }
    
    if (status) {
      filter.status = status;
    }
    
    if (priority) {
      filter.priority = priority;
    }
    
    if (category) {
      filter.category = category;
    }
    
    if (startDate || endDate) {
      filter.dueDate = {};
      if (startDate) filter.dueDate.$gte = new Date(startDate);
      if (endDate) filter.dueDate.$lte = new Date(endDate);
    }
    
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Get tasks
    const taskService = new TaskService();
    const result = await taskService.findWithPagination(
      filter,
      {
        page,
        limit,
        populate: [
          {
            path: 'assignedTo',
            select: 'firstName lastName email avatar'
          },
          {
            path: 'assignedBy',
            select: 'firstName lastName email avatar'
          },
          {
            path: 'projectId',
            select: 'name description'
          }
        ],
        sort: { createdAt: -1 }
      }
    );

    // Transform data for frontend
    const transformedTasks = result.data.map((task: any) => ({
      id: task._id,
      title: task.title,
      description: task.description,
      status: task.status,
      priority: task.priority,
      category: task.category,
      dueDate: task.dueDate,
      estimatedHours: task.estimatedHours,
      actualHours: task.actualHours,
      completedDate: task.completedDate,
      qualityScore: task.qualityScore,
      assignedTo: task.assignedTo?.map((user: any) => user._id) || [],
      assignedBy: {
        id: task.assignedBy?._id,
        name: task.assignedBy ? `${task.assignedBy.firstName} ${task.assignedBy.lastName}` : 'Unknown',
        avatar: task.assignedBy?.avatar || '/placeholder-user.jpg'
      },
      project: task.projectId ? task.projectId.name : undefined,
      attachments: task.attachments || [],
      comments: task.comments || [],
      createdAt: task.createdAt,
      updatedAt: task.updatedAt
    }));

    logger.info('Tasks retrieved successfully', LogCategory.API, {
      userId: authResult.user.id,
      count: transformedTasks.length,
      total: result.total,
      filters: { assignedTo, assignedBy, status, priority, category, search }
    });

    return NextResponse.json({
      success: true,
      message: 'Tasks retrieved successfully',
      data: {
        tasks: transformedTasks,
        total: result.total,
        page: result.page,
        totalPages: result.totalPages,
        hasMore: result.hasMore
      }
    });

  } catch (error) {
    logger.error('Error retrieving tasks', LogCategory.API, { error });
    return errorHandler.handleError(error);
  }
}

/**
 * POST /api/tasks - Create new task
 */
export async function POST(request: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    // Authenticate user
    const authResult = await authMiddleware(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      title,
      description,
      status = 'todo',
      priority = 'medium',
      category,
      dueDate,
      estimatedHours,
      assignedTo,
      projectId
    } = body;

    // Validate required fields
    if (!title || !description) {
      return NextResponse.json(
        { success: false, message: 'Title and description are required' },
        { status: 400 }
      );
    }

    // Create task
    const taskService = new TaskService();
    const taskData = {
      title,
      description,
      status,
      priority,
      category,
      dueDate: dueDate ? new Date(dueDate) : undefined,
      estimatedHours,
      assignedTo: assignedTo || [],
      assignedBy: authResult.user.id,
      projectId,
      createdBy: authResult.user.id
    };

    const newTask = await taskService.create(taskData);

    // Populate task data for response
    const populatedTask = await taskService.findById(newTask._id, {
      populate: [
        {
          path: 'assignedTo',
          select: 'firstName lastName email avatar'
        },
        {
          path: 'assignedBy',
          select: 'firstName lastName email avatar'
        },
        {
          path: 'projectId',
          select: 'name description'
        }
      ]
    });

    // Transform data for frontend
    const transformedTask = {
      id: populatedTask._id,
      title: populatedTask.title,
      description: populatedTask.description,
      status: populatedTask.status,
      priority: populatedTask.priority,
      category: populatedTask.category,
      dueDate: populatedTask.dueDate,
      estimatedHours: populatedTask.estimatedHours,
      actualHours: populatedTask.actualHours,
      completedDate: populatedTask.completedDate,
      qualityScore: populatedTask.qualityScore,
      assignedTo: populatedTask.assignedTo?.map((user: any) => user._id) || [],
      assignedBy: {
        id: populatedTask.assignedBy?._id,
        name: populatedTask.assignedBy ? `${populatedTask.assignedBy.firstName} ${populatedTask.assignedBy.lastName}` : 'Unknown',
        avatar: populatedTask.assignedBy?.avatar || '/placeholder-user.jpg'
      },
      project: populatedTask.projectId ? populatedTask.projectId.name : undefined,
      attachments: populatedTask.attachments || [],
      comments: populatedTask.comments || [],
      createdAt: populatedTask.createdAt,
      updatedAt: populatedTask.updatedAt
    };

    logger.info('Task created successfully', LogCategory.API, {
      userId: authResult.user.id,
      taskId: newTask._id,
      title
    });

    return NextResponse.json({
      success: true,
      message: 'Task created successfully',
      data: transformedTask
    }, { status: 201 });

  } catch (error) {
    logger.error('Error creating task', LogCategory.API, { error });
    return errorHandler.handleError(error);
  }
}

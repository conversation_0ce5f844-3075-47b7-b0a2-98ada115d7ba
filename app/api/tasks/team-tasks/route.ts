import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database/connection';
import { TaskService } from '@/lib/backend/services/project/TaskService';
import { EmployeeService } from '@/lib/backend/services/hr/EmployeeService';
import { authMiddleware } from '@/lib/backend/middleware/authMiddleware';
import { errorHandler } from '@/lib/backend/utils/errorHandler';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/tasks/team-tasks - Get tasks assigned to team members
 */
export async function GET(request: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    // Authenticate user
    const authResult = await authMiddleware(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const teamId = searchParams.get('teamId');
    const departmentId = searchParams.get('departmentId');
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Get team members based on current user's role and department
    const employeeService = new EmployeeService();
    let teamMemberFilter: any = {
      status: 'active'
    };

    // If user is not super admin, limit to their department/team
    if (authResult.user.role !== 'super_admin') {
      // Get current user's employee record to find their department
      const currentEmployee = await employeeService.findOne({ userId: authResult.user.id });
      if (currentEmployee && currentEmployee.department) {
        teamMemberFilter.department = currentEmployee.department;
      }
    }

    if (departmentId) {
      teamMemberFilter.department = departmentId;
    }

    // Get team members
    const teamMembers = await employeeService.find(teamMemberFilter, {
      populate: [
        {
          path: 'department',
          select: 'name'
        }
      ]
    });

    const teamMemberIds = teamMembers.map(member => member._id);

    // Build filter object for tasks
    const filter: any = {
      assignedTo: { $in: teamMemberIds }
    };
    
    if (status) {
      filter.status = status;
    }
    
    if (priority) {
      filter.priority = priority;
    }
    
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Get tasks
    const taskService = new TaskService();
    const result = await taskService.findWithPagination(
      filter,
      {
        page,
        limit,
        populate: [
          {
            path: 'assignedTo',
            select: 'firstName lastName email avatar'
          },
          {
            path: 'assignedBy',
            select: 'firstName lastName email avatar'
          },
          {
            path: 'projectId',
            select: 'name description'
          }
        ],
        sort: { dueDate: 1, createdAt: -1 }
      }
    );

    // Transform data for frontend
    const transformedTasks = result.data.map((task: any) => ({
      id: task._id,
      title: task.title,
      description: task.description,
      status: task.status,
      priority: task.priority,
      category: task.category,
      dueDate: task.dueDate,
      estimatedHours: task.estimatedHours,
      actualHours: task.actualHours,
      completedDate: task.completedDate,
      qualityScore: task.qualityScore,
      assignedTo: task.assignedTo?.map((user: any) => user._id) || [],
      assignedBy: {
        id: task.assignedBy?._id,
        name: task.assignedBy ? `${task.assignedBy.firstName} ${task.assignedBy.lastName}` : 'Unknown',
        avatar: task.assignedBy?.avatar || '/placeholder-user.jpg'
      },
      project: task.projectId ? task.projectId.name : undefined,
      attachments: task.attachments || [],
      comments: task.comments || [],
      createdAt: task.createdAt,
      updatedAt: task.updatedAt
    }));

    // Transform team members for frontend
    const transformedTeamMembers = teamMembers.map((member: any) => ({
      id: member._id,
      _id: member._id,
      firstName: member.firstName,
      lastName: member.lastName,
      email: member.email,
      avatar: member.avatar || '/placeholder-user.jpg',
      department: member.department?.name || 'Unknown',
      position: member.position || 'Employee',
      role: member.role || 'employee'
    }));

    logger.info('Team tasks retrieved successfully', LogCategory.API, {
      userId: authResult.user.id,
      teamMembersCount: transformedTeamMembers.length,
      tasksCount: transformedTasks.length,
      total: result.total,
      filters: { teamId, departmentId, status, priority, search }
    });

    return NextResponse.json({
      success: true,
      message: 'Team tasks retrieved successfully',
      data: {
        tasks: transformedTasks,
        teamMembers: transformedTeamMembers,
        total: result.total,
        page: result.page,
        totalPages: result.totalPages,
        hasMore: result.hasMore
      }
    });

  } catch (error) {
    logger.error('Error retrieving team tasks', LogCategory.API, { error });
    return errorHandler.handleError(error);
  }
}

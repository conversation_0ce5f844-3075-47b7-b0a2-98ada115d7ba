import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database/connection';
import { TaskService } from '@/lib/backend/services/project/TaskService';
import { authMiddleware } from '@/lib/backend/middleware/authMiddleware';
import { errorHandler } from '@/lib/backend/utils/errorHandler';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * PATCH /api/tasks/[id]/status - Update task status
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    await connectToDatabase();

    // Authenticate user
    const authResult = await authMiddleware(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;
    const body = await request.json();
    const { status, actualHours, qualityScore } = body;

    // Validate required fields
    if (!status) {
      return NextResponse.json(
        { success: false, message: 'Status is required' },
        { status: 400 }
      );
    }

    // Get existing task to check permissions
    const taskService = new TaskService();
    const existingTask = await taskService.findById(id);

    if (!existingTask) {
      return NextResponse.json(
        { success: false, message: 'Task not found' },
        { status: 404 }
      );
    }

    // Check if user is assigned to this task or has permission to update it
    const isAssigned = existingTask.assignedTo?.includes(authResult.user.id);
    const isCreator = existingTask.assignedBy?.toString() === authResult.user.id;
    const isAdmin = ['super_admin', 'admin', 'manager'].includes(authResult.user.role);

    if (!isAssigned && !isCreator && !isAdmin) {
      return NextResponse.json(
        { success: false, message: 'You do not have permission to update this task' },
        { status: 403 }
      );
    }

    // Prepare update data
    const updateData: any = {
      status,
      updatedBy: authResult.user.id
    };

    // If task is being completed, set completion date and actual hours
    if (status === 'completed') {
      updateData.completedDate = new Date();
      
      if (actualHours !== undefined) {
        updateData.actualHours = actualHours;
      }
      
      if (qualityScore !== undefined) {
        updateData.qualityScore = qualityScore;
      }
    }

    // If task is being moved from completed to another status, clear completion data
    if (existingTask.status === 'completed' && status !== 'completed') {
      updateData.completedDate = null;
    }

    // Update task
    const updatedTask = await taskService.update(id, updateData);

    // Populate task data for response
    const populatedTask = await taskService.findById(id, {
      populate: [
        {
          path: 'assignedTo',
          select: 'firstName lastName email avatar'
        },
        {
          path: 'assignedBy',
          select: 'firstName lastName email avatar'
        },
        {
          path: 'projectId',
          select: 'name description'
        }
      ]
    });

    // Transform data for frontend
    const transformedTask = {
      id: populatedTask._id,
      title: populatedTask.title,
      description: populatedTask.description,
      status: populatedTask.status,
      priority: populatedTask.priority,
      category: populatedTask.category,
      dueDate: populatedTask.dueDate,
      estimatedHours: populatedTask.estimatedHours,
      actualHours: populatedTask.actualHours,
      completedDate: populatedTask.completedDate,
      qualityScore: populatedTask.qualityScore,
      assignedTo: populatedTask.assignedTo?.map((user: any) => user._id) || [],
      assignedBy: {
        id: populatedTask.assignedBy?._id,
        name: populatedTask.assignedBy ? `${populatedTask.assignedBy.firstName} ${populatedTask.assignedBy.lastName}` : 'Unknown',
        avatar: populatedTask.assignedBy?.avatar || '/placeholder-user.jpg'
      },
      project: populatedTask.projectId ? populatedTask.projectId.name : undefined,
      attachments: populatedTask.attachments || [],
      comments: populatedTask.comments || [],
      createdAt: populatedTask.createdAt,
      updatedAt: populatedTask.updatedAt
    };

    logger.info('Task status updated successfully', LogCategory.API, {
      userId: authResult.user.id,
      taskId: id,
      oldStatus: existingTask.status,
      newStatus: status,
      actualHours,
      qualityScore
    });

    return NextResponse.json({
      success: true,
      message: 'Task status updated successfully',
      data: transformedTask
    });

  } catch (error) {
    logger.error('Error updating task status', LogCategory.API, { error });
    return errorHandler.handleError(error);
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import { TaskService } from '@/lib/backend/services/project/TaskService';
import { authMiddleware } from '@/lib/backend/middleware/authMiddleware';
import { errorHandler } from '@/lib/backend/utils/errorHandler';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/tasks/[id] - Get task by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    await connectToDatabase();

    // Authenticate user
    const authResult = await authMiddleware(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;

    // Get task
    const taskService = new TaskService();
    const task = await taskService.findById(id, ['assignedTo', 'assignedBy', 'project']);

    if (!task) {
      return NextResponse.json(
        { success: false, message: 'Task not found' },
        { status: 404 }
      );
    }

    // Transform data for frontend
    const transformedTask = {
      id: task._id,
      title: task.title,
      description: task.description,
      status: task.status,
      priority: task.priority,
      category: task.category,
      dueDate: task.dueDate,
      estimatedHours: task.estimatedHours,
      actualHours: task.actualHours,
      completedDate: task.completedDate,
      qualityScore: task.qualityScore,
      assignedTo: task.assignedTo?.map((user: any) => user._id) || [],
      assignedBy: {
        id: task.assignedBy?._id,
        name: task.assignedBy ? `${task.assignedBy.firstName} ${task.assignedBy.lastName}` : 'Unknown',
        avatar: task.assignedBy?.avatar || '/placeholder-user.jpg'
      },
      project: task.projectId ? task.projectId.name : undefined,
      attachments: task.attachments || [],
      comments: task.comments || [],
      createdAt: task.createdAt,
      updatedAt: task.updatedAt
    };

    logger.info('Task retrieved successfully', LogCategory.API, {
      userId: authResult.user.id,
      taskId: id
    });

    return NextResponse.json({
      success: true,
      message: 'Task retrieved successfully',
      data: transformedTask
    });

  } catch (error) {
    logger.error('Error retrieving task', LogCategory.API, { error });
    return errorHandler.handleError(error);
  }
}

/**
 * PUT /api/tasks/[id] - Update task
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    await connectToDatabase();

    // Authenticate user
    const authResult = await authMiddleware(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;
    const body = await request.json();
    const {
      title,
      description,
      status,
      priority,
      category,
      dueDate,
      estimatedHours,
      actualHours,
      qualityScore,
      assignedTo
    } = body;

    // Get existing task to check permissions
    const taskService = new TaskService();
    const existingTask = await taskService.findById(id);

    if (!existingTask) {
      return NextResponse.json(
        { success: false, message: 'Task not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const isCreator = existingTask.assignedBy?.toString() === authResult.user.id;
    const isAdmin = ['super_admin', 'admin', 'manager'].includes(authResult.user.role);

    if (!isCreator && !isAdmin) {
      return NextResponse.json(
        { success: false, message: 'You do not have permission to update this task' },
        { status: 403 }
      );
    }

    // Prepare update data
    const updateData: any = {
      updatedBy: authResult.user.id
    };

    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (status !== undefined) updateData.status = status;
    if (priority !== undefined) updateData.priority = priority;
    if (category !== undefined) updateData.category = category;
    if (dueDate !== undefined) updateData.dueDate = dueDate ? new Date(dueDate) : null;
    if (estimatedHours !== undefined) updateData.estimatedHours = estimatedHours;
    if (actualHours !== undefined) updateData.actualHours = actualHours;
    if (qualityScore !== undefined) updateData.qualityScore = qualityScore;
    if (assignedTo !== undefined) updateData.assignedTo = assignedTo;

    // Update task
    const updatedTask = await taskService.updateById(id, updateData);

    if (!updatedTask) {
      return NextResponse.json(
        { success: false, message: 'Failed to update task' },
        { status: 500 }
      );
    }

    // Populate task data for response
    const populatedTask = await taskService.findById(id, ['assignedTo', 'assignedBy', 'project']);

    // Transform data for frontend
    const transformedTask = {
      id: populatedTask._id,
      title: populatedTask.title,
      description: populatedTask.description,
      status: populatedTask.status,
      priority: populatedTask.priority,
      category: populatedTask.category,
      dueDate: populatedTask.dueDate,
      estimatedHours: populatedTask.estimatedHours,
      actualHours: populatedTask.actualHours,
      completedDate: populatedTask.completedDate,
      qualityScore: populatedTask.qualityScore,
      assignedTo: populatedTask.assignedTo?.map((user: any) => user._id) || [],
      assignedBy: {
        id: populatedTask.assignedBy?._id,
        name: populatedTask.assignedBy ? `${populatedTask.assignedBy.firstName} ${populatedTask.assignedBy.lastName}` : 'Unknown',
        avatar: populatedTask.assignedBy?.avatar || '/placeholder-user.jpg'
      },
      project: populatedTask.projectId ? populatedTask.projectId.name : undefined,
      attachments: populatedTask.attachments || [],
      comments: populatedTask.comments || [],
      createdAt: populatedTask.createdAt,
      updatedAt: populatedTask.updatedAt
    };

    logger.info('Task updated successfully', LogCategory.API, {
      userId: authResult.user.id,
      taskId: id
    });

    return NextResponse.json({
      success: true,
      message: 'Task updated successfully',
      data: transformedTask
    });

  } catch (error) {
    logger.error('Error updating task', LogCategory.API, { error });
    return errorHandler.handleError(error);
  }
}

/**
 * DELETE /api/tasks/[id] - Delete task
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    await connectToDatabase();

    // Authenticate user
    const authResult = await authMiddleware(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;

    // Get existing task to check permissions
    const taskService = new TaskService();
    const existingTask = await taskService.findById(id);

    if (!existingTask) {
      return NextResponse.json(
        { success: false, message: 'Task not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const isCreator = existingTask.assignedBy?.toString() === authResult.user.id;
    const isAdmin = ['super_admin', 'admin'].includes(authResult.user.role);

    if (!isCreator && !isAdmin) {
      return NextResponse.json(
        { success: false, message: 'You do not have permission to delete this task' },
        { status: 403 }
      );
    }

    // Delete task
    await taskService.deleteById(id);

    logger.info('Task deleted successfully', LogCategory.API, {
      userId: authResult.user.id,
      taskId: id
    });

    return NextResponse.json({
      success: true,
      message: 'Task deleted successfully'
    });

  } catch (error) {
    logger.error('Error deleting task', LogCategory.API, { error });
    return errorHandler.handleError(error);
  }
}

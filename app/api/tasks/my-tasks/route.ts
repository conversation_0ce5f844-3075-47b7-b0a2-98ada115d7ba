import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database/connection';
import { TaskService } from '@/lib/backend/services/project/TaskService';
import { authMiddleware } from '@/lib/backend/middleware/authMiddleware';
import { errorHandler } from '@/lib/backend/utils/errorHandler';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/tasks/my-tasks - Get tasks assigned to current user
 */
export async function GET(request: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    // Authenticate user
    const authResult = await authMiddleware(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Build filter object - only tasks assigned to current user
    const filter: any = {
      assignedTo: { $in: [authResult.user.id] }
    };
    
    if (status) {
      filter.status = status;
    }
    
    if (priority) {
      filter.priority = priority;
    }
    
    if (category) {
      filter.category = category;
    }
    
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Get tasks
    const taskService = new TaskService();
    const result = await taskService.findWithPagination(
      filter,
      {
        page,
        limit,
        populate: [
          {
            path: 'assignedTo',
            select: 'firstName lastName email avatar'
          },
          {
            path: 'assignedBy',
            select: 'firstName lastName email avatar'
          },
          {
            path: 'projectId',
            select: 'name description'
          }
        ],
        sort: { dueDate: 1, createdAt: -1 } // Sort by due date first, then creation date
      }
    );

    // Transform data for frontend
    const transformedTasks = result.data.map((task: any) => ({
      id: task._id,
      title: task.title,
      description: task.description,
      status: task.status,
      priority: task.priority,
      category: task.category,
      dueDate: task.dueDate,
      estimatedHours: task.estimatedHours,
      actualHours: task.actualHours,
      completedDate: task.completedDate,
      qualityScore: task.qualityScore,
      assignedTo: task.assignedTo?.map((user: any) => user._id) || [],
      assignedBy: {
        id: task.assignedBy?._id,
        name: task.assignedBy ? `${task.assignedBy.firstName} ${task.assignedBy.lastName}` : 'Unknown',
        avatar: task.assignedBy?.avatar || '/placeholder-user.jpg'
      },
      project: task.projectId ? task.projectId.name : undefined,
      attachments: task.attachments || [],
      comments: task.comments || [],
      createdAt: task.createdAt,
      updatedAt: task.updatedAt
    }));

    logger.info('My tasks retrieved successfully', LogCategory.API, {
      userId: authResult.user.id,
      count: transformedTasks.length,
      total: result.total,
      filters: { status, priority, category, search }
    });

    return NextResponse.json({
      success: true,
      message: 'My tasks retrieved successfully',
      data: {
        tasks: transformedTasks,
        total: result.total,
        page: result.page,
        totalPages: result.totalPages,
        hasMore: result.hasMore
      }
    });

  } catch (error) {
    logger.error('Error retrieving my tasks', LogCategory.API, { error });
    return errorHandler.handleError(error);
  }
}

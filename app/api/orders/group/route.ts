import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';
import { groupOrderService } from '@/lib/backend/services/orders/GroupOrderService';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

// app/api/orders/group/route.ts
// Auto-generated type definitions
interface MongoFilter {
  [key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined;
  date?: { $gte?: Date; $lte?: Date };
  amount?: { $gte?: number; $lte?: number };
}
/**
 * POST /api/orders/group
 * Create a new group order
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Get request body
    const body = await req.json();
    // Validate required fields
    if (!body.name || !body.items || !Array.isArray(body.items) || body.items.length === 0) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Create group order
    const groupOrder = await groupOrderService.createGroupOrder({
      name: body.name,
      description: body.description,
      status: body.status || 'draft',
      items: body.items,
      participants: body.participants || [],
      deadline: body.deadline ? new Date(body.deadline) : undefined,
      totalAmount: body.totalAmount,
      createdBy: user.id ? new mongoose.Types.ObjectId(user.id) : undefined
    });
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Group order created successfully',
      data: groupOrder
    });
  } catch (error) {
    logger.error('Error creating group order:', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to create group order.' },
      { status: 500 }
    );
  }
}
/**
 * GET /api/orders/group
 * Get all group orders
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const createdBy = searchParams.get('createdBy');
    const participantId = searchParams.get('participantId');
    let result;
    // Get group orders based on query parameters
    if (createdBy) {
      // Get group orders by creator
      result = await groupOrderService.getByCreator(createdBy, {
        status: status as any,
        page,
        limit
      });
    } else if (participantId) {
      // Get group orders by participant
      result = await groupOrderService.getByParticipant(participantId, {
        status: status as any,
        page,
        limit
      });
    } else {
      // Get all group orders (with pagination)
      // Define MongoDB filter interface
      interface MongoFilter {
        [key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined;
        date?: { $gte?: Date; $lte?: Date };
        amount?: { $gte?: number; $lte?: number };
      }
      const filter: MongoFilter = {};
      if (status) {
        filter.status = status;
      }
      result = await groupOrderService.paginate(filter, page, limit, { createdAt: -1 }, ['createdBy', 'participants']);
    }
    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error fetching group orders:', LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to fetch group orders.' },
      { status: 500 }
    );
  }
}
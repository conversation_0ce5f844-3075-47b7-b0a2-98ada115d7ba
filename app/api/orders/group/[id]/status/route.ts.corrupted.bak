import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';
import { groupOrderService } from '@/lib/backend/services/orders/GroupOrderService';

/**
 * PATCH /api/orders/group/[id]/status
 * Update the status of a group order
 */
export async function PATCH(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();
    const { status } = body;

    if (!status || !['draft', 'active', 'completed', 'cancelled'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Update group order status
    const groupOrder = await groupOrderService.updateStatus(id, status as "active" | "completed" | "cancelled" | "draft");

    return NextResponse.json({
      success: true,
      message: 'Group order status updated successfully',
      data: groupOrder
    });
  } catch (error) {
    logger.error(`Error updating group order status:`, LogCategory.API, error);
    return NextResponse.json(
      { error: 'Failed to update group order status.' },
      { status: 500 }
    );
  }
}

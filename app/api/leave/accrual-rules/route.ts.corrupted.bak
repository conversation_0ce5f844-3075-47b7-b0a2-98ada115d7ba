import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import LeaveAccrualRule from '@/models/leave/LeaveAccrualRule';
import mongoose from 'mongoose';

/**
 * GET /api/leave/accrual-rules
 * Get all leave accrual rules
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const active = searchParams.get('active');
    const leaveTypeId = searchParams.get('leaveTypeId');

    // Build query
    const query: any = {};
    if (active === 'true') {
      query.isActive = true;
    }
    if (leaveTypeId) {
      query.leaveTypeId = leaveTypeId;
    }

    // Get accrual rules
    const accrualRules = await LeaveAccrualRule.find(query)
      .populate('leaveTypeId', 'name code color')
      .populate('eligibilityCriteria.departments', 'name')
      .populate('createdBy', 'firstName lastName')
      .populate('updatedBy', 'firstName lastName')
      .sort({ createdAt: -1 });

    return NextResponse.json(accrualRules);

  } catch (error: unknown) {
    logger.error('Error fetching leave accrual rules', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while fetching accrual rules' 
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/leave/accrual-rules
 * Create a new leave accrual rule
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.name || !body.leaveTypeId || !body.accrualFrequency || !body.accrualMethod || !body.effectiveFrom) {
      return NextResponse.json(
        { error: 'Missing required fields: name, leaveTypeId, accrualFrequency, accrualMethod, effectiveFrom' },
        { status: 400 }
      );
    }

    // Validate leaveTypeId
    if (!mongoose.Types.ObjectId.isValid(body.leaveTypeId)) {
      return NextResponse.json(
        { error: 'Invalid leave type ID format' },
        { status: 400 }
      );
    }

    // Validate accrual frequency
    if (!['monthly', 'quarterly', 'annually'].includes(body.accrualFrequency)) {
      return NextResponse.json(
        { error: 'Invalid accrual frequency. Must be monthly, quarterly, or annually' },
        { status: 400 }
      );
    }

    // Validate accrual method
    if (!['fixed', 'tenure-based', 'pro-rata'].includes(body.accrualMethod)) {
      return NextResponse.json(
        { error: 'Invalid accrual method. Must be fixed, tenure-based, or pro-rata' },
        { status: 400 }
      );
    }

    // Validate method-specific fields
    if (body.accrualMethod === 'fixed' && !body.fixedDaysPerPeriod) {
      return NextResponse.json(
        { error: 'fixedDaysPerPeriod is required for fixed accrual method' },
        { status: 400 }
      );
    }

    if (body.accrualMethod === 'tenure-based' && (!body.tenureRules || !Array.isArray(body.tenureRules) || body.tenureRules.length === 0)) {
      return NextResponse.json(
        { error: 'tenureRules array is required for tenure-based accrual method' },
        { status: 400 }
      );
    }

    // Check for overlapping rules
    const existingRule = await LeaveAccrualRule.findOne({
      leaveTypeId: body.leaveTypeId,
      isActive: true,
      effectiveFrom: { $lte: new Date(body.effectiveFrom) },
      $or: [
        { effectiveTo: { $exists: false } },
        { effectiveTo: { $gte: new Date(body.effectiveFrom) } }
      ]
    });

    if (existingRule) {
      return NextResponse.json(
        { error: 'An active accrual rule already exists for this leave type in the specified date range' },
        { status: 400 }
      );
    }

    // Set created by
    body.createdBy = user.id;

    // Create accrual rule
    const accrualRule = new LeaveAccrualRule(body);
    await accrualRule.save();

    // Populate the response
    const populatedRule = await LeaveAccrualRule.findById(accrualRule._id)
      .populate('leaveTypeId', 'name code color')
      .populate('eligibilityCriteria.departments', 'name')
      .populate('createdBy', 'firstName lastName');

    return NextResponse.json({
      success: true,
      message: 'Leave accrual rule created successfully',
      data: populatedRule
    });

  } catch (error: unknown) {
    logger.error('Error creating leave accrual rule', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while creating accrual rule' 
      },
      { status: 500 }
    );
  }
}

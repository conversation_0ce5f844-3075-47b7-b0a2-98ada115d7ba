// app/api/leave/requests/[id]/reject/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { leaveService } from '@/services/leave/LeaveService';
import Leave from '@/models/leave/Leave';
import Department from '@/models/Department';
import { leaveAttendanceService } from '@/services/attendance/LeaveAttendanceService';

/**
 * POST /api/leave/requests/[id]/reject
 * Reject a leave request
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.reason) {
      return NextResponse.json(
        { error: 'Missing required field: reason' },
        { status: 400 }
      );
    }

    // Resolve the params promise
    const { id } = await params;

    // Get leave request
    const leaveRequest = await Leave.findById(id)
      .populate('employeeId', 'departmentId');

    if (!leaveRequest) {
      return NextResponse.json(
        { error: 'Leave request not found' },
        { status: 404 }
      );
    }

    // Check if user is department head and has permission to reject
    if (hasRequiredPermissions(user, [UserRole.DEPARTMENT_HEAD]) &&
        !hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.HR_DIRECTOR,
          UserRole.HR_MANAGER,
          UserRole.HR_SPECIALIST
        ])) {
      // Get department where user is head
      const department = await Department.findOne({ head: user.id });

      if (!department || department._id.toString() !== leaveRequest.employeeId.departmentId.toString()) {
        return NextResponse.json(
          { error: 'You do not have permission to reject this leave request' },
          { status: 403 }
        );
      }
    }

    // Check if leave request is already approved or rejected
    if (leaveRequest.status !== 'pending') {
      return NextResponse.json(
        { error: `Leave request is already ${leaveRequest.status}` },
        { status: 400 }
      );
    }

    // Update leave request status
    const updatedLeaveRequest = await leaveService.updateLeaveRequestStatus(
      id,
      'rejected',
      user.id,
      body.reason
    );

    // Remove leave from attendance records if any exist
    try {
      await leaveAttendanceService.removeLeaveFromAttendance(id, user.id);
    } catch (error) {
      logger.error('Error removing leave from attendance records', LogCategory.HR, error);
      // Don't fail the request if attendance update fails
    }

    return NextResponse.json({
      success: true,
      message: 'Leave request rejected successfully',
      data: updatedLeaveRequest
    });
  } catch (error: unknown) {
    logger.error('Error rejecting leave request', LogCategory.HR, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

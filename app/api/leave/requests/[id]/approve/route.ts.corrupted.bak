import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { leaveService } from '@/services/leave/LeaveService';
import Leave from '@/models/leave/Leave';
import { Employee } from '@/models/Employee';
import Department from '@/models/Department';
import { accountingService } from '@/services/accounting/AccountingService';
import { leaveAttendanceService } from '@/services/attendance/LeaveAttendanceService';

/**
 * POST /api/leave/requests/[id]/approve
 * Approve a leave request
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Get leave request
    const leaveRequest = await Leave.findById(id)
      .populate('employeeId', 'firstName lastName departmentId')
      .populate('leaveTypeId');

    if (!leaveRequest) {
      return NextResponse.json(
        { error: 'Leave request not found' },
        { status: 404 }
      );
    }

    // Check if user is department head and has permission to approve
    if (hasRequiredPermissions(user, [UserRole.DEPARTMENT_HEAD]) &&
        !hasRequiredPermissions(user, [
          UserRole.SUPER_ADMIN,
          UserRole.SYSTEM_ADMIN,
          UserRole.HR_DIRECTOR,
          UserRole.HR_MANAGER,
          UserRole.HR_SPECIALIST
        ])) {
      // Get department where user is head
      const department = await Department.findOne({ head: user.id });

      if (!department || department._id.toString() !== leaveRequest.employeeId.departmentId.toString()) {
        return NextResponse.json(
          { error: 'You do not have permission to approve this leave request' },
          { status: 403 }
        );
      }
    }

    // Check if leave request is already approved or rejected
    if (leaveRequest.status !== 'pending') {
      return NextResponse.json(
        { error: `Leave request is already ${leaveRequest.status}` },
        { status: 400 }
      );
    }

    // Update leave request status
    const updatedLeaveRequest = await leaveService.updateLeaveRequestStatus(
      id,
      'approved',
      user.id
    );

    // Create accounting entry for paid leave if applicable
    if (leaveRequest.leaveTypeId.isPaid) {
      try {
        // Create accounting entry
        await accountingService.createJournalEntry({
          date: new Date(),
          reference: `LEAVE-${leaveRequest.leaveId}`,
          description: `Paid leave for ${leaveRequest.employeeId.firstName} ${leaveRequest.employeeId.lastName}`,
          entries: [
            {
              accountId: process.env.SALARY_EXPENSE_ACCOUNT_ID || '60001', // Salary Expense
              debit: 0,
              credit: 0, // This is just a memo entry, no actual money movement
              description: `Paid leave: ${leaveRequest.leaveTypeId.name}`,
              metadata: {
                employeeId: leaveRequest.employeeId._id,
                leaveId: leaveRequest._id,
                leaveType: leaveRequest.leaveTypeId.name,
                startDate: leaveRequest.startDate,
                endDate: leaveRequest.endDate,
                duration: leaveRequest.duration
              }
            }
          ],
          createdBy: user.id,
          status: 'posted',
          postingDate: new Date()
        });
      } catch (error) {
        logger.error('Error creating accounting entry for paid leave', LogCategory.ACCOUNTING, error);
        // Don't fail the request if accounting entry fails
      }
    }

    // Sync leave with attendance records
    try {
      await leaveAttendanceService.syncLeaveWithAttendance(id, user.id);
    } catch (error) {
      logger.error('Error syncing leave with attendance records', LogCategory.HR, error);
      // Don't fail the request if attendance sync fails
    }

    return NextResponse.json({
      success: true,
      message: 'Leave request approved successfully',
      data: updatedLeaveRequest
    });
  } catch (error: unknown) {
    logger.error('Error approving leave request', LogCategory.HR, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

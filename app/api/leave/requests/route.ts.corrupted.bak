import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { leaveService } from '@/services/leave/LeaveService';
import Leave from '@/models/leave/Leave';

/**
 * GET /api/leave/requests
 * Get leave requests - all requests for HR users, own requests for regular users
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const year = parseInt(searchParams.get('year') || new Date().getFullYear().toString());

    // Build query based on user permissions
    const query: Record<string, unknown> = {
      startDate: {
        $gte: new Date(year, 0, 1),
        $lt: new Date(year + 1, 0, 1)
      }
    };

    // Check if user has HR permissions to see all requests
    const hasHRPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD
    ]);

    // If user doesn't have HR permissions, only show their own requests
    if (!hasHRPermission) {
      query.employeeId = user.id;
    }
    // If user has HR permissions, show all requests (no employeeId filter)

    if (status) {
      query.status = status;
    }

    // Get leave requests
    const leaveRequests = await Leave.find(query)
      .sort({ startDate: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate('employeeId', 'firstName lastName position avatar employeeId')
      .populate('leaveTypeId', 'name code color')
      .populate('approvedBy', 'firstName lastName')
      .lean();

    // Get total count
    const total = await Leave.countDocuments(query);

    return NextResponse.json({
      data: leaveRequests,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error: unknown) {
    logger.error('Error getting leave requests', LogCategory.HR, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/leave/requests
 * Create a new leave request
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.leaveTypeId || !body.startDate || !body.endDate || !body.reason) {
      return NextResponse.json(
        { error: 'Missing required fields: leaveTypeId, startDate, endDate, reason' },
        { status: 400 }
      );
    }

    // Determine employee ID
    let employeeId = user.id; // Default to current user

    if (body.employeeId && body.employeeId !== user.id) {
      // Check if user has permission to create requests for other employees
      const hasHRPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.HR_DIRECTOR,
        UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST,
        UserRole.DEPARTMENT_HEAD,
        UserRole.TEAM_LEADER
      ]);

      if (!hasHRPermission) {
        return NextResponse.json(
          { error: 'Forbidden: You do not have permission to create leave requests for other employees' },
          { status: 403 }
        );
      }

      employeeId = body.employeeId;
    }

    // Set the final employee ID
    body.employeeId = employeeId;

    // Create leave request
    const leaveRequest = await leaveService.createLeaveRequest(body, user.id);

    return NextResponse.json(
      {
        success: true,
        message: 'Leave request submitted successfully',
        data: leaveRequest
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error creating leave request', LogCategory.HR, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

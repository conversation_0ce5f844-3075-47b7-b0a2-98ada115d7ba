import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { leaveApprovalWorkflowService } from '@/services/leave/LeaveApprovalWorkflowService';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

interface ApproveStepPayload {
  stepNumber: number;
  comments?: string;
}
/**
 * POST /api/leave/workflows/[id]/approve
 * Approve a workflow step
 */
export async function POST(
  req: NextRequest, 
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Get the workflow ID from params
    const { id } = await params;
    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid workflow ID format' },
        { status: 400 }
      );
    }
    // Get request body
    const body: ApproveStepPayload = await req.json();
    // Validate required fields
    if (!body.stepNumber) {
      return NextResponse.json(
        { error: 'Missing required field: stepNumber' },
        { status: 400 }
      );
    }
    // Approve the workflow step
    const result = await leaveApprovalWorkflowService.approveStep(
      id,
      body.stepNumber,
      user.id,
      body.comments
    );
    return NextResponse.json({
      success: true,
      message: result.message,
      data: {
        workflowCompleted: result.workflowCompleted,
        finalStatus: result.finalStatus,
        nextStep: result.nextStep,
        nextApprovers: result.nextApprovers
      }
    });
  } catch (error: unknown) {
    logger.error('Error approving workflow step', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while approving workflow step' 
      },
      { status: 500 }
    );
  }
}
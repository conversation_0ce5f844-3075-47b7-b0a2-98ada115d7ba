import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { leaveReportingService } from '@/services/leave/LeaveReportingService';

export const runtime = 'nodejs';

/**
 * GET /api/leave/reports/department
 * Generate department analytics report
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions to view reports' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get query parameters
    const { searchParams } = new URL(req.url);
    const yearParam = searchParams.get('year');
    const format = searchParams.get('format'); // 'json' or 'csv'
    // Validate year
    if (!yearParam) {
      return NextResponse.json(
        { error: 'Missing required parameter: year' },
        { status: 400 }
      );
    }
    const year = parseInt(yearParam);
    if (isNaN(year) || year < 2000 || year > new Date().getFullYear() + 5) {
      return NextResponse.json(
        { error: 'Invalid year. Must be between 2000 and current year + 5' },
        { status: 400 }
      );
    }
    // Generate report
    const report = await leaveReportingService.generateDepartmentAnalytics(year);
    // Handle CSV export
    if (format === 'csv') {
      const csvContent = await leaveReportingService.exportToCSV(report, 'department');
      return new NextResponse(csvContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="department-analytics-report-${year}.csv"`
        }
      });
    }
    return NextResponse.json({
      success: true,
      data: report,
      metadata: {
        reportType: 'department',
        year,
        generatedAt: new Date().toISOString(),
        generatedBy: user.id
      }
    });
  } catch (error: unknown) {
    logger.error('Error generating department analytics report', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while generating the report' 
      },
      { status: 500 }
    );
  }
}
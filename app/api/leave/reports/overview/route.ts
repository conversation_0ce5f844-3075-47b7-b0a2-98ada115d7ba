import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { LeaveReportingService } from '@/lib/backend/services/hr/LeaveReportingService';

export const runtime = 'nodejs';

/**
 * GET /api/leave/reports/overview
 * Generate leave overview report with key statistics
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions to view reports' },
        { status: 403 }
      );
    }

    await connectToDatabase();

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const timeRange = searchParams.get('timeRange') || 'month';
    const departmentId = searchParams.get('departmentId');
    const format = searchParams.get('format') || 'json';

    // Calculate date range based on timeRange
    const now = new Date();
    let startDate: Date;
    let endDate: Date = now;

    switch (timeRange) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'quarter':
        startDate = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      case 'month':
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
    }

    logger.info('Generating leave overview report', LogCategory.REPORTING, {
      userId: user.id,
      timeRange,
      departmentId,
      startDate,
      endDate
    });

    // Initialize reporting service
    const leaveReportingService = new LeaveReportingService();

    // Generate overview report
    const report = await leaveReportingService.generateOverviewReport({
      startDate,
      endDate,
      departmentId,
      timeRange
    });

    // Log report data for debugging
    logger.info('Generated leave overview report', LogCategory.REPORTING, {
      hasOverview: !!report.overview,
      totalLeaveDays: report.overview?.totalLeaveDays || 0,
      leavesByTypeCount: report.leavesByType?.length || 0,
      leavesByDepartmentCount: report.leavesByDepartment?.length || 0,
      monthlyTrendsCount: report.monthlyTrends?.length || 0,
      format
    });

    // Handle export formats
    if (format === 'csv') {
      const csvContent = await leaveReportingService.exportToCSV(report, 'overview');
      return new NextResponse(csvContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="leave-overview-report-${timeRange}.csv"`
        }
      });
    }

    if (format === 'excel') {
      const excelBuffer = await leaveReportingService.exportToExcel(report, 'overview');
      return new NextResponse(excelBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': `attachment; filename="leave-overview-report-${timeRange}.xlsx"`
        }
      });
    }

    if (format === 'pdf') {
      const pdfBuffer = await leaveReportingService.exportToPDF(report, 'overview');
      return new NextResponse(pdfBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="leave-overview-report-${timeRange}.pdf"`
        }
      });
    }

    return NextResponse.json({
      success: true,
      data: report,
      metadata: {
        reportType: 'overview',
        timeRange,
        departmentId,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        generatedAt: new Date().toISOString(),
        generatedBy: user.id
      }
    });

  } catch (error) {
    logger.error('Error generating leave overview report', LogCategory.REPORTING, error);
    return NextResponse.json(
      { error: 'Failed to generate leave overview report' },
      { status: 500 }
    );
  }
}

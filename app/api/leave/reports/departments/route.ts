import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

/**
 * GET /api/leave/reports/departments
 * Get list of departments for leave reports filtering
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions to view departments' },
        { status: 403 }
      );
    }

    await connectToDatabase();

    logger.info('Fetching departments for leave reports', LogCategory.REPORTING, {
      userId: user.id
    });

    // Get departments that have employees with leave requests
    const Department = mongoose.models.Department;
    if (!Department) {
      return NextResponse.json({ error: 'Department model not found' }, { status: 500 });
    }

    const Employee = mongoose.models.Employee;
    if (!Employee) {
      return NextResponse.json({ error: 'Employee model not found' }, { status: 500 });
    }

    const departments = await Department.aggregate([
      {
        $lookup: {
          from: 'employees',
          localField: '_id',
          foreignField: 'departmentId',
          as: 'employees'
        }
      },
      {
        $match: {
          employees: { $ne: [] }
        }
      },
      {
        $project: {
          _id: 1,
          name: 1,
          code: 1,
          employeeCount: { $size: '$employees' }
        }
      },
      {
        $sort: { name: 1 }
      }
    ]);

    return NextResponse.json({
      success: true,
      data: departments.map(dept => ({
        id: dept._id.toString(),
        name: dept.name,
        code: dept.code,
        employeeCount: dept.employeeCount
      }))
    });

  } catch (error) {
    logger.error('Error fetching departments for leave reports', LogCategory.REPORTING, error);
    return NextResponse.json(
      { error: 'Failed to fetch departments' },
      { status: 500 }
    );
  }
}

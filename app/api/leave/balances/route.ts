import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { leaveService } from '@/services/leave/LeaveService';
import Employee from '@/models/Employee';
import LeaveBalance from '@/models/leave/LeaveBalance';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

// Custom auth system doesn't require authOptions;
/**
 * GET /api/leave/balances
 * Get leave balances for the current user, specified employee, or all employees (for HR managers)
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const requestedEmployeeId = searchParams.get('employeeId');
    const year = parseInt(searchParams.get('year') || new Date().getFullYear().toString());
    const getAllEmployees = searchParams.get('all') === 'true';
    const department = searchParams.get('department');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    // Check if user wants all employees' balances
    if (getAllEmployees) {
      // Check if user has permission to view all employees' balances
      const hasHRPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.HR_DIRECTOR,
        UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST,
        UserRole.DEPARTMENT_HEAD,
        UserRole.TEAM_LEADER
      ]);
      if (!hasHRPermission) {
        return NextResponse.json(
          { error: 'Forbidden: You do not have permission to view all employees\' leave balances' },
          { status: 403 }
        );
      }
      // Get all employees' leave balances
      const skip = (page - 1) * limit;
      // Build employee filter
      const employeeFilter: any = { employmentStatus: 'active' };
      if (department) {
        employeeFilter.department = department;
      }
      // Get employees with pagination
      const employees = await Employee.find(employeeFilter)
        .select('_id firstName lastName employeeId department position')
        .skip(skip)
        .limit(limit)
        .sort({ firstName: 1, lastName: 1 });
      const totalEmployees = await Employee.countDocuments(employeeFilter);
      // Get leave balances for these employees
      const employeeIds = employees.map(emp => emp._id);
      const leaveBalances = await LeaveBalance.find({
        employeeId: { $in: employeeIds },
        year
      }).populate('leaveTypeId', 'name code color isPaid')
        .populate('employeeId', 'firstName lastName employeeId department position');
      // Group balances by employee
      const balancesByEmployee = leaveBalances.reduce((acc, balance) => {
        const empId = balance.employeeId._id.toString();
        if (!acc[empId]) {
          acc[empId] = [];
        }
        acc[empId].push(balance);
        return acc;
      }, {} as Record<string, any[]>);
      // Format response
      const employeesWithBalances = employees.map(employee => {
        const empBalances = balancesByEmployee[employee._id.toString()] || [];
        const totalDays = empBalances.reduce((sum, b) => sum + b.totalDays, 0);
        const usedDays = empBalances.reduce((sum, b) => sum + b.usedDays, 0);
        const pendingDays = empBalances.reduce((sum, b) => sum + b.pendingDays, 0);
        const remainingDays = empBalances.reduce((sum, b) => sum + b.remainingDays, 0);
        return {
          employee: {
            _id: employee._id,
            firstName: employee.firstName,
            lastName: employee.lastName,
            employeeId: employee.employeeId,
            department: employee.department,
            position: employee.position
          },
          balances: empBalances,
          summary: {
            totalDays,
            usedDays,
            pendingDays,
            remainingDays,
            utilizationPercentage: totalDays > 0 ? Math.round((usedDays / totalDays) * 100) : 0
          }
        };
      });
      return NextResponse.json({
        data: employeesWithBalances,
        pagination: {
          page,
          limit,
          total: totalEmployees,
          totalPages: Math.ceil(totalEmployees / limit),
          hasNext: page * limit < totalEmployees,
          hasPrev: page > 1
        },
        year,
        filters: {
          department
        }
      });
    }
    // Single employee logic (existing functionality)
    let employeeId = user.id; // Default to current user
    if (requestedEmployeeId) {
      // Check if user has permission to view other employees' balances
      const hasHRPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.HR_DIRECTOR,
        UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST,
        UserRole.DEPARTMENT_HEAD,
        UserRole.TEAM_LEADER
      ]);
      if (!hasHRPermission) {
        return NextResponse.json(
          { error: 'Forbidden: You do not have permission to view other employees\' leave balances' },
          { status: 403 }
        );
      }
      employeeId = requestedEmployeeId;
    }
    // Check if employee exists
    const employee = await Employee.findById(employeeId);
    if (!employee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }
    // Get leave balances
    const leaveBalances = await leaveService.getLeaveBalances(employeeId, year);
    return NextResponse.json({
      data: leaveBalances,
      employee: {
        _id: employee._id,
        firstName: employee.firstName,
        lastName: employee.lastName,
        employeeId: employee.employeeId
      },
      year
    });
  } catch (error: unknown) {
    logger.error('Error getting leave balances', LogCategory.HR, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { leaveEncashmentService } from '@/services/leave/LeaveEncashmentService';

export const runtime = 'nodejs';

/**
 * GET /api/leave/encashments/eligibility
 * Check encashment eligibility for an employee
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Get query parameters
    const { searchParams } = new URL(req.url);
    const employeeId = searchParams.get('employeeId');
    const leaveTypeId = searchParams.get('leaveTypeId');
    const year = searchParams.get('year');
    // Validate required parameters
    if (!employeeId || !leaveTypeId || !year) {
      return NextResponse.json(
        { error: 'Missing required parameters: employeeId, leaveTypeId, year' },
        { status: 400 }
      );
    }
    // Check permissions - users can only check their own eligibility unless they have HR permissions
    const hasHRPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);
    if (!hasHRPermission && employeeId !== user.id) {
      return NextResponse.json(
        { error: 'Forbidden - You can only check your own encashment eligibility' },
        { status: 403 }
      );
    }
    // Validate year
    const yearNum = parseInt(year);
    if (isNaN(yearNum) || yearNum < 2000 || yearNum > new Date().getFullYear() + 5) {
      return NextResponse.json(
        { error: 'Invalid year format' },
        { status: 400 }
      );
    }
    // Check eligibility
    const eligibility = await leaveEncashmentService.checkEncashmentEligibility(
      employeeId,
      leaveTypeId,
      yearNum
    );
    return NextResponse.json({
      success: true,
      data: eligibility
    });
  } catch (error: unknown) {
    logger.error('Error checking encashment eligibility', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while checking eligibility' 
      },
      { status: 500 }
    );
  }
}
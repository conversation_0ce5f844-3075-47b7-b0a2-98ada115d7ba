import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { leaveEncashmentService } from '@/services/leave/LeaveEncashmentService';
import mongoose from 'mongoose';

interface RejectEncashmentPayload {
  rejectionReason: string;
}

/**
 * POST /api/leave/encashments/[id]/reject
 * Reject a leave encashment request
 */
export async function POST(
  req: NextRequest, 
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only <PERSON>R can reject encashments
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions to reject encashments' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get the encashment ID from params
    const { id } = await params;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid encashment ID format' },
        { status: 400 }
      );
    }

    // Get request body
    const body: RejectEncashmentPayload = await req.json();

    // Validate required fields
    if (!body.rejectionReason) {
      return NextResponse.json(
        { error: 'Missing required field: rejectionReason' },
        { status: 400 }
      );
    }

    // Reject the encashment request
    const encashment = await leaveEncashmentService.rejectEncashmentRequest(
      id, 
      user.id, 
      body.rejectionReason
    );

    return NextResponse.json({
      success: true,
      message: 'Leave encashment request rejected successfully',
      data: encashment
    });

  } catch (error: unknown) {
    logger.error('Error rejecting encashment request', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while rejecting encashment request' 
      },
      { status: 500 }
    );
  }
}

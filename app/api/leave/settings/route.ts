import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { leaveSettingsService } from '@/lib/backend/services/hr/LeaveSettingsService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

/**
 * GET /api/leave/settings
 * Get leave settings
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only admin roles can view settings
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions to view leave settings' },
        { status: 403 }
      );
    }

    logger.info('Getting leave settings', LogCategory.HR, {
      userId: user.id,
      userRole: user.role
    });

    // Get organization ID from query params (optional)
    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get('organizationId');

    // Get settings
    const settings = await leaveSettingsService.getSettings(organizationId || undefined);

    return NextResponse.json({
      success: true,
      data: settings
    });

  } catch (error) {
    logger.error('Error getting leave settings', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: 'Failed to get leave settings',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/leave/settings
 * Update leave settings
 */
export async function PUT(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only admin roles can update settings
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions to update leave settings' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await req.json();
    const { settings, organizationId } = body;

    if (!settings) {
      return NextResponse.json(
        { error: 'Settings data is required' },
        { status: 400 }
      );
    }

    logger.info('Updating leave settings', LogCategory.HR, {
      userId: user.id,
      userRole: user.role,
      organizationId
    });

    // Validate settings
    const validation = leaveSettingsService.validateSettings(settings);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          error: 'Invalid settings data',
          details: validation.errors
        },
        { status: 400 }
      );
    }

    // Update settings
    const updatedSettings = await leaveSettingsService.updateSettings(
      settings,
      user.id,
      organizationId
    );

    logger.info('Leave settings updated successfully', LogCategory.HR, {
      settingsId: updatedSettings._id,
      userId: user.id
    });

    return NextResponse.json({
      success: true,
      data: updatedSettings,
      message: 'Leave settings updated successfully'
    });

  } catch (error) {
    logger.error('Error updating leave settings', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: 'Failed to update leave settings',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/leave/settings/reset
 * Reset settings to defaults
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only super admin and system admin can reset settings
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Only system administrators can reset settings' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await req.json();
    const { organizationId } = body;

    logger.info('Resetting leave settings to defaults', LogCategory.HR, {
      userId: user.id,
      userRole: user.role,
      organizationId
    });

    // Reset settings
    const resetSettings = await leaveSettingsService.resetToDefaults(
      user.id,
      organizationId
    );

    logger.info('Leave settings reset to defaults successfully', LogCategory.HR, {
      settingsId: resetSettings._id,
      userId: user.id
    });

    return NextResponse.json({
      success: true,
      data: resetSettings,
      message: 'Leave settings reset to defaults successfully'
    });

  } catch (error) {
    logger.error('Error resetting leave settings', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: 'Failed to reset leave settings',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import LeaveType from '@/models/leave/LeaveType';

/**
 * GET /api/leave/types
 * Get leave types
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const activeOnly = searchParams.get('activeOnly') === 'true';
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');

    // Build query
    const query: Record<string, unknown> = {};
    if (activeOnly) {
      query.isActive = true;
    }

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { code: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Get total count
    const total = await LeaveType.countDocuments(query);

    // Get leave types with pagination
    const leaveTypes = await LeaveType.find(query)
      .populate('createdBy', 'firstName lastName')
      .populate('updatedBy', 'firstName lastName')
      .populate('applicableDepartments', 'name')
      .sort({ name: 1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();

    return NextResponse.json({
      data: leaveTypes,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1
      }
    });
  } catch (error: unknown) {
    logger.error('Error getting leave types', LogCategory.HR, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred while fetching leave types' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/leave/types
 * Create a new leave type
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.name || !body.code || body.defaultDays === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: name, code, defaultDays' },
        { status: 400 }
      );
    }

    // Set default color if not provided
    if (!body.color) {
      body.color = '#3B82F6';
    }

    // Check if leave type with same code already exists
    const existingLeaveType = await LeaveType.findOne({ code: body.code });
    if (existingLeaveType) {
      return NextResponse.json(
        { error: `Leave type with code ${body.code} already exists` },
        { status: 400 }
      );
    }

    // Set created by
    body.createdBy = user.id;

    // Create leave type
    const leaveType = new LeaveType(body);
    await leaveType.save();

    return NextResponse.json(
      {
        success: true,
        message: 'Leave type created successfully',
        data: leaveType
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error creating leave type', LogCategory.HR, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred while creating leave type' },
      { status: 500 }
    );
  }
}

// app/api/employees/partial/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/backend/auth/auth'
import { UserRole } from '@/types/user-roles'
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions'
import { Employee } from '@/models/Employee'
import { EmployeePartial } from '@/models/employee-partial'
import { connectToDatabase } from '@/lib/backend/database'
import mongoose from 'mongoose'
import logger, { LogCategory } from '@/lib/backend/utils/logger'

export const runtime = 'nodejs';



export async function POST(request: NextRequest) {
  logger.info('Partial employee data POST request', LogCategory.API, {
    path: request.nextUrl.pathname,
    method: request.method
  })

  try {
    // Get current user
    const user = await getCurrentUser(request)

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ])

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Connect to database
    await connectToDatabase()

    // Get request body
    const body = await request.json()
    const { data, step, employeeId } = body

    if (!data || step === undefined) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Add metadata
    const partialData = {
      ...data,
      step,
      updatedBy: user.id,
      updatedAt: new Date()
    }

    // If employeeId is provided, this is for an existing employee
    if (employeeId) {
      // Check if employee exists
      const employee = await Employee.findById(employeeId)
      if (!employee) {
        return NextResponse.json({ error: 'Employee not found' }, { status: 404 })
      }

      // Check if partial data exists for this employee
      const existingPartial = await EmployeePartial.findOne({ employeeId })

      if (existingPartial) {
        // Update existing partial data
        await EmployeePartial.findByIdAndUpdate(existingPartial._id, {
          $set: {
            [`data.step${step}`]: data,
            updatedBy: user.id,
            updatedAt: new Date()
          }
        })
      } else {
        // Create new partial data
        await EmployeePartial.create({
          employeeId,
          data: {
            [`step${step}`]: data
          },
          createdBy: user.id,
          updatedBy: user.id
        })
      }

      return NextResponse.json({
        status: 'success',
        message: 'Partial employee data saved successfully',
        step
      })
    } else {
      // This is for a new employee
      // Generate a temporary ID for the new employee
      const tempId = new mongoose.Types.ObjectId()

      // Check if partial data exists with this session ID
      const existingPartial = await EmployeePartial.findOne({
        'metadata.sessionId': user.id,
        'metadata.completed': false
      })

      if (existingPartial) {
        // Update existing partial data
        await EmployeePartial.findByIdAndUpdate(existingPartial._id, {
          $set: {
            [`data.step${step}`]: data,
            updatedBy: user.id,
            updatedAt: new Date()
          }
        })

        return NextResponse.json({
          status: 'success',
          message: 'Partial employee data updated successfully',
          tempId: existingPartial.tempId,
          step
        })
      } else {
        // Create new partial data
        const newPartial = await EmployeePartial.create({
          tempId,
          data: {
            [`step${step}`]: data
          },
          metadata: {
            sessionId: user.id,
            completed: false
          },
          createdBy: user.id,
          updatedBy: user.id
        })

        return NextResponse.json({
          status: 'success',
          message: 'Partial employee data saved successfully',
          tempId: newPartial.tempId,
          step
        })
      }
    }
  } catch (error: unknown) {
    console.error('Error saving partial employee data:', error)
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while saving partial employee data' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  logger.info('Partial employee data GET request', LogCategory.API, {
    path: request.nextUrl.pathname,
    method: request.method
  })

  try {
    // Get current user
    const user = await getCurrentUser(request)

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Connect to database
    await connectToDatabase()

    // Get query parameters
    const url = new URL(request.url)
    const employeeId = url.searchParams.get('employeeId')
    const tempId = url.searchParams.get('tempId')

    if (!employeeId && !tempId) {
      return NextResponse.json({ error: 'Missing employeeId or tempId parameter' }, { status: 400 })
    }

    let partialData

    if (employeeId) {
      // Get partial data for existing employee
      partialData = await EmployeePartial.findOne({ employeeId })
    } else {
      // Get partial data for new employee
      partialData = await EmployeePartial.findOne({
        tempId,
        'metadata.sessionId': user.id,
        'metadata.completed': false
      })
    }

    if (!partialData) {
      return NextResponse.json({ error: 'No partial data found' }, { status: 404 })
    }

    return NextResponse.json({
      status: 'success',
      data: partialData.data
    })
  } catch (error: unknown) {
    console.error('Error retrieving partial employee data:', error)
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while retrieving partial employee data' }, { status: 500 })
  }
}

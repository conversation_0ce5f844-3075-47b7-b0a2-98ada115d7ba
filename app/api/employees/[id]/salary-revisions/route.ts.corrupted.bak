// app/api/employees/[id]/salary-revisions/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
// Removed deprecated SalaryService import - using unified service
import { Employee } from '@/models/Employee';
import { accountingService } from '@/services/accounting/AccountingService';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



/**
 * POST /api/employees/[id]/salary-revisions
 * Create a salary revision for an employee
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Check if employee exists
    const employee = await Employee.findById(id);
    if (!employee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.revisionType || !body.newBasicSalary || !body.effectiveDate || !body.reason) {
      return NextResponse.json(
        { error: 'Missing required fields: revisionType, newBasicSalary, effectiveDate, reason' },
        { status: 400 }
      );
    }

    // Get current salary
    const currentSalary = await salaryService.getEmployeeSalary(id);
    if (!currentSalary) {
      return NextResponse.json(
        { error: 'No active salary found for this employee' },
        { status: 404 }
      );
    }

    // Calculate percentage change
    const percentageChange = ((body.newBasicSalary - currentSalary.basicSalary) / currentSalary.basicSalary) * 100;
    const amountChange = body.newBasicSalary - currentSalary.basicSalary;

    // Create salary revision - using a direct model approach since the method signature doesn't match
    const SalaryRevision = mongoose.models.SalaryRevision || mongoose.model('SalaryRevision', new mongoose.Schema({
      employeeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Employee', required: true },
      previousSalaryId: { type: mongoose.Schema.Types.ObjectId, ref: 'EmployeeSalary' },
      revisionType: { type: String, required: true },
      effectiveDate: { type: Date, required: true },
      previousBasicSalary: { type: Number, required: true },
      newBasicSalary: { type: Number, required: true },
      percentageChange: { type: Number },
      amountChange: { type: Number },
      reason: { type: String, required: true },
      salaryStructureId: { type: mongoose.Schema.Types.ObjectId, ref: 'SalaryStructure' },
      createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      createdAt: { type: Date, default: Date.now }
    }));

    const salaryRevision = await SalaryRevision.create({
      employeeId: new mongoose.Types.ObjectId(id),
      previousSalaryId: currentSalary._id,
      revisionType: body.revisionType,
      effectiveDate: new Date(body.effectiveDate),
      previousBasicSalary: currentSalary.basicSalary,
      newBasicSalary: body.newBasicSalary,
      percentageChange,
      amountChange,
      reason: body.reason,
      salaryStructureId: body.salaryStructureId || currentSalary.salaryStructureId,
      createdBy: user.id
    });

    // Create new salary record - using a direct model approach
    const EmployeeSalary = mongoose.models.EmployeeSalary || mongoose.model('EmployeeSalary', new mongoose.Schema({
      employeeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Employee', required: true },
      basicSalary: { type: Number, required: true },
      currency: { type: String, default: 'MWK' },
      effectiveDate: { type: Date, required: true },
      salaryStructureId: { type: mongoose.Schema.Types.ObjectId, ref: 'SalaryStructure' },
      allowances: { type: Array },
      deductions: { type: Array },
      isActive: { type: Boolean, default: true },
      createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      createdAt: { type: Date, default: Date.now }
    }));

    const newSalary = await EmployeeSalary.create({
      employeeId: new mongoose.Types.ObjectId(id),
      basicSalary: body.newBasicSalary,
      currency: currentSalary.currency,
      effectiveDate: new Date(body.effectiveDate),
      salaryStructureId: body.salaryStructureId || currentSalary.salaryStructureId,
      allowances: currentSalary.allowances,
      deductions: currentSalary.deductions,
      isActive: true,
      createdBy: user.id
    });

    // Deactivate previous salary - using a direct update since the method doesn't exist
    await mongoose.models.EmployeeSalary.findByIdAndUpdate(
      currentSalary._id,
      {
        $set: {
          isActive: false,
          deactivatedAt: new Date(body.effectiveDate),
          lastModifiedBy: user.id
        }
      }
    );

    // Create accounting entry for salary revision
    try {
      // Create accounting entry
      await accountingService.createJournalEntry({
        date: new Date(),
        reference: `SALARY-REV-${employee.employeeNumber || id}`,
        description: `Salary revision for ${employee.firstName} ${employee.lastName}`,
        entries: [
          {
            accountId: process.env.SALARY_EXPENSE_ACCOUNT_ID || '60001', // Salary Expense
            debit: 0,
            credit: 0, // This is just a memo entry, no actual money movement
            description: `Salary revision: ${body.revisionType}`,
            metadata: {
              employeeId: id,
              revisionId: salaryRevision._id,
              previousSalaryId: currentSalary._id,
              newSalaryId: newSalary._id,
              previousBasicSalary: currentSalary.basicSalary,
              newBasicSalary: body.newBasicSalary,
              percentageChange,
              amountChange,
              effectiveDate: body.effectiveDate
            }
          }
        ],
        createdBy: user.id,
        status: 'posted',
        postingDate: new Date()
      });
    } catch (error) {
      logger.error('Error creating accounting entry for salary revision', LogCategory.ACCOUNTING, error);
      // Don't fail the request if accounting entry fails
    }

    return NextResponse.json({
      success: true,
      message: 'Salary revision created successfully',
      data: {
        salaryRevision,
        newSalary
      }
    });
  } catch (error: unknown) {
    logger.error('Error creating salary revision', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

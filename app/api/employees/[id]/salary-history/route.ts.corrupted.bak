// app/api/employees/[id]/salary-history/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
// Removed deprecated SalaryService import - using unified service
import EmployeeSalary from '@/models/payroll/EmployeeSalary';
import SalaryRevision from '@/models/payroll/SalaryRevision';
import { Employee } from '@/models/Employee';

/**
 * GET /api/employees/[id]/salary-history
 * Get salary history for an employee
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Check if employee exists
    const employee = await Employee.findById(id);
    if (!employee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    // Check if user has permission to view this employee's salary history
    const isOwnSalary = id === user.id;
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER
    ]);

    if (!isOwnSalary && !hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '20');

    // Get salary history
    const salaries = await EmployeeSalary.find({ employeeId: id })
      .sort({ effectiveDate: -1 })
      .limit(limit)
      .populate('salaryStructure', 'name')
      .lean();

    // Get salary revisions
    const revisions = await SalaryRevision.find({ employeeId: id })
      .sort({ effectiveDate: -1 })
      .limit(limit)
      .lean();

    // Combine and sort by date
    const combinedHistory = [
      ...salaries.map(salary => ({
        type: 'salary',
        date: salary.effectiveDate,
        data: salary
      })),
      ...revisions.map(revision => ({
        type: 'revision',
        date: revision.effectiveDate,
        data: revision
      }))
    ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
     .slice(0, limit);

    return NextResponse.json(combinedHistory);
  } catch (error: unknown) {
    logger.error('Error getting salary history', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

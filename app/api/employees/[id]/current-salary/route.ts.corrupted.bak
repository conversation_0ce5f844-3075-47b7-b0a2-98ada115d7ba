// app/api/employees/[id]/current-salary/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
// Removed deprecated SalaryService import - using unified service
import { Employee } from '@/models/Employee';

/**
 * GET /api/employees/[id]/current-salary
 * Get current salary for an employee
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Check if employee exists
    const employee = await Employee.findById(id);
    if (!employee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    // Check if user has permission to view this employee's salary
    const isOwnSalary = id === user.id;
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER
    ]);

    if (!isOwnSalary && !hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get current salary
    const currentSalary = await salaryService.getEmployeeSalary(id);

    if (!currentSalary) {
      return NextResponse.json(
        { error: 'No active salary found for this employee' },
        { status: 404 }
      );
    }

    return NextResponse.json(currentSalary);
  } catch (error: unknown) {
    logger.error('Error getting current salary', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

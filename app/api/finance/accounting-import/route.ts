import { NextRequest, NextResponse } from 'next/server';
import { AccountingImportService } from '@/lib/backend/services/finance/AccountingImportService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { getCurrentUser } from '@/lib/backend/auth/auth';

export const runtime = 'nodejs';

// Initialize service
const accountingImportService = new AccountingImportService();
/**
 * POST handler for accounting import
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Get current user
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasImportPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR,
      UserRole.FINANCE_MANAGER
    ]);
    if (!hasImportPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    // Get request body
    const body = await request.json();
    // Validate required fields
    if (!body.sourceSystem) {
      return NextResponse.json({ error: 'Source system is required' }, { status: 400 });
    }
    if (!body.entityType) {
      return NextResponse.json({ error: 'Entity type is required' }, { status: 400 });
    }
    if (!body.fileData) {
      return NextResponse.json({ error: 'File data is required' }, { status: 400 });
    }
    // Get file buffer
    const fileBuffer = Buffer.from(body.fileData, 'base64');
    // Process import
    const importResult = await accountingImportService.importData(fileBuffer, {
      sourceSystem: body.sourceSystem,
      entityType: body.entityType,
      validateOnly: body.validateOnly,
      updateExisting: body.updateExisting,
      fieldMapping: body.fieldMapping,
      userId: user.id,
      dateFormat: body.dateFormat,
      startDate: body.startDate ? new Date(body.startDate) : undefined,
      endDate: body.endDate ? new Date(body.endDate) : undefined,
      fiscalYear: body.fiscalYear,
      fiscalPeriod: body.fiscalPeriod
    });
    return NextResponse.json(importResult);
  } catch (error: unknown) {
    logger.error('Error in accounting import handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}
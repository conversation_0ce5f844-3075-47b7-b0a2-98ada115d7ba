import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { contractService } from '@/lib/backend/services/procurement/ContractService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

// Validation schema for contract termination
const terminateContractSchema = z.object({
  reason: z.string().min(10, 'Termination reason must be at least 10 characters').max(500, 'Reason too long'),
  effectiveDate: z.string().transform(val => new Date(val)).optional(),
  notifySupplier: z.boolean().default(true),
  penaltyApplied: z.boolean().default(false),
  penaltyAmount: z.number().min(0).optional(),
  penaltyReason: z.string().optional()
});

// Response type for successful termination operations
interface TerminationResponse {
  success: true;
  data: {
    contract: unknown;
    termination: {
      reason: string;
      effectiveDate?: Date;
      terminatedBy: {
        id: string;
        name: string;
        email: string;
      };
      terminationDate: Date;
      notifySupplier: boolean;
      penaltyApplied: boolean;
      penaltyAmount?: number;
      penaltyReason?: string;
    };
  };
  message?: string;
}

/**
 * POST /api/procurement/contracts/[id]/terminate
 * Terminate a specific contract
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<TerminationResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'CONTRACT_TERMINATION_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to terminate contracts.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions - termination requires manager-level approval
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.FINANCE_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'CONTRACT_TERMINATION_FORBIDDEN',
        'Insufficient permissions to terminate contracts',
        'You do not have permission to terminate contract records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.HIGH,
        'User attempted to terminate contract without proper permissions',
        [
          'Contact your administrator to request contract termination permissions',
          'Ensure you are logged in with a manager-level account',
          'Verify you have procurement, finance, or department head access'
        ]
      );
    }

    // Get contract ID from params
    const { id } = await params;

    // Validate contract ID
    if (!id || id.length !== 24) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CONTRACT_TERMINATION_INVALID_ID',
        'Invalid contract ID',
        'The contract ID provided is not valid. Please check the ID and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          contractId: id
        },
        400,
        ErrorSeverity.LOW,
        'Contract ID must be a valid 24-character MongoDB ObjectId',
        [
          'Ensure the contract ID is copied correctly',
          'Check that the URL is complete and correct'
        ]
      );
    }

    // Parse request body
    let body: unknown;
    try {
      body = await request.json();
    } catch (parseError: unknown) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CONTRACT_TERMINATION_INVALID_JSON',
        'Invalid request format',
        'The request data is not in the correct format. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          parseError: parseError instanceof Error ? parseError.message : 'Unknown parse error'
        },
        400,
        ErrorSeverity.MEDIUM,
        'Failed to parse JSON request body',
        [
          'Ensure the request body is valid JSON',
          'Check that all required fields are included',
          'Verify date formats are correct (YYYY-MM-DD)'
        ]
      );
    }

    // Validate request data
    const validationResult = terminateContractSchema.safeParse(body);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CONTRACT_TERMINATION_VALIDATION_ERROR',
        'Invalid contract termination data',
        'The contract termination information provided is not valid. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.MEDIUM,
        validationResult.error.errors[0].message,
        [
          'Ensure the termination reason is at least 10 characters',
          'Check that penalty information is complete if penalty is applied',
          'Verify date formats are correct',
          'Ensure all required fields are provided'
        ]
      );
    }

    const {
      reason,
      effectiveDate,
      notifySupplier,
      penaltyApplied,
      penaltyAmount,
      penaltyReason
    } = validationResult.data;

    // Validate penalty information if penalty is applied
    if (penaltyApplied && (!penaltyAmount || !penaltyReason)) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'CONTRACT_TERMINATION_PENALTY_INCOMPLETE',
        'Incomplete penalty information',
        'When applying a penalty, you must provide both the penalty amount and reason.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          penaltyApplied,
          penaltyAmount,
          penaltyReason
        },
        400,
        ErrorSeverity.LOW,
        'Penalty amount and reason are required when penalty is applied',
        [
          'Provide a penalty amount greater than zero',
          'Include a clear reason for the penalty',
          'Reference specific contract terms if applicable'
        ]
      );
    }

    // Check if contract exists
    const existingContract = await contractService.findById(id);
    if (!existingContract) {
      return errorService.createApiResponse(
        ErrorType.NOT_FOUND,
        'CONTRACT_TERMINATION_NOT_FOUND',
        'Contract not found',
        'The contract you are trying to terminate could not be found.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          contractId: id
        },
        404,
        ErrorSeverity.MEDIUM,
        `Contract with ID ${id} not found in database`,
        [
          'Verify the contract ID is correct',
          'Check if the contract was recently deleted',
          'Contact support if you believe this is an error'
        ]
      );
    }

    // Validate contract can be terminated
    if (!['active', 'suspended'].includes(existingContract.status)) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'CONTRACT_TERMINATION_INVALID_STATUS',
        'Contract cannot be terminated',
        'This contract cannot be terminated due to its current status.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          contractId: id,
          currentStatus: existingContract.status,
          allowedStatuses: ['active', 'suspended']
        },
        400,
        ErrorSeverity.MEDIUM,
        `Contract with status '${existingContract.status}' cannot be terminated`,
        [
          'Only active or suspended contracts can be terminated',
          'Check the contract status',
          'Contact procurement team if termination is needed',
          'Consider other actions based on the contract status'
        ]
      );
    }

    // Additional validation for high-value contracts
    if (existingContract.value > 1000000) {
      const hasHighLevelPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.FINANCE_MANAGER
      ]);

      if (!hasHighLevelPermission) {
        return errorService.createApiResponse(
          ErrorType.FORBIDDEN,
          'CONTRACT_TERMINATION_HIGH_VALUE_FORBIDDEN',
          'Insufficient permissions for high-value contract termination',
          'Termination of contracts over $1,000,000 requires manager-level approval.',
          {
            userId: user.id,
            userRole: user.role,
            endpoint: request.nextUrl.pathname,
            method: request.method,
            contractId: id,
            contractValue: existingContract.value,
            valueThreshold: 1000000
          },
          403,
          ErrorSeverity.HIGH,
          'User attempted high-value contract termination without manager permissions',
          [
            'Contact your procurement or finance manager for approval',
            'Ensure you have manager-level permissions',
            'Submit a termination request through proper channels'
          ]
        );
      }
    }

    // Build comprehensive termination reason
    let fullReason = reason;
    
    if (effectiveDate) {
      fullReason += `\nEffective Date: ${effectiveDate.toISOString().split('T')[0]}`;
    }
    
    if (penaltyApplied && penaltyAmount && penaltyReason) {
      fullReason += `\nPenalty Applied: ${existingContract.currency} ${penaltyAmount.toLocaleString()}`;
      fullReason += `\nPenalty Reason: ${penaltyReason}`;
    }
    
    if (notifySupplier) {
      fullReason += '\nSupplier notification: Required';
    }
    
    fullReason += `\nTerminated by: ${user.firstName} ${user.lastName} (${user.email})`;
    fullReason += `\nTermination Date: ${new Date().toISOString()}`;

    // Terminate contract
    const terminatedContract = await contractService.terminateContract(id, fullReason, user.id);

    // Log termination for audit trail
    logger.info('Contract terminated', LogCategory.PROCUREMENT, {
      contractId: id,
      contractNumber: terminatedContract.contractNumber,
      terminatedBy: user.id,
      reason: reason,
      penaltyApplied,
      penaltyAmount,
      notifySupplier
    });

    // Prepare response data
    const responseData = {
      contract: terminatedContract,
      termination: {
        reason,
        effectiveDate,
        terminatedBy: {
          id: user.id,
          name: `${user.firstName} ${user.lastName}`,
          email: user.email
        },
        terminationDate: new Date(),
        notifySupplier,
        penaltyApplied,
        penaltyAmount,
        penaltyReason
      }
    };

    return NextResponse.json({
      success: true,
      message: 'Contract terminated successfully',
      data: responseData
    } as TerminationResponse);

  } catch (error: unknown) {
    logger.error('Error terminating contract', LogCategory.PROCUREMENT, error);

    // Handle specific business logic errors
    if (error instanceof Error) {
      if (error.message.includes('cannot be terminated')) {
        return errorService.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'CONTRACT_TERMINATION_BUSINESS_ERROR',
          'Contract termination not allowed',
          'This contract cannot be terminated due to business rules.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          400,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Check the contract status and conditions',
            'Verify the contract is eligible for termination',
            'Contact procurement team for assistance'
          ]
        );
      }
    }

    // Generic server error
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'CONTRACT_TERMINATION_ERROR',
      error instanceof Error ? error.message : 'Failed to terminate contract',
      'Unable to terminate the contract. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try terminating the contract again',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

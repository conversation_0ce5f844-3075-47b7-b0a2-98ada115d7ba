import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { contractService, UpdateContractData } from '@/lib/backend/services/procurement/ContractService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

// Validation schema for contract updates
const updateContractSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long').optional(),
  description: z.string().min(1, 'Description is required').max(1000, 'Description too long').optional(),
  contractType: z.enum(['service', 'supply', 'maintenance', 'lease', 'consulting', 'construction']).optional(),
  value: z.number().min(0, 'Value must be positive').optional(),
  currency: z.string().length(3, 'Currency must be 3 characters').optional(),
  startDate: z.string().transform(val => new Date(val)).optional(),
  endDate: z.string().transform(val => new Date(val)).optional(),
  autoRenewal: z.boolean().optional(),
  renewalTerms: z.string().optional(),
  terms: z.array(z.string()).optional(),
  paymentTerms: z.string().min(1, 'Payment terms are required').optional(),
  deliveryTerms: z.string().optional(),
  penaltyClause: z.string().optional(),
  warrantyTerms: z.string().optional(),
  performanceMetrics: z.array(z.object({
    metric: z.string(),
    target: z.string(),
    measurement: z.string(),
    frequency: z.enum(['daily', 'weekly', 'monthly', 'quarterly', 'annually'])
  })).optional(),
  budgetCategory: z.string().optional(),
  costCenter: z.string().optional(),
  taxRate: z.number().min(0).max(100).optional(),
  discountRate: z.number().min(0).max(100).optional(),
  complianceRequirements: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  priority: z.enum(['low', 'medium', 'high', 'critical']).optional(),
  riskLevel: z.enum(['low', 'medium', 'high']).optional(),
  notes: z.string().optional(),
  status: z.enum(['draft', 'active', 'expired', 'terminated', 'renewed', 'suspended', 'pending_approval']).optional(),
  renewalDate: z.string().transform(val => val ? new Date(val) : undefined).optional(),
  approvedBy: z.string().optional(),
  approvalDate: z.string().transform(val => val ? new Date(val) : undefined).optional(),
  reviewDate: z.string().transform(val => val ? new Date(val) : undefined).optional()
});

// Response type for successful contract operations
interface ContractResponse {
  success: true;
  data: unknown;
  message?: string;
}

/**
 * GET /api/procurement/contracts/[id]
 * Get a specific contract by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ContractResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'CONTRACT_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to view contract details.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'CONTRACT_VIEW_FORBIDDEN',
        'Insufficient permissions to view contract details',
        'You do not have permission to view contract information.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to view contract without proper permissions',
        [
          'Contact your administrator to request contract access permissions',
          'Ensure you are logged in with the correct account'
        ]
      );
    }

    // Get contract ID from params
    const { id } = await params;

    // Validate contract ID
    if (!id || id.length !== 24) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CONTRACT_INVALID_ID',
        'Invalid contract ID',
        'The contract ID provided is not valid. Please check the ID and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          contractId: id
        },
        400,
        ErrorSeverity.LOW,
        'Contract ID must be a valid 24-character MongoDB ObjectId',
        [
          'Ensure the contract ID is copied correctly',
          'Check that the URL is complete and correct'
        ]
      );
    }

    // Get contract
    const contract = await contractService.findById(id);
    if (!contract) {
      return errorService.createApiResponse(
        ErrorType.NOT_FOUND,
        'CONTRACT_NOT_FOUND',
        'Contract not found',
        'The requested contract could not be found. It may have been deleted or the ID is incorrect.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          contractId: id
        },
        404,
        ErrorSeverity.MEDIUM,
        `Contract with ID ${id} not found in database`,
        [
          'Verify the contract ID is correct',
          'Check if the contract was recently deleted',
          'Contact support if you believe this is an error'
        ]
      );
    }

    return NextResponse.json({
      success: true,
      data: contract
    } as ContractResponse);

  } catch (error: unknown) {
    logger.error('Error getting contract', LogCategory.PROCUREMENT, error);

    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'CONTRACT_FETCH_ERROR',
      error instanceof Error ? error.message : 'Failed to fetch contract',
      'Unable to retrieve contract information. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try refreshing the page',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

/**
 * PUT /api/procurement/contracts/[id]
 * Update a specific contract
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ContractResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'CONTRACT_UPDATE_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to update contracts.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'CONTRACT_UPDATE_FORBIDDEN',
        'Insufficient permissions to update contracts',
        'You do not have permission to update contract records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to update contract without proper permissions',
        [
          'Contact your administrator to request contract update permissions',
          'Ensure you are logged in with the correct account'
        ]
      );
    }

    // Get contract ID from params
    const { id } = await params;

    // Validate contract ID
    if (!id || id.length !== 24) {
      return NextResponse.json(
        { error: 'Invalid contract ID' },
        { status: 400 }
      );
    }

    // Parse request body
    const body = await request.json();

    // Validate request data
    const validationResult = updateContractSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    const updateData: UpdateContractData = {
      ...validationResult.data,
      lastModifiedBy: user.id
    };

    // Validate date range if both dates are provided
    if (updateData.startDate && updateData.endDate && updateData.startDate >= updateData.endDate) {
      return NextResponse.json(
        { error: 'Start date must be before end date' },
        { status: 400 }
      );
    }

    // Update contract
    const contract = await contractService.updateContract(id, updateData);

    return NextResponse.json({
      success: true,
      message: 'Contract updated successfully',
      data: contract
    });

  } catch (error: unknown) {
    logger.error('Error updating contract', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/procurement/contracts/[id]
 * Delete a specific contract
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only high-level users can delete contracts
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to delete contracts' },
        { status: 403 }
      );
    }

    // Get contract ID from params
    const { id } = await params;

    // Validate contract ID
    if (!id || id.length !== 24) {
      return NextResponse.json(
        { error: 'Invalid contract ID' },
        { status: 400 }
      );
    }

    // Check if contract exists
    const existingContract = await contractService.findById(id);
    if (!existingContract) {
      return NextResponse.json(
        { error: 'Contract not found' },
        { status: 404 }
      );
    }

    // Check if contract can be deleted (only draft contracts)
    if (existingContract.status !== 'draft') {
      return NextResponse.json(
        { error: 'Only draft contracts can be deleted. Use termination for active contracts.' },
        { status: 400 }
      );
    }

    // Delete contract
    await contractService.delete(id);

    return NextResponse.json({
      success: true,
      message: 'Contract deleted successfully'
    });

  } catch (error: unknown) {
    logger.error('Error deleting contract', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { contractService, ContractRenewalData } from '@/lib/backend/services/procurement/ContractService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

export const runtime = 'nodejs';

// Validation schema for contract renewal
const renewContractSchema = z.object({
  newEndDate: z.string().transform(val => new Date(val)),
  renewalTerms: z.string().optional(),
  valueAdjustment: z.number().optional(),
  updatedTerms: z.array(z.string()).optional(),
  notes: z.string().optional()
});
// Response type for successful renewal operations
interface RenewalResponse {
  success: true;
  data: unknown;
  message?: string;
}
/**
 * POST /api/procurement/contracts/[id]/renew
 * Renew a specific contract
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<RenewalResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'CONTRACT_RENEWAL_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to renew contracts.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER
    ]);
    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'CONTRACT_RENEWAL_FORBIDDEN',
        'Insufficient permissions to renew contracts',
        'You do not have permission to renew contract records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to renew contract without proper permissions',
        [
          'Contact your administrator to request contract renewal permissions',
          'Ensure you are logged in with the correct account',
          'Verify you have procurement or finance manager access'
        ]
      );
    }
    // Get contract ID from params
    const { id } = await params;
    // Validate contract ID
    if (!id || id.length !== 24) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CONTRACT_RENEWAL_INVALID_ID',
        'Invalid contract ID',
        'The contract ID provided is not valid. Please check the ID and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          contractId: id
        },
        400,
        ErrorSeverity.LOW,
        'Contract ID must be a valid 24-character MongoDB ObjectId',
        [
          'Ensure the contract ID is copied correctly',
          'Check that the URL is complete and correct'
        ]
      );
    }
    // Parse request body
    let body: unknown;
    try {
      body = await request.json();
    } catch (parseError: unknown) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CONTRACT_RENEWAL_INVALID_JSON',
        'Invalid request format',
        'The request data is not in the correct format. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          parseError: parseError instanceof Error ? parseError.message : 'Unknown parse error'
        },
        400,
        ErrorSeverity.MEDIUM,
        'Failed to parse JSON request body',
        [
          'Ensure the request body is valid JSON',
          'Check that all required fields are included',
          'Verify date formats are correct (YYYY-MM-DD)'
        ]
      );
    }
    // Validate request data
    const validationResult = renewContractSchema.safeParse(body);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CONTRACT_RENEWAL_VALIDATION_ERROR',
        'Invalid contract renewal data',
        'The contract renewal information provided is not valid. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.MEDIUM,
        validationResult.error.errors[0].message,
        [
          'Ensure all required fields are filled',
          'Check that the new end date is in the future',
          'Verify numeric values are valid',
          'Ensure date formats are correct'
        ]
      );
    }
    const renewalData: ContractRenewalData = {
      ...validationResult.data,
      renewedBy: user.id
    };
    // Validate new end date is in the future
    const today = new Date();
    if (renewalData.newEndDate <= today) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'CONTRACT_RENEWAL_INVALID_DATE',
        'Invalid renewal end date',
        'The new end date must be in the future.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          newEndDate: renewalData.newEndDate.toISOString(),
          currentDate: today.toISOString()
        },
        400,
        ErrorSeverity.LOW,
        'New end date is not in the future',
        [
          'Set the new end date to a future date',
          'Verify the date format is correct',
          'Check your system clock if needed'
        ]
      );
    }
    // Check if contract exists and can be renewed
    const existingContract = await contractService.findById(id);
    if (!existingContract) {
      return errorService.createApiResponse(
        ErrorType.NOT_FOUND,
        'CONTRACT_RENEWAL_NOT_FOUND',
        'Contract not found',
        'The contract you are trying to renew could not be found.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          contractId: id
        },
        404,
        ErrorSeverity.MEDIUM,
        `Contract with ID ${id} not found in database`,
        [
          'Verify the contract ID is correct',
          'Check if the contract was recently deleted',
          'Contact support if you believe this is an error'
        ]
      );
    }
    // Validate contract can be renewed
    if (!existingContract.canRenew()) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'CONTRACT_RENEWAL_NOT_ALLOWED',
        'Contract cannot be renewed',
        'This contract cannot be renewed at this time. Only active or expiring contracts can be renewed.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          contractId: id,
          currentStatus: existingContract.status,
          isExpired: existingContract.isExpired(),
          isExpiringSoon: existingContract.isExpiringSoon(90)
        },
        400,
        ErrorSeverity.MEDIUM,
        `Contract with status '${existingContract.status}' cannot be renewed`,
        [
          'Ensure the contract is active or expiring soon',
          'Check the contract status and expiry date',
          'Contact procurement team if renewal is needed',
          'Consider creating a new contract instead'
        ]
      );
    }
    // Additional validation for high-value renewals
    if (renewalData.valueAdjustment && Math.abs(renewalData.valueAdjustment) > existingContract.value * 0.2) {
      // Require additional approval for value adjustments > 20%
      const hasHighLevelPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.FINANCE_MANAGER
      ]);
      if (!hasHighLevelPermission) {
        return errorService.createApiResponse(
          ErrorType.FORBIDDEN,
          'CONTRACT_RENEWAL_HIGH_VALUE_FORBIDDEN',
          'Insufficient permissions for high-value adjustments',
          'Value adjustments greater than 20% require manager-level approval.',
          {
            userId: user.id,
            userRole: user.role,
            endpoint: request.nextUrl.pathname,
            method: request.method,
            contractId: id,
            currentValue: existingContract.value,
            valueAdjustment: renewalData.valueAdjustment,
            adjustmentPercentage: Math.abs(renewalData.valueAdjustment) / existingContract.value * 100
          },
          403,
          ErrorSeverity.HIGH,
          'User attempted high-value contract renewal without manager permissions',
          [
            'Contact your procurement or finance manager for approval',
            'Reduce the value adjustment to less than 20%',
            'Submit a separate approval request for the value change'
          ]
        );
      }
    }
    // Renew contract
    const renewedContract = await contractService.renewContract(id, renewalData);
    return NextResponse.json({
      success: true,
      message: 'Contract renewed successfully',
      data: renewedContract
    } as RenewalResponse);
  } catch (error: unknown) {
    logger.error('Error renewing contract', LogCategory.PROCUREMENT, error);
    // Handle specific business logic errors
    if (error instanceof Error) {
      if (error.message.includes('cannot be renewed')) {
        return errorService.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'CONTRACT_RENEWAL_BUSINESS_ERROR',
          'Contract renewal not allowed',
          'This contract cannot be renewed due to business rules.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          400,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Check the contract status and conditions',
            'Verify the contract is eligible for renewal',
            'Contact procurement team for assistance'
          ]
        );
      }
    }
    // Generic server error
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'CONTRACT_RENEWAL_ERROR',
      error instanceof Error ? error.message : 'Failed to renew contract',
      'Unable to renew the contract. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try renewing the contract again',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}
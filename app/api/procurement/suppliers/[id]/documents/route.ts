import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

// Document schema for supplier documents
const SupplierDocumentSchema = new mongoose.Schema({
  supplierId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Supplier',
    required: true,
    index: true
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    trim: true,
    maxlength: 1000
  },
  documentType: {
    type: String,
    enum: [
      'contract',
      'certificate',
      'license',
      'insurance',
      'tax_document',
      'bank_statement',
      'compliance_document',
      'quality_certificate',
      'other'
    ],
    required: true,
    index: true
  },
  fileName: {
    type: String,
    required: true
  },
  fileType: {
    type: String,
    required: true
  },
  fileSize: {
    type: Number,
    required: true
  },
  filePath: {
    type: String,
    required: true
  },
  issuedDate: Date,
  expiryDate: Date,
  isExpired: {
    type: Boolean,
    default: false
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  verifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  verifiedAt: Date,
  verificationNotes: String,
  tags: [String],
  isActive: {
    type: Boolean,
    default: true
  },
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});
// Create indexes
SupplierDocumentSchema.index({ supplierId: 1, documentType: 1 });
SupplierDocumentSchema.index({ expiryDate: 1 });
SupplierDocumentSchema.index({ isExpired: 1, isActive: 1 });
const SupplierDocument = mongoose.models.SupplierDocument || 
  mongoose.model('SupplierDocument', SupplierDocumentSchema);
/**
 * GET /api/procurement/suppliers/[id]/documents
 * Get all documents for a specific supplier
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'SUPPLIER_DOCUMENTS_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to view supplier documents.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER,
      UserRole.DEPARTMENT_HEAD
    ]);
    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'SUPPLIER_DOCUMENTS_FORBIDDEN',
        'Insufficient permissions to view supplier documents',
        'You do not have permission to view supplier documents.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }
    // Connect to database
    await connectToDatabase();
    // Resolve params
    const { id } = await params;
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const documentType = searchParams.get('documentType');
    const isExpired = searchParams.get('isExpired');
    // Build filters
    const filters: any = { 
      supplierId: id,
      isActive: true
    };
    if (documentType) {
      filters.documentType = documentType;
    }
    if (isExpired !== null) {
      filters.isExpired = isExpired === 'true';
    }
    // Calculate pagination
    const skip = (page - 1) * limit;
    // Get documents
    const [documents, total] = await Promise.all([
      SupplierDocument.find(filters)
        .populate('uploadedBy', 'firstName lastName email')
        .populate('verifiedBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      SupplierDocument.countDocuments(filters)
    ]);
    const pages = Math.ceil(total / limit);
    return NextResponse.json({
      success: true,
      data: documents,
      pagination: {
        page,
        limit,
        total,
        pages
      }
    });
  } catch (error) {
    logger.error('Error getting supplier documents', LogCategory.PROCUREMENT, error);
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'SUPPLIER_DOCUMENTS_ERROR',
      error instanceof Error ? error.message : 'Failed to get supplier documents',
      'Unable to retrieve supplier documents.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}
/**
 * POST /api/procurement/suppliers/[id]/documents
 * Upload a new document for a supplier
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'SUPPLIER_DOCUMENT_UPLOAD_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to upload supplier documents.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);
    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'SUPPLIER_DOCUMENT_UPLOAD_FORBIDDEN',
        'Insufficient permissions to upload supplier documents',
        'You do not have permission to upload supplier documents.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }
    // Connect to database
    await connectToDatabase();
    // Resolve params
    const { id } = await params;
    // Parse form data (for file upload)
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const title = formData.get('title') as string;
    const description = formData.get('description') as string;
    const documentType = formData.get('documentType') as string;
    const issuedDate = formData.get('issuedDate') as string;
    const expiryDate = formData.get('expiryDate') as string;
    const tags = formData.get('tags') as string;
    if (!file || !title || !documentType) {
      return NextResponse.json(
        { error: 'Missing required fields: file, title, documentType' },
        { status: 400 }
      );
    }
    // TODO: Implement file upload to storage (AWS S3, local storage, etc.)
    // For now, we'll create a placeholder path
    const filePath = `/uploads/suppliers/${id}/${Date.now()}-${file.name}`;
    // Create document record
    const document = new SupplierDocument({
      supplierId: id,
      title,
      description,
      documentType,
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
      filePath,
      issuedDate: issuedDate ? new Date(issuedDate) : undefined,
      expiryDate: expiryDate ? new Date(expiryDate) : undefined,
      isExpired: expiryDate ? new Date(expiryDate) < new Date() : false,
      tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
      uploadedBy: user.id
    });
    await document.save();
    logger.info('Supplier document uploaded', LogCategory.PROCUREMENT, {
      supplierId: id,
      documentId: document._id,
      documentType,
      uploadedBy: user.id
    });
    return NextResponse.json({
      success: true,
      data: document,
      message: 'Document uploaded successfully'
    });
  } catch (error) {
    logger.error('Error uploading supplier document', LogCategory.PROCUREMENT, error);
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'SUPPLIER_DOCUMENT_UPLOAD_ERROR',
      error instanceof Error ? error.message : 'Failed to upload supplier document',
      'Unable to upload supplier document.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}
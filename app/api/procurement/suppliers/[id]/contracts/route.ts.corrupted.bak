import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { ContractService } from '@/lib/backend/services/procurement/ContractService';

// Initialize service
const contractService = new ContractService();
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/procurement/suppliers/[id]/contracts
 * Get all contracts for a specific supplier
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'SUPPLIER_CONTRACTS_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to view supplier contracts.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'SUPPLIER_CONTRACTS_FORBIDDEN',
        'Insufficient permissions to view supplier contracts',
        'You do not have permission to view supplier contracts.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Resolve params
    const { id } = await params;

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const contractType = searchParams.get('contractType');

    // Build filters
    const filters: any = { supplierId: id };
    
    if (status) {
      filters.status = status;
    }
    
    if (contractType) {
      filters.contractType = contractType;
    }

    // Get contracts for supplier
    const contracts = await contractService.paginate(
      filters,
      page,
      limit,
      { startDate: -1 }, // Sort by start date descending
      ['supplierId', 'budgetCategory', 'costCenter']
    );

    return NextResponse.json({
      success: true,
      data: contracts.data,
      pagination: {
        page: contracts.page,
        limit: contracts.limit,
        total: contracts.total,
        pages: contracts.pages
      }
    });

  } catch (error) {
    logger.error('Error getting supplier contracts', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'SUPPLIER_CONTRACTS_ERROR',
      error instanceof Error ? error.message : 'Failed to get supplier contracts',
      'Unable to retrieve supplier contracts.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method
      },
      500,
      ErrorSeverity.HIGH
    );
  }
}

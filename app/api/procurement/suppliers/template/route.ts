import { NextRequest, NextResponse } from 'next/server';
import * as XLSX from 'xlsx';

export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    // Sample data for the template
    const sampleData = [
      {
        name: 'Office Supplies Ltd',
        contactPerson: '<PERSON>',
        email: '<EMAIL>',
        phone: '+265 1 234 567',
        address: '123 Business Street',
        city: 'Lilongwe',
        country: 'Malawi',
        website: 'https://officesupplies.mw',
        category: 'office-supplies,stationery',
        taxId: 'TAX123456',
        paymentTerms: 'Net 30',
        bankName: 'Standard Bank',
        accountNumber: '**********',
        status: 'active',
        rating: '4',
        notes: 'Reliable supplier for office materials'
      },
      {
        name: 'Tech Solutions Inc',
        contactPerson: '<PERSON>',
        email: '<EMAIL>',
        phone: '+265 1 345 678',
        address: '456 Technology Avenue',
        city: 'Blantyre',
        country: 'Malawi',
        website: 'https://techsolutions.mw',
        category: 'it-equipment,electronics',
        taxId: 'TAX789012',
        paymentTerms: 'Net 15',
        bankName: 'National Bank',
        accountNumber: '**********',
        status: 'active',
        rating: '5',
        notes: 'Excellent IT equipment supplier'
      },
      {
        name: 'Construction Materials Co',
        contactPerson: 'Bob Johnson',
        email: '<EMAIL>',
        phone: '+265 1 456 789',
        address: '789 Industrial Road',
        city: 'Mzuzu',
        country: 'Malawi',
        website: '',
        category: 'construction,maintenance',
        taxId: 'TAX345678',
        paymentTerms: 'COD',
        bankName: 'FDH Bank',
        accountNumber: '**********',
        status: 'active',
        rating: '3',
        notes: 'Good for construction materials'
      }
    ];
    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(sampleData);
    // Set column widths for better readability
    const columnWidths = [
      { wch: 25 }, // name
      { wch: 20 }, // contactPerson
      { wch: 25 }, // email
      { wch: 15 }, // phone
      { wch: 30 }, // address
      { wch: 15 }, // city
      { wch: 10 }, // country
      { wch: 25 }, // website
      { wch: 25 }, // category
      { wch: 15 }, // taxId
      { wch: 15 }, // paymentTerms
      { wch: 20 }, // bankName
      { wch: 15 }, // accountNumber
      { wch: 10 }, // status
      { wch: 8 },  // rating
      { wch: 30 }  // notes
    ];
    worksheet['!cols'] = columnWidths;
    // Add the worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Suppliers');
    // Create instructions sheet
    const instructions = [
      {
        Field: 'name',
        Description: 'Supplier name (required)',
        Example: 'Office Supplies Ltd',
        Notes: 'Must be unique'
      },
      {
        Field: 'contactPerson',
        Description: 'Contact person name',
        Example: 'John Smith',
        Notes: 'Optional'
      },
      {
        Field: 'email',
        Description: 'Contact email address',
        Example: '<EMAIL>',
        Notes: 'Must be valid email format'
      },
      {
        Field: 'phone',
        Description: 'Contact phone number',
        Example: '+265 1 234 567',
        Notes: 'Include country code'
      },
      {
        Field: 'address',
        Description: 'Full address',
        Example: '123 Business Street',
        Notes: 'Street address'
      },
      {
        Field: 'city',
        Description: 'City name',
        Example: 'Lilongwe',
        Notes: 'City where supplier is located'
      },
      {
        Field: 'country',
        Description: 'Country name',
        Example: 'Malawi',
        Notes: 'Default: Malawi'
      },
      {
        Field: 'website',
        Description: 'Website URL',
        Example: 'https://example.com',
        Notes: 'Optional, must be valid URL'
      },
      {
        Field: 'category',
        Description: 'Categories (comma-separated)',
        Example: 'office-supplies,stationery',
        Notes: 'At least one category required'
      },
      {
        Field: 'taxId',
        Description: 'Tax identification number',
        Example: 'TAX123456',
        Notes: 'Optional'
      },
      {
        Field: 'paymentTerms',
        Description: 'Payment terms',
        Example: 'Net 30, COD',
        Notes: 'Optional'
      },
      {
        Field: 'bankName',
        Description: 'Bank name',
        Example: 'Standard Bank',
        Notes: 'Optional'
      },
      {
        Field: 'accountNumber',
        Description: 'Bank account number',
        Example: '**********',
        Notes: 'Optional'
      },
      {
        Field: 'status',
        Description: 'Supplier status',
        Example: 'active, inactive, blacklisted',
        Notes: 'Default: active'
      },
      {
        Field: 'rating',
        Description: 'Supplier rating (1-5)',
        Example: '4',
        Notes: 'Optional, must be 1-5'
      },
      {
        Field: 'notes',
        Description: 'Additional notes',
        Example: 'Reliable supplier',
        Notes: 'Optional'
      }
    ];
    const instructionsSheet = XLSX.utils.json_to_sheet(instructions);
    instructionsSheet['!cols'] = [
      { wch: 15 }, // Field
      { wch: 30 }, // Description
      { wch: 25 }, // Example
      { wch: 25 }  // Notes
    ];
    XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Instructions');
    // Generate Excel file buffer
    const excelBuffer = XLSX.write(workbook, { 
      type: 'buffer', 
      bookType: 'xlsx' 
    });
    // Set response headers
    const headers = new Headers();
    headers.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    headers.set('Content-Disposition', 'attachment; filename="supplier_import_template.xlsx"');
    headers.set('Content-Length', excelBuffer.length.toString());
    return new NextResponse(excelBuffer, {
      status: 200,
      headers
    });
  } catch (error) {
    console.error('Error generating supplier template:', error);
    return NextResponse.json(
      { error: 'Failed to generate template' },
      { status: 500 }
    );
  }
}
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { SupplierService } from '@/lib/backend/services/procurement/SupplierService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import * as XLSX from 'xlsx';

const supplierService = new SupplierService();

interface SupplierImportRow {
  name: string;
  contactPerson?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  website?: string;
  category: string;
  taxId?: string;
  paymentTerms?: string;
  bankName?: string;
  accountNumber?: string;
  status?: string;
  rating?: string;
  notes?: string;
}

interface ImportResult {
  totalRows: number;
  successfulSuppliers: Array<{
    row: number;
    supplierName: string;
    supplierId: string;
    category: string[];
    status: string;
  }>;
  failedSuppliers: Array<{
    row: number;
    supplierName: string;
    error: string;
    data: any;
  }>;
  duplicateSuppliers: Array<{
    row: number;
    supplierName: string;
    existingSupplierId: string;
    action: string;
  }>;
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'SUPPLIER_IMPORT_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to import suppliers.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'SUPPLIER_IMPORT_FORBIDDEN',
        'Insufficient permissions to import suppliers',
        'You do not have permission to import supplier data.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    const fileName = file.name.toLowerCase();
    if (!fileName.endsWith('.csv') && !fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload a CSV or Excel file.' },
        { status: 400 }
      );
    }

    // Read file content
    const buffer = await file.arrayBuffer();
    let data: SupplierImportRow[];

    try {
      if (fileName.endsWith('.csv')) {
        const text = new TextDecoder().decode(buffer);
        const lines = text.split('\n').filter(line => line.trim());
        const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
        
        data = lines.slice(1).map(line => {
          const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
          const row: any = {};
          headers.forEach((header, index) => {
            row[header] = values[index] || '';
          });
          return row;
        });
      } else {
        const workbook = XLSX.read(buffer, { type: 'buffer' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        data = XLSX.utils.sheet_to_json(worksheet);
      }
    } catch (parseError) {
      logger.error('Error parsing file', LogCategory.PROCUREMENT, parseError);
      return NextResponse.json(
        { error: 'Failed to parse file. Please check the file format.' },
        { status: 400 }
      );
    }

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: 'No data found in file' },
        { status: 400 }
      );
    }

    // Process suppliers
    const result: ImportResult = {
      totalRows: data.length,
      successfulSuppliers: [],
      failedSuppliers: [],
      duplicateSuppliers: []
    };

    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const rowNumber = i + 2; // +2 because Excel rows start at 1 and we skip header

      try {
        // Validate required fields
        if (!row.name || !row.name.trim()) {
          result.failedSuppliers.push({
            row: rowNumber,
            supplierName: row.name || 'Unknown',
            error: 'Supplier name is required',
            data: row
          });
          continue;
        }

        if (!row.category || !row.category.trim()) {
          result.failedSuppliers.push({
            row: rowNumber,
            supplierName: row.name,
            error: 'Category is required',
            data: row
          });
          continue;
        }

        // Check for duplicates by name
        const existingSupplier = await supplierService.findOne({ name: row.name.trim() });
        if (existingSupplier) {
          result.duplicateSuppliers.push({
            row: rowNumber,
            supplierName: row.name,
            existingSupplierId: existingSupplier.supplierId,
            action: 'Skipped'
          });
          continue;
        }

        // Parse categories (comma-separated)
        const categories = row.category.split(',').map((cat: string) => cat.trim()).filter(Boolean);

        // Parse rating
        let rating: number | undefined;
        if (row.rating) {
          const ratingNum = parseFloat(row.rating);
          if (!isNaN(ratingNum) && ratingNum >= 1 && ratingNum <= 5) {
            rating = ratingNum;
          }
        }

        // Create supplier data
        const supplierData = {
          name: row.name.trim(),
          contactPerson: row.contactPerson?.trim(),
          email: row.email?.trim(),
          phone: row.phone?.trim(),
          address: row.address?.trim(),
          city: row.city?.trim(),
          country: row.country?.trim() || 'Malawi',
          website: row.website?.trim(),
          category: categories,
          taxId: row.taxId?.trim(),
          paymentTerms: row.paymentTerms?.trim(),
          bankName: row.bankName?.trim(),
          accountNumber: row.accountNumber?.trim(),
          status: (row.status?.trim() as any) || 'active',
          rating,
          notes: row.notes?.trim(),
          createdBy: user.id
        };

        // Create supplier
        const supplier = await supplierService.createSupplier(supplierData);

        result.successfulSuppliers.push({
          row: rowNumber,
          supplierName: supplier.name,
          supplierId: supplier.supplierId,
          category: supplier.category,
          status: supplier.status
        });

      } catch (error) {
        logger.error(`Error processing supplier row ${rowNumber}`, LogCategory.PROCUREMENT, error);
        result.failedSuppliers.push({
          row: rowNumber,
          supplierName: row.name || 'Unknown',
          error: error instanceof Error ? error.message : 'Unknown error',
          data: row
        });
      }
    }

    logger.info('Supplier bulk import completed', LogCategory.PROCUREMENT, {
      userId: user.id,
      totalRows: result.totalRows,
      successful: result.successfulSuppliers.length,
      failed: result.failedSuppliers.length,
      duplicates: result.duplicateSuppliers.length
    });

    return NextResponse.json({
      success: true,
      data: result,
      message: `Import completed. ${result.successfulSuppliers.length} suppliers imported successfully.`
    });

  } catch (error) {
    logger.error('Error in supplier bulk import', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'SUPPLIER_IMPORT_ERROR',
      error instanceof Error ? error.message : 'Failed to import suppliers',
      'Unable to process the supplier import. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Check the file format and try again',
        'Ensure all required fields are present',
        'Contact support if the problem persists'
      ]
    );
  }
}

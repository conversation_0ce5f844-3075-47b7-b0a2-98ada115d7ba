import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { RequisitionService } from '@/lib/backend/services/procurement/RequisitionService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import { connectToDatabase } from '@/lib/backend/database';
import Department from '@/models/Department';
import User from '@/models/User';
import mongoose from 'mongoose';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import * as XLSX from 'xlsx';

export const runtime = 'nodejs';



const requisitionService = new RequisitionService();

interface RequisitionImportRow {
  title: string;
  description: string;
  justification?: string;
  departmentName: string;
  requestorEmail: string;
  priority: string;
  itemName: string;
  itemDescription?: string;
  quantity: string;
  unit: string;
  estimatedUnitPrice: string;
  category?: string;
  urgentDate?: string;
  notes?: string;
}

interface ImportResult {
  totalRows: number;
  successfulRequisitions: Array<{
    row: number;
    requisitionNumber: string;
    title: string;
    totalAmount: number;
    status: string;
  }>;
  failedRequisitions: Array<{
    row: number;
    title: string;
    error: string;
    data: any;
  }>;
  duplicateRequisitions: Array<{
    row: number;
    title: string;
    existingRequisitionNumber: string;
    action: string;
  }>;
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'REQUISITION_IMPORT_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to import requisitions.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'REQUISITION_IMPORT_FORBIDDEN',
        'Insufficient permissions to import requisitions',
        'You do not have permission to import requisition data.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    const fileName = file.name.toLowerCase();
    if (!fileName.endsWith('.csv') && !fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload a CSV or Excel file.' },
        { status: 400 }
      );
    }

    // Read file content
    const buffer = await file.arrayBuffer();
    let data: RequisitionImportRow[];

    try {
      if (fileName.endsWith('.csv')) {
        const text = new TextDecoder().decode(buffer);
        const lines = text.split('\n').filter(line => line.trim());
        const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
        
        data = lines.slice(1).map(line => {
          const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
          const row: any = {};
          headers.forEach((header, index) => {
            row[header] = values[index] || '';
          });
          return row;
        });
      } else {
        const workbook = XLSX.read(buffer, { type: 'buffer' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        data = XLSX.utils.sheet_to_json(worksheet);
      }
    } catch (parseError) {
      logger.error('Error parsing file', LogCategory.PROCUREMENT, parseError);
      return NextResponse.json(
        { error: 'Failed to parse file. Please check the file format.' },
        { status: 400 }
      );
    }

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: 'No data found in file' },
        { status: 400 }
      );
    }

    // Process requisitions
    const result: ImportResult = {
      totalRows: data.length,
      successfulRequisitions: [],
      failedRequisitions: [],
      duplicateRequisitions: []
    };

    // Group rows by title and requestor to create consolidated requisitions
    const requisitionGroups = new Map<string, RequisitionImportRow[]>();
    
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const rowNumber = i + 2;

      try {
        // Validate required fields
        if (!row.title || !row.title.trim()) {
          result.failedRequisitions.push({
            row: rowNumber,
            title: row.title || 'Unknown',
            error: 'Title is required',
            data: row
          });
          continue;
        }

        if (!row.requestorEmail || !row.requestorEmail.trim()) {
          result.failedRequisitions.push({
            row: rowNumber,
            title: row.title,
            error: 'Requestor email is required',
            data: row
          });
          continue;
        }

        if (!row.itemName || !row.itemName.trim()) {
          result.failedRequisitions.push({
            row: rowNumber,
            title: row.title,
            error: 'Item name is required',
            data: row
          });
          continue;
        }

        if (!row.quantity || isNaN(parseFloat(row.quantity))) {
          result.failedRequisitions.push({
            row: rowNumber,
            title: row.title,
            error: 'Valid quantity is required',
            data: row
          });
          continue;
        }

        if (!row.estimatedUnitPrice || isNaN(parseFloat(row.estimatedUnitPrice))) {
          result.failedRequisitions.push({
            row: rowNumber,
            title: row.title,
            error: 'Valid estimated unit price is required',
            data: row
          });
          continue;
        }

        // Group by title and requestor
        const groupKey = `${row.title.trim()}_${row.requestorEmail.trim()}`;
        if (!requisitionGroups.has(groupKey)) {
          requisitionGroups.set(groupKey, []);
        }
        requisitionGroups.get(groupKey)!.push(row);

      } catch (error) {
        logger.error(`Error processing requisition row ${rowNumber}`, LogCategory.PROCUREMENT, error);
        result.failedRequisitions.push({
          row: rowNumber,
          title: row.title || 'Unknown',
          error: error instanceof Error ? error.message : 'Unknown error',
          data: row
        });
      }
    }

    // Connect to database
    await connectToDatabase();

    // Fetch departments and users for lookup
    const [departments, users] = await Promise.all([
      Department.find({ status: 'active' }).select('_id name'),
      User.find({ status: 'active' }).select('_id email firstName lastName')
    ]);

    // Create lookup maps
    const departmentMap = new Map<string, string>();
    departments.forEach((dept: any) => {
      departmentMap.set(dept.name.toLowerCase().trim(), dept._id.toString());
    });

    const userMap = new Map<string, string>();
    users.forEach((userDoc: any) => {
      userMap.set(userDoc.email.toLowerCase().trim(), userDoc._id.toString());
    });

    logger.info('Created lookup maps for bulk import', LogCategory.PROCUREMENT, {
      departmentsCount: departments.length,
      usersCount: users.length
    });

    // Create requisitions from grouped data
    for (const [groupKey, rows] of requisitionGroups) {
      try {
        const firstRow = rows[0];

        // Find user by email
        let requestorId = user.id; // Default to current user
        if (firstRow.requestorEmail) {
          const foundUserId = userMap.get(firstRow.requestorEmail.toLowerCase().trim());
          if (foundUserId) {
            requestorId = foundUserId;
          } else {
            logger.warn('User not found for email, using current user', LogCategory.PROCUREMENT, {
              email: firstRow.requestorEmail,
              currentUserId: user.id
            });
          }
        }

        // Find department by name
        let departmentId = user.department; // Default to current user's department
        if (firstRow.departmentName) {
          const foundDepartmentId = departmentMap.get(firstRow.departmentName.toLowerCase().trim());
          if (foundDepartmentId) {
            departmentId = foundDepartmentId;
          } else {
            // If department not found, add to failed requisitions
            rows.forEach((row) => {
              result.failedRequisitions.push({
                row: data.indexOf(row) + 2,
                title: row.title,
                error: `Department "${firstRow.departmentName}" not found. Please use exact department names from the Reference Data sheet.`,
                data: row
              });
            });
            continue; // Skip this requisition group
          }
        }

        // Validate that we have a valid departmentId
        if (!departmentId) {
          rows.forEach((row) => {
            result.failedRequisitions.push({
              row: data.indexOf(row) + 2,
              title: row.title,
              error: 'No valid department found. Please specify a valid department name.',
              data: row
            });
          });
          continue; // Skip this requisition group
        }

        // Build items array
        const items = rows.map(row => ({
          name: row.itemName.trim(),
          description: row.itemDescription?.trim() || '',
          quantity: parseFloat(row.quantity),
          unit: row.unit?.trim() || 'pcs',
          estimatedUnitPrice: parseFloat(row.estimatedUnitPrice),
          estimatedTotal: parseFloat(row.quantity) * parseFloat(row.estimatedUnitPrice),
          category: row.category?.trim() || 'General',
          specifications: row.itemDescription?.trim() || ''
        }));

        // Calculate total amount
        const totalAmount = items.reduce((sum, item) => sum + item.estimatedTotal, 0);

        // Create requisition data
        const requisitionData = {
          title: firstRow.title.trim(),
          description: firstRow.description?.trim() || '',
          justification: firstRow.justification?.trim() || '',
          departmentId: new mongoose.Types.ObjectId(departmentId),
          requestedBy: new mongoose.Types.ObjectId(requestorId),
          priority: (firstRow.priority?.trim().toLowerCase() as any) || 'medium',
          items,
          totalAmount,
          urgentDate: firstRow.urgentDate ? new Date(firstRow.urgentDate) : undefined,
          notes: firstRow.notes?.trim() || '',
          status: 'draft' as const,
          createdBy: new mongoose.Types.ObjectId(user.id)
        };

        // Create requisition
        const requisition = await requisitionService.createRequisition(requisitionData);

        result.successfulRequisitions.push({
          row: data.indexOf(firstRow) + 2,
          requisitionNumber: (requisition as any).requisitionNumber || (requisition as any).requisitionId || 'N/A',
          title: requisition.title,
          totalAmount: requisition.totalAmount || 0,
          status: requisition.status
        });

      } catch (error) {
        logger.error(`Error creating requisition for group ${groupKey}`, LogCategory.PROCUREMENT, error);
        rows.forEach((row) => {
          result.failedRequisitions.push({
            row: data.indexOf(row) + 2,
            title: row.title,
            error: error instanceof Error ? error.message : 'Unknown error',
            data: row
          });
        });
      }
    }

    logger.info('Requisition bulk import completed', LogCategory.PROCUREMENT, {
      userId: user.id,
      totalRows: result.totalRows,
      successful: result.successfulRequisitions.length,
      failed: result.failedRequisitions.length,
      duplicates: result.duplicateRequisitions.length
    });

    return NextResponse.json({
      success: true,
      data: result,
      message: `Import completed. ${result.successfulRequisitions.length} requisitions imported successfully.`
    });

  } catch (error) {
    logger.error('Error in requisition bulk import', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'REQUISITION_IMPORT_ERROR',
      error instanceof Error ? error.message : 'Failed to import requisitions',
      'Unable to process the requisition import. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Check the file format and try again',
        'Ensure all required fields are present',
        'Verify requestor emails and department names exist in the system',
        'Contact support if the problem persists'
      ]
    );
  }
}

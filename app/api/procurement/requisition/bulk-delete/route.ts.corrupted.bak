import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { RequisitionService } from '@/lib/backend/services/procurement/RequisitionService';
import { AuditDeletionService } from '@/lib/services/audit/audit-deletion-service';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

const requisitionService = new RequisitionService();
const auditService = new AuditDeletionService();

// Validation schema for bulk delete request
const bulkDeleteSchema = z.object({
  requisitionIds: z.array(z.string()).min(1, 'At least one requisition ID is required'),
  deletionReason: z.string().min(25, 'Deletion reason must be at least 25 characters'),
  context: z.object({
    department: z.string(),
    fiscalYear: z.string()
  }).optional()
});

interface DeleteResult {
  requestedCount: number;
  deletedCount: number;
  auditRecordsCreated: number;
  errors: Array<{ requisitionId: string; message: string }>;
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'REQUISITION_DELETE_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to delete requisitions.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions - only managers and admins can bulk delete
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'REQUISITION_DELETE_FORBIDDEN',
        'Insufficient permissions to delete requisitions',
        'You do not have permission to delete requisition records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Parse and validate request body
    let body: unknown;
    try {
      body = await request.json();
    } catch (parseError) {
      return NextResponse.json(
        { error: 'Invalid request format' },
        { status: 400 }
      );
    }

    const validationResult = bulkDeleteSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { requisitionIds, deletionReason, context } = validationResult.data;

    // Initialize result tracking
    const result: DeleteResult = {
      requestedCount: requisitionIds.length,
      deletedCount: 0,
      auditRecordsCreated: 0,
      errors: []
    };

    // Process each requisition deletion
    for (const requisitionId of requisitionIds) {
      try {
        // Get requisition details for audit
        const requisition = await requisitionService.getRequisitionDetails(requisitionId);
        if (!requisition) {
          result.errors.push({
            requisitionId,
            message: 'Requisition not found'
          });
          continue;
        }

        // Check if requisition can be deleted (business rules)
        if (['approved', 'completed'].includes(requisition.status)) {
          result.errors.push({
            requisitionId,
            message: `Cannot delete requisition with status: ${requisition.status}`
          });
          continue;
        }

        // Create audit record before deletion
        const auditRecord = await auditService.createDeletionRecord({
          entityType: 'Requisition',
          entityId: requisitionId,
          entityData: {
            requisitionNumber: requisition.requisitionNumber,
            title: requisition.title,
            departmentId: requisition.departmentId,
            requestedBy: requisition.requestedBy,
            status: requisition.status,
            priority: requisition.priority,
            items: requisition.items,
            totalAmount: requisition.totalAmount,
            description: requisition.description,
            justification: requisition.justification
          },
          deletionReason,
          deletedBy: user.id,
          context: {
            department: context?.department || 'Procurement',
            fiscalYear: context?.fiscalYear || new Date().getFullYear().toString(),
            bulkOperation: true,
            totalRequisitions: requisitionIds.length
          }
        });

        // Delete the requisition
        await requisitionService.deleteRequisition(requisitionId);

        result.deletedCount++;
        result.auditRecordsCreated++;

        logger.info('Requisition deleted in bulk operation', LogCategory.PROCUREMENT, {
          requisitionNumber: requisition.requisitionNumber,
          requisitionId: requisition._id,
          deletedBy: user.id,
          auditRecordId: auditRecord._id,
          reason: deletionReason
        });

      } catch (error) {
        logger.error(`Error deleting requisition ${requisitionId}`, LogCategory.PROCUREMENT, error);
        result.errors.push({
          requisitionId,
          message: error instanceof Error ? error.message : 'Unknown error occurred'
        });
      }
    }

    // Log bulk operation summary
    logger.info('Requisition bulk delete operation completed', LogCategory.PROCUREMENT, {
      userId: user.id,
      requestedCount: result.requestedCount,
      deletedCount: result.deletedCount,
      errorCount: result.errors.length,
      auditRecordsCreated: result.auditRecordsCreated,
      deletionReason
    });

    return NextResponse.json({
      success: true,
      data: result,
      message: `Bulk delete completed. ${result.deletedCount} requisitions deleted successfully.`
    });

  } catch (error) {
    logger.error('Error in requisition bulk delete', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'REQUISITION_BULK_DELETE_ERROR',
      error instanceof Error ? error.message : 'Failed to delete requisitions',
      'Unable to complete the bulk delete operation. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try the operation again',
        'Check if requisitions have active dependencies',
        'Ensure requisitions are in deletable status (draft, rejected)',
        'Contact support if the problem persists'
      ]
    );
  }
}

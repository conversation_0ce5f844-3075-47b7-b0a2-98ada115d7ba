// app/api/procurement/deliveries/audit-delete/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { AuditDeletionService } from '@/lib/services/audit/audit-deletion-service';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import Delivery from '@/models/procurement/Delivery';
import { z } from 'zod';

// Validation schema for audit deletion
const auditDeleteSchema = z.object({
  ids: z.array(z.string()).min(1, 'At least one delivery ID is required'),
  deletionReason: z.string().min(10, 'Deletion reason must be at least 10 characters').max(1000, 'Deletion reason must be less than 1000 characters'),
  additionalContext: z.object({
    fiscalYear: z.string().optional(),
    department: z.string().optional(),
    relatedTransactions: z.array(z.string()).optional()
  }).optional()
});

/**
 * POST /api/procurement/deliveries/audit-delete
 * Perform audit-compliant deletion of deliveries
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'DELIVERY_DELETE_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to delete deliveries.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions - only specific roles can delete deliveries
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'DELIVERY_DELETE_FORBIDDEN',
        'Insufficient permissions to delete deliveries',
        'You do not have permission to delete delivery records. This action requires elevated privileges.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.HIGH,
        'User attempted to delete deliveries without proper permissions',
        [
          'Contact your administrator to request delivery deletion permissions',
          'Ensure you are logged in with the correct account',
          'Only Procurement Managers and System Administrators can delete deliveries'
        ]
      );
    }

    // Parse request body
    let body: unknown;
    try {
      body = await request.json();
    } catch (parseError: unknown) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'DELIVERY_DELETE_INVALID_JSON',
        'Invalid request format',
        'The request data is not in the correct format. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        400,
        ErrorSeverity.MEDIUM
      );
    }

    // Validate request data
    const validationResult = auditDeleteSchema.safeParse(body);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'DELIVERY_DELETE_VALIDATION_ERROR',
        'Invalid deletion request',
        'The deletion request is not valid. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.MEDIUM,
        validationResult.error.errors[0].message,
        [
          'Ensure at least one delivery ID is provided',
          'Provide a deletion reason of at least 10 characters',
          'Check that all delivery IDs are valid'
        ]
      );
    }

    const { ids, deletionReason, additionalContext = {} } = validationResult.data;

    // Prepare audit context
    const auditContext = {
      deletionReason: deletionReason.trim(),
      userInfo: {
        id: user.id,
        name: `${user.firstName} ${user.lastName}`,
        email: user.email,
        role: user.role
      },
      context: {
        fiscalYear: additionalContext.fiscalYear || new Date().getFullYear().toString(),
        department: additionalContext.department || 'Procurement',
        relatedTransactions: additionalContext.relatedTransactions || [],
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        sessionId: request.headers.get('x-session-id') || 'unknown'
      }
    };

    // Define validation rules for deliveries
    const validationRules = [
      {
        field: 'status',
        condition: (value: string) => !['in_transit', 'delivered'].includes(value),
        message: 'Cannot delete deliveries that are in transit or already delivered'
      }
    ];

    // Define permission check
    const permissionCheck = {
      isAdmin: hasRequiredPermissions(user, [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN]),
      userId: user.id,
      checkOwnership: false, // Deliveries don't have direct ownership
      ownershipField: 'createdBy'
    };

    // Perform audit deletion using the service
    const result = await AuditDeletionService.performAuditDeletion(
      Delivery,
      ids,
      auditContext,
      {
        validationRules,
        permissionCheck,
        populateFields: ['supplierId', 'purchaseOrderId', 'createdBy'],
        beforeDelete: async (deliveries) => {
          // Log delivery deletion for audit purposes
          logger.info('Preparing to delete deliveries', LogCategory.PROCUREMENT, {
            userId: user.id,
            deliveryCount: deliveries.length,
            deliveryIds: deliveries.map(d => d._id),
            deletionReason: deletionReason.trim()
          });

          // Check for any business logic constraints
          const activeDeliveries = deliveries.filter(d => 
            ['in_transit', 'delivered'].includes(d.status)
          );

          if (activeDeliveries.length > 0) {
            throw new Error(`Cannot delete ${activeDeliveries.length} deliveries that are in transit or delivered`);
          }
        },
        afterDelete: async (deletedDeliveries, auditRecords) => {
          // Log successful deletion
          logger.info('Deliveries successfully deleted', LogCategory.PROCUREMENT, {
            userId: user.id,
            deletedCount: deletedDeliveries.length,
            auditRecordsCreated: auditRecords.length,
            deletionReason: deletionReason.trim()
          });

          // Additional cleanup or notifications could be added here
        }
      }
    );

    return NextResponse.json(result);

  } catch (error: unknown) {
    logger.error('Error in delivery audit deletion', LogCategory.PROCUREMENT, error);

    // Handle specific business logic errors
    if (error instanceof Error) {
      if (error.message.includes('Cannot delete') && error.message.includes('in transit')) {
        return errorService.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'DELIVERY_DELETE_ACTIVE_DELIVERIES',
          'Cannot delete active deliveries',
          'Some deliveries cannot be deleted because they are currently in transit or have been delivered.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          400,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Only delete deliveries that are scheduled or cancelled',
            'Update delivery status before deletion if appropriate',
            'Contact system administrator for assistance'
          ]
        );
      }

      if (error.message.includes('not found')) {
        return errorService.createApiResponse(
          ErrorType.NOT_FOUND,
          'DELIVERY_DELETE_NOT_FOUND',
          'Some deliveries not found',
          'One or more deliveries could not be found. They may have already been deleted.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          404,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Verify all delivery IDs are correct',
            'Refresh the page to see current delivery status',
            'Remove non-existent delivery IDs from the selection'
          ]
        );
      }
    }

    // Generic server error
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'DELIVERY_DELETE_ERROR',
      error instanceof Error ? error.message : 'Failed to delete deliveries',
      'Unable to delete the delivery records. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try the deletion operation again',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

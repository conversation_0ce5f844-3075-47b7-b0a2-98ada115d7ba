import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { deliveryService, GoodsReceiptData } from '@/lib/backend/services/procurement/DeliveryService';
import { procurementBudgetIntegrationService } from '@/lib/services/procurement/budget-integration-service';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

// Validation schema for goods receipt
const goodsReceiptSchema = z.object({
  receivedDate: z.string().transform(val => new Date(val)).optional(),
  goodsReceiptNumber: z.string().optional(),
  items: z.array(z.object({
    purchaseOrderItemId: z.string().min(1, 'Purchase order item ID is required'),
    quantityAccepted: z.number().min(0, 'Quantity accepted must be non-negative'),
    quantityRejected: z.number().min(0, 'Quantity rejected must be non-negative'),
    rejectionReason: z.string().optional(),
    condition: z.enum(['good', 'damaged', 'defective', 'incomplete', 'expired']),
    notes: z.string().optional()
  })).min(1, 'At least one receipt item is required'),
  packingList: z.boolean().optional(),
  invoice: z.boolean().optional(),
  deliveryNote: z.boolean().optional(),
  qualityCertificates: z.boolean().optional(),
  customsDocuments: z.boolean().optional(),
  notes: z.string().optional()
});

// Response type for successful receipt operations
interface ReceiptResponse {
  success: true;
  data: unknown;
  message?: string;
}

/**
 * POST /api/procurement/deliveries/[id]/receipt
 * Record goods receipt for a delivery
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ReceiptResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'RECEIPT_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to record goods receipts.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.WAREHOUSE_MANAGER,
      UserRole.WAREHOUSE_STAFF
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'RECEIPT_FORBIDDEN',
        'Insufficient permissions to record goods receipts',
        'You do not have permission to record goods receipt information.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to record goods receipt without proper permissions',
        [
          'Contact your administrator to request goods receipt permissions',
          'Ensure you are logged in with the correct account',
          'Verify you have warehouse or procurement access'
        ]
      );
    }

    // Get delivery ID from params
    const { id } = await params;

    // Validate delivery ID
    if (!id || id.length !== 24) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'RECEIPT_INVALID_DELIVERY_ID',
        'Invalid delivery ID',
        'The delivery ID provided is not valid. Please check the ID and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          deliveryId: id
        },
        400,
        ErrorSeverity.LOW,
        'Delivery ID must be a valid 24-character MongoDB ObjectId',
        [
          'Ensure the delivery ID is copied correctly',
          'Check that the URL is complete and correct'
        ]
      );
    }

    // Parse request body
    let body: unknown;
    try {
      body = await request.json();
    } catch (parseError: unknown) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'RECEIPT_INVALID_JSON',
        'Invalid request format',
        'The request data is not in the correct format. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          parseError: parseError instanceof Error ? parseError.message : 'Unknown parse error'
        },
        400,
        ErrorSeverity.MEDIUM,
        'Failed to parse JSON request body',
        [
          'Ensure the request body is valid JSON',
          'Check that all required fields are included',
          'Verify date formats are correct (YYYY-MM-DD)'
        ]
      );
    }

    // Validate request data
    const validationResult = goodsReceiptSchema.safeParse(body);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'RECEIPT_VALIDATION_ERROR',
        'Invalid goods receipt data',
        'The goods receipt information provided is not valid. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.MEDIUM,
        validationResult.error.errors[0].message,
        [
          'Ensure all required fields are filled',
          'Check that quantities are non-negative numbers',
          'Verify item conditions are valid',
          'Ensure at least one receipt item is included'
        ]
      );
    }

    const receiptData: GoodsReceiptData = {
      ...validationResult.data,
      receivedBy: user.id
    };

    // Validate receipt quantities
    const invalidItems = receiptData.items.filter(item => {
      const totalProcessed = item.quantityAccepted + item.quantityRejected;
      return totalProcessed <= 0;
    });

    if (invalidItems.length > 0) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'RECEIPT_INVALID_QUANTITIES',
        'Invalid receipt quantities',
        'Some items have no accepted or rejected quantities. Please specify quantities for all items.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          invalidItems: invalidItems.map(item => ({
            purchaseOrderItemId: item.purchaseOrderItemId,
            quantityAccepted: item.quantityAccepted,
            quantityRejected: item.quantityRejected
          }))
        },
        400,
        ErrorSeverity.MEDIUM,
        `${invalidItems.length} items have no accepted or rejected quantities`,
        [
          'Specify accepted and/or rejected quantities for all items',
          'Ensure quantities are greater than zero',
          'Provide rejection reasons for rejected items'
        ]
      );
    }

    // Validate rejection reasons for rejected items
    const rejectedItemsWithoutReason = receiptData.items.filter(item => 
      item.quantityRejected > 0 && !item.rejectionReason
    );

    if (rejectedItemsWithoutReason.length > 0) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'RECEIPT_MISSING_REJECTION_REASONS',
        'Missing rejection reasons',
        'Items with rejected quantities must include rejection reasons.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          itemsWithoutReasons: rejectedItemsWithoutReason.map(item => ({
            purchaseOrderItemId: item.purchaseOrderItemId,
            quantityRejected: item.quantityRejected
          }))
        },
        400,
        ErrorSeverity.MEDIUM,
        `${rejectedItemsWithoutReason.length} rejected items missing rejection reasons`,
        [
          'Provide rejection reasons for all rejected items',
          'Specify the condition and reason for rejection',
          'Include detailed notes if necessary'
        ]
      );
    }

    // Record goods receipt
    const delivery = await deliveryService.recordGoodsReceipt(id, receiptData);

    // Record expenditure for budget integration
    if (delivery.purchaseOrderId) {
      try {
        // Calculate actual amount from received items
        const actualAmount = receiptData.items.reduce((total, item) => {
          // For now, we'll use the purchase order item price
          // In a real implementation, you might want to get the actual unit price
          return total + (item.quantityAccepted * (item.unitPrice || 0));
        }, 0);

        if (actualAmount > 0) {
          logger.info('Recording expenditure for goods receipt', LogCategory.PROCUREMENT, {
            deliveryId: id,
            purchaseOrderId: delivery.purchaseOrderId,
            actualAmount,
            userId: user.id
          });

          await procurementBudgetIntegrationService.recordExpenditure(
            delivery.purchaseOrderId,
            actualAmount,
            `Goods receipt for delivery ${id} - ${receiptData.items.length} items received`
          );

          logger.info('Expenditure recorded successfully for goods receipt', LogCategory.PROCUREMENT, {
            deliveryId: id,
            purchaseOrderId: delivery.purchaseOrderId,
            actualAmount,
            userId: user.id
          });
        }
      } catch (expenditureError) {
        logger.error('Failed to record expenditure for goods receipt', LogCategory.PROCUREMENT, {
          deliveryId: id,
          purchaseOrderId: delivery.purchaseOrderId,
          error: expenditureError instanceof Error ? expenditureError.message : 'Unknown error',
          userId: user.id
        });

        // Don't fail the goods receipt if expenditure recording fails
        // But include a warning in the response
        return NextResponse.json({
          success: true,
          message: 'Goods receipt recorded successfully',
          warning: 'Expenditure recording failed - please contact finance team',
          data: delivery,
          expenditureError: expenditureError instanceof Error ? expenditureError.message : 'Unknown error'
        } as ReceiptResponse, { status: 201 });
      }
    }

    logger.info('Goods receipt recorded successfully', LogCategory.PROCUREMENT, {
      deliveryId: id,
      purchaseOrderId: delivery.purchaseOrderId,
      itemsReceived: receiptData.items.length,
      expenditureRecorded: !!delivery.purchaseOrderId,
      userId: user.id
    });

    return NextResponse.json({
      success: true,
      message: 'Goods receipt recorded successfully',
      data: delivery
    } as ReceiptResponse, { status: 201 });

  } catch (error: unknown) {
    logger.error('Error recording goods receipt', LogCategory.PROCUREMENT, error);
    
    // Handle specific business logic errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return errorService.createApiResponse(
          ErrorType.NOT_FOUND,
          'RECEIPT_DELIVERY_NOT_FOUND',
          'Delivery not found',
          'The delivery you are trying to record receipt for could not be found.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          404,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Verify the delivery ID is correct',
            'Check if the delivery exists in the system',
            'Contact support if needed'
          ]
        );
      }

      if (error.message.includes('cannot be received')) {
        return errorService.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'RECEIPT_NOT_ALLOWED',
          'Goods receipt not allowed',
          'This delivery cannot be received at this time. Check the delivery status.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          400,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Check the current delivery status',
            'Ensure the delivery has been marked as delivered',
            'Contact the procurement team if needed'
          ]
        );
      }
    }

    // Generic server error
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'RECEIPT_RECORD_ERROR',
      error instanceof Error ? error.message : 'Failed to record goods receipt',
      'Unable to record the goods receipt. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try recording the receipt again',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { deliveryService } from '@/lib/backend/services/procurement/DeliveryService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

// Validation schema for status update
const statusUpdateSchema = z.object({
  status: z.enum(['scheduled', 'in_transit', 'delivered', 'partially_delivered', 'delayed', 'cancelled', 'returned']),
  actualDate: z.string().transform(val => new Date(val)).optional(),
  notes: z.string().optional()
});

// Response type for successful status operations
interface StatusResponse {
  success: true;
  data: unknown;
  message?: string;
}

/**
 * PUT /api/procurement/deliveries/[id]/status
 * Update delivery status
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<StatusResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'STATUS_UPDATE_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to update delivery status.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.WAREHOUSE_MANAGER,
      UserRole.WAREHOUSE_STAFF
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'STATUS_UPDATE_FORBIDDEN',
        'Insufficient permissions to update delivery status',
        'You do not have permission to update delivery status.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to update delivery status without proper permissions',
        [
          'Contact your administrator to request delivery status update permissions',
          'Ensure you are logged in with the correct account',
          'Verify you have warehouse or procurement access'
        ]
      );
    }

    // Get delivery ID from params
    const { id } = await params;

    // Validate delivery ID
    if (!id || id.length !== 24) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'STATUS_UPDATE_INVALID_DELIVERY_ID',
        'Invalid delivery ID',
        'The delivery ID provided is not valid. Please check the ID and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          deliveryId: id
        },
        400,
        ErrorSeverity.LOW,
        'Delivery ID must be a valid 24-character MongoDB ObjectId',
        [
          'Ensure the delivery ID is copied correctly',
          'Check that the URL is complete and correct'
        ]
      );
    }

    // Parse request body
    let body: unknown;
    try {
      body = await request.json();
    } catch (parseError: unknown) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'STATUS_UPDATE_INVALID_JSON',
        'Invalid request format',
        'The request data is not in the correct format. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          parseError: parseError instanceof Error ? parseError.message : 'Unknown parse error'
        },
        400,
        ErrorSeverity.MEDIUM,
        'Failed to parse JSON request body',
        [
          'Ensure the request body is valid JSON',
          'Check that all required fields are included',
          'Verify date formats are correct (YYYY-MM-DD)'
        ]
      );
    }

    // Validate request data
    const validationResult = statusUpdateSchema.safeParse(body);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'STATUS_UPDATE_VALIDATION_ERROR',
        'Invalid status update data',
        'The status update information provided is not valid. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.MEDIUM,
        validationResult.error.errors[0].message,
        [
          'Ensure the status is one of the allowed values',
          'Check that date formats are correct',
          'Verify all required fields are provided'
        ]
      );
    }

    const { status, actualDate, notes } = validationResult.data;

    // Validate business logic for status updates
    if (status === 'delivered' && !actualDate) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'STATUS_UPDATE_MISSING_ACTUAL_DATE',
        'Actual delivery date required',
        'When marking a delivery as delivered, you must provide the actual delivery date.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          status,
          actualDate
        },
        400,
        ErrorSeverity.MEDIUM,
        'Delivered status requires actual delivery date',
        [
          'Provide the actual delivery date',
          'Ensure the date format is correct (YYYY-MM-DD)',
          'Use the current date if delivered today'
        ]
      );
    }

    if (actualDate && actualDate > new Date()) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'STATUS_UPDATE_FUTURE_DATE',
        'Invalid actual delivery date',
        'The actual delivery date cannot be in the future.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          actualDate: actualDate.toISOString(),
          currentDate: new Date().toISOString()
        },
        400,
        ErrorSeverity.LOW,
        'Actual delivery date is in the future',
        [
          'Use the current date or a past date',
          'Verify the date format is correct',
          'Check your system clock if needed'
        ]
      );
    }

    // Update delivery status
    const delivery = await deliveryService.updateDeliveryStatus(id, status, user.id, actualDate);

    // Add notes if provided
    if (notes) {
      const updateData = {
        notes: delivery.notes ? `${delivery.notes}\n\nStatus Update: ${notes}` : `Status Update: ${notes}`,
        updatedBy: user.id
      };
      await deliveryService.updateDelivery(id, updateData);
    }

    return NextResponse.json({
      success: true,
      message: `Delivery status updated to ${status} successfully`,
      data: delivery
    } as StatusResponse);

  } catch (error: unknown) {
    logger.error('Error updating delivery status', LogCategory.PROCUREMENT, error);
    
    // Handle specific business logic errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return errorService.createApiResponse(
          ErrorType.NOT_FOUND,
          'STATUS_UPDATE_DELIVERY_NOT_FOUND',
          'Delivery not found',
          'The delivery you are trying to update could not be found.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          404,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Verify the delivery ID is correct',
            'Check if the delivery exists in the system',
            'Contact support if needed'
          ]
        );
      }

      if (error.message.includes('Invalid status transition')) {
        return errorService.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'STATUS_UPDATE_INVALID_TRANSITION',
          'Invalid status transition',
          'The status change you are trying to make is not allowed for this delivery.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          400,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Check the current delivery status',
            'Verify the status transition is valid',
            'Review the delivery workflow requirements',
            'Contact support if you need help with status changes'
          ]
        );
      }
    }

    // Generic server error
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'STATUS_UPDATE_ERROR',
      error instanceof Error ? error.message : 'Failed to update delivery status',
      'Unable to update the delivery status. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try updating the status again',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { PurchaseOrderService } from '@/lib/backend/services/procurement/PurchaseOrderService';
import { SupplierService } from '@/lib/backend/services/procurement/SupplierService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import * as XLSX from 'xlsx';

const purchaseOrderService = new PurchaseOrderService();
const supplierService = new SupplierService();

interface PurchaseOrderImportRow {
  supplierName: string;
  orderDate: string;
  expectedDeliveryDate?: string;
  itemName: string;
  itemDescription?: string;
  quantity: string;
  unit: string;
  unitPrice: string;
  tax?: string;
  discount?: string;
  paymentTerms?: string;
  shippingTerms?: string;
  notes?: string;
  status?: string;
}

interface ImportResult {
  totalRows: number;
  successfulOrders: Array<{
    row: number;
    orderNumber: string;
    supplierName: string;
    total: number;
    status: string;
  }>;
  failedOrders: Array<{
    row: number;
    supplierName: string;
    error: string;
    data: any;
  }>;
  duplicateOrders: Array<{
    row: number;
    supplierName: string;
    existingOrderNumber: string;
    action: string;
  }>;
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'PURCHASE_ORDER_IMPORT_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to import purchase orders.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'PURCHASE_ORDER_IMPORT_FORBIDDEN',
        'Insufficient permissions to import purchase orders',
        'You do not have permission to import purchase order data.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    const fileName = file.name.toLowerCase();
    if (!fileName.endsWith('.csv') && !fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload a CSV or Excel file.' },
        { status: 400 }
      );
    }

    // Read file content
    const buffer = await file.arrayBuffer();
    let data: PurchaseOrderImportRow[];

    try {
      if (fileName.endsWith('.csv')) {
        const text = new TextDecoder().decode(buffer);
        const lines = text.split('\n').filter(line => line.trim());
        const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
        
        data = lines.slice(1).map(line => {
          const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
          const row: any = {};
          headers.forEach((header, index) => {
            row[header] = values[index] || '';
          });
          return row;
        });
      } else {
        const workbook = XLSX.read(buffer, { type: 'buffer' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        data = XLSX.utils.sheet_to_json(worksheet);
      }
    } catch (parseError) {
      logger.error('Error parsing file', LogCategory.PROCUREMENT, parseError);
      return NextResponse.json(
        { error: 'Failed to parse file. Please check the file format.' },
        { status: 400 }
      );
    }

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: 'No data found in file' },
        { status: 400 }
      );
    }

    // Process purchase orders
    const result: ImportResult = {
      totalRows: data.length,
      successfulOrders: [],
      failedOrders: [],
      duplicateOrders: []
    };

    // Group rows by supplier and order date to create consolidated orders
    const orderGroups = new Map<string, PurchaseOrderImportRow[]>();
    
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const rowNumber = i + 2;

      try {
        // Validate required fields
        if (!row.supplierName || !row.supplierName.trim()) {
          result.failedOrders.push({
            row: rowNumber,
            supplierName: row.supplierName || 'Unknown',
            error: 'Supplier name is required',
            data: row
          });
          continue;
        }

        if (!row.itemName || !row.itemName.trim()) {
          result.failedOrders.push({
            row: rowNumber,
            supplierName: row.supplierName,
            error: 'Item name is required',
            data: row
          });
          continue;
        }

        if (!row.quantity || isNaN(parseFloat(row.quantity))) {
          result.failedOrders.push({
            row: rowNumber,
            supplierName: row.supplierName,
            error: 'Valid quantity is required',
            data: row
          });
          continue;
        }

        if (!row.unitPrice || isNaN(parseFloat(row.unitPrice))) {
          result.failedOrders.push({
            row: rowNumber,
            supplierName: row.supplierName,
            error: 'Valid unit price is required',
            data: row
          });
          continue;
        }

        // Group by supplier and order date
        const groupKey = `${row.supplierName.trim()}_${row.orderDate || new Date().toISOString().split('T')[0]}`;
        if (!orderGroups.has(groupKey)) {
          orderGroups.set(groupKey, []);
        }
        orderGroups.get(groupKey)!.push(row);

      } catch (error) {
        logger.error(`Error processing purchase order row ${rowNumber}`, LogCategory.PROCUREMENT, error);
        result.failedOrders.push({
          row: rowNumber,
          supplierName: row.supplierName || 'Unknown',
          error: error instanceof Error ? error.message : 'Unknown error',
          data: row
        });
      }
    }

    // Create purchase orders from grouped data
    for (const [groupKey, rows] of orderGroups) {
      try {
        const firstRow = rows[0];
        
        // Find supplier
        const supplier = await supplierService.findOne({ name: firstRow.supplierName.trim() });
        if (!supplier) {
          rows.forEach((row, index) => {
            result.failedOrders.push({
              row: data.indexOf(row) + 2,
              supplierName: row.supplierName,
              error: `Supplier '${row.supplierName}' not found`,
              data: row
            });
          });
          continue;
        }

        // Build items array
        const items = rows.map(row => ({
          name: row.itemName.trim(),
          description: row.itemDescription?.trim() || '',
          quantity: parseFloat(row.quantity),
          unit: row.unit?.trim() || 'pcs',
          unitPrice: parseFloat(row.unitPrice),
          totalPrice: parseFloat(row.quantity) * parseFloat(row.unitPrice),
          tax: row.tax ? parseFloat(row.tax) : 0,
          discount: row.discount ? parseFloat(row.discount) : 0
        }));

        // Calculate totals
        const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);
        const tax = items.reduce((sum, item) => sum + item.tax, 0);
        const total = subtotal + tax;

        // Create purchase order data
        const orderData = {
          supplierId: supplier._id,
          orderDate: firstRow.orderDate ? new Date(firstRow.orderDate) : new Date(),
          expectedDeliveryDate: firstRow.expectedDeliveryDate ? new Date(firstRow.expectedDeliveryDate) : undefined,
          items,
          subtotal,
          tax,
          total,
          paymentTerms: firstRow.paymentTerms?.trim(),
          shippingTerms: firstRow.shippingTerms?.trim(),
          notes: firstRow.notes?.trim(),
          status: (firstRow.status?.trim() as any) || 'draft',
          createdBy: user.id
        };

        // Create purchase order
        const order = await purchaseOrderService.createPurchaseOrder(orderData);

        result.successfulOrders.push({
          row: data.indexOf(firstRow) + 2,
          orderNumber: order.orderNumber,
          supplierName: supplier.name,
          total: order.total,
          status: order.status
        });

      } catch (error) {
        logger.error(`Error creating purchase order for group ${groupKey}`, LogCategory.PROCUREMENT, error);
        rows.forEach((row) => {
          result.failedOrders.push({
            row: data.indexOf(row) + 2,
            supplierName: row.supplierName,
            error: error instanceof Error ? error.message : 'Unknown error',
            data: row
          });
        });
      }
    }

    logger.info('Purchase order bulk import completed', LogCategory.PROCUREMENT, {
      userId: user.id,
      totalRows: result.totalRows,
      successful: result.successfulOrders.length,
      failed: result.failedOrders.length,
      duplicates: result.duplicateOrders.length
    });

    return NextResponse.json({
      success: true,
      data: result,
      message: `Import completed. ${result.successfulOrders.length} purchase orders imported successfully.`
    });

  } catch (error) {
    logger.error('Error in purchase order bulk import', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'PURCHASE_ORDER_IMPORT_ERROR',
      error instanceof Error ? error.message : 'Failed to import purchase orders',
      'Unable to process the purchase order import. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Check the file format and try again',
        'Ensure all required fields are present',
        'Verify supplier names exist in the system',
        'Contact support if the problem persists'
      ]
    );
  }
}

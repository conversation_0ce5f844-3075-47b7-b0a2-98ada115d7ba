import { NextRequest, NextResponse } from 'next/server';
import * as XLSX from 'xlsx';

export async function GET(request: NextRequest) {
  try {
    // Sample data for the template
    const sampleData = [
      {
        supplierName: 'Office Supplies Ltd',
        orderDate: '2024-01-15',
        expectedDeliveryDate: '2024-01-25',
        itemName: 'A4 Paper',
        itemDescription: 'White A4 printing paper, 80gsm',
        quantity: '10',
        unit: 'box',
        unitPrice: '15000',
        tax: '2250',
        discount: '0',
        paymentTerms: 'Net 30',
        shippingTerms: 'FOB',
        notes: 'Urgent delivery required',
        status: 'draft'
      },
      {
        supplierName: 'Office Supplies Ltd',
        orderDate: '2024-01-15',
        expectedDeliveryDate: '2024-01-25',
        itemName: 'Ballpoint Pens',
        itemDescription: 'Blue ink ballpoint pens',
        quantity: '50',
        unit: 'pcs',
        unitPrice: '500',
        tax: '3750',
        discount: '5',
        paymentTerms: 'Net 30',
        shippingTerms: 'FOB',
        notes: 'Urgent delivery required',
        status: 'draft'
      },
      {
        supplierName: 'Tech Solutions Inc',
        orderDate: '2024-01-16',
        expectedDeliveryDate: '2024-01-30',
        itemName: 'Laptop Computer',
        itemDescription: 'Business laptop with Windows 11',
        quantity: '5',
        unit: 'pcs',
        unitPrice: '450000',
        tax: '337500',
        discount: '10',
        paymentTerms: 'Net 15',
        shippingTerms: 'CIF',
        notes: 'Include warranty and setup',
        status: 'draft'
      },
      {
        supplierName: 'Construction Materials Co',
        orderDate: '2024-01-17',
        expectedDeliveryDate: '2024-01-27',
        itemName: 'Cement',
        itemDescription: 'Portland cement 50kg bags',
        quantity: '100',
        unit: 'bag',
        unitPrice: '8500',
        tax: '127500',
        discount: '0',
        paymentTerms: 'COD',
        shippingTerms: 'FOB',
        notes: 'Delivery to construction site',
        status: 'draft'
      }
    ];

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(sampleData);

    // Set column widths for better readability
    const columnWidths = [
      { wch: 25 }, // supplierName
      { wch: 12 }, // orderDate
      { wch: 18 }, // expectedDeliveryDate
      { wch: 20 }, // itemName
      { wch: 30 }, // itemDescription
      { wch: 10 }, // quantity
      { wch: 8 },  // unit
      { wch: 12 }, // unitPrice
      { wch: 8 },  // tax
      { wch: 8 },  // discount
      { wch: 15 }, // paymentTerms
      { wch: 15 }, // shippingTerms
      { wch: 25 }, // notes
      { wch: 10 }  // status
    ];
    worksheet['!cols'] = columnWidths;

    // Add the worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Purchase Orders');

    // Create instructions sheet
    const instructions = [
      {
        Field: 'supplierName',
        Description: 'Supplier name (required)',
        Example: 'Office Supplies Ltd',
        Notes: 'Must match existing supplier name exactly'
      },
      {
        Field: 'orderDate',
        Description: 'Order date (required)',
        Example: '2024-01-15',
        Notes: 'Format: YYYY-MM-DD'
      },
      {
        Field: 'expectedDeliveryDate',
        Description: 'Expected delivery date',
        Example: '2024-01-25',
        Notes: 'Format: YYYY-MM-DD, optional'
      },
      {
        Field: 'itemName',
        Description: 'Item name (required)',
        Example: 'A4 Paper',
        Notes: 'Name of the item being ordered'
      },
      {
        Field: 'itemDescription',
        Description: 'Item description',
        Example: 'White A4 printing paper, 80gsm',
        Notes: 'Optional detailed description'
      },
      {
        Field: 'quantity',
        Description: 'Quantity (required)',
        Example: '10',
        Notes: 'Must be a positive number'
      },
      {
        Field: 'unit',
        Description: 'Unit of measurement',
        Example: 'box, pcs, kg, m, l',
        Notes: 'Default: pcs'
      },
      {
        Field: 'unitPrice',
        Description: 'Unit price in MWK (required)',
        Example: '15000',
        Notes: 'Price per unit in Malawi Kwacha'
      },
      {
        Field: 'tax',
        Description: 'Tax amount in MWK',
        Example: '2250',
        Notes: 'Optional, tax amount for the item'
      },
      {
        Field: 'discount',
        Description: 'Discount percentage',
        Example: '5',
        Notes: 'Optional, discount as percentage (0-100)'
      },
      {
        Field: 'paymentTerms',
        Description: 'Payment terms',
        Example: 'Net 30, COD, Net 15',
        Notes: 'Optional payment terms'
      },
      {
        Field: 'shippingTerms',
        Description: 'Shipping terms',
        Example: 'FOB, CIF, EXW',
        Notes: 'Optional shipping terms'
      },
      {
        Field: 'notes',
        Description: 'Additional notes',
        Example: 'Urgent delivery required',
        Notes: 'Optional notes or special instructions'
      },
      {
        Field: 'status',
        Description: 'Order status',
        Example: 'draft, sent, confirmed',
        Notes: 'Default: draft'
      }
    ];

    const instructionsSheet = XLSX.utils.json_to_sheet(instructions);
    instructionsSheet['!cols'] = [
      { wch: 20 }, // Field
      { wch: 30 }, // Description
      { wch: 25 }, // Example
      { wch: 35 }  // Notes
    ];
    XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Instructions');

    // Create grouping instructions sheet
    const groupingInstructions = [
      {
        'Import Logic': 'Items with the same supplier name and order date will be grouped into a single purchase order'
      },
      {
        'Import Logic': 'Each row represents one line item in the purchase order'
      },
      {
        'Import Logic': 'Multiple items for the same supplier on the same date will create one order with multiple line items'
      },
      {
        'Import Logic': 'Different suppliers or different dates will create separate purchase orders'
      },
      {
        'Import Logic': 'Supplier names must match existing suppliers in the system exactly'
      },
      {
        'Import Logic': 'All required fields must be filled for successful import'
      },
      {
        'Import Logic': 'Invalid rows will be reported in the import results'
      }
    ];

    const groupingSheet = XLSX.utils.json_to_sheet(groupingInstructions);
    groupingSheet['!cols'] = [{ wch: 80 }];
    XLSX.utils.book_append_sheet(workbook, groupingSheet, 'Import Logic');

    // Generate Excel file buffer
    const excelBuffer = XLSX.write(workbook, { 
      type: 'buffer', 
      bookType: 'xlsx' 
    });

    // Set response headers
    const headers = new Headers();
    headers.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    headers.set('Content-Disposition', 'attachment; filename="purchase_order_import_template.xlsx"');
    headers.set('Content-Length', excelBuffer.length.toString());

    return new NextResponse(excelBuffer, {
      status: 200,
      headers
    });

  } catch (error) {
    console.error('Error generating purchase order template:', error);
    return NextResponse.json(
      { error: 'Failed to generate template' },
      { status: 500 }
    );
  }
}

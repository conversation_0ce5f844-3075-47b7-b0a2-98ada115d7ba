import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { PurchaseOrderService } from '@/lib/backend/services/procurement/PurchaseOrderService';
import { AuditDeletionService } from '@/lib/services/audit/audit-deletion-service';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

const purchaseOrderService = new PurchaseOrderService();
const auditService = new AuditDeletionService();

// Validation schema for bulk delete request
const bulkDeleteSchema = z.object({
  orderIds: z.array(z.string()).min(1, 'At least one purchase order ID is required'),
  deletionReason: z.string().min(25, 'Deletion reason must be at least 25 characters'),
  context: z.object({
    department: z.string(),
    fiscalYear: z.string()
  }).optional()
});

interface DeleteResult {
  requestedCount: number;
  deletedCount: number;
  auditRecordsCreated: number;
  errors: Array<{ orderId: string; message: string }>;
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'PURCHASE_ORDER_DELETE_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to delete purchase orders.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions - only managers and admins can bulk delete
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'PURCHASE_ORDER_DELETE_FORBIDDEN',
        'Insufficient permissions to delete purchase orders',
        'You do not have permission to delete purchase order records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM
      );
    }

    // Parse and validate request body
    let body: unknown;
    try {
      body = await request.json();
    } catch (parseError) {
      return NextResponse.json(
        { error: 'Invalid request format' },
        { status: 400 }
      );
    }

    const validationResult = bulkDeleteSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { orderIds, deletionReason, context } = validationResult.data;

    // Initialize result tracking
    const result: DeleteResult = {
      requestedCount: orderIds.length,
      deletedCount: 0,
      auditRecordsCreated: 0,
      errors: []
    };

    // Process each purchase order deletion
    for (const orderId of orderIds) {
      try {
        // Get purchase order details for audit
        const order = await purchaseOrderService.findById(orderId);
        if (!order) {
          result.errors.push({
            orderId,
            message: 'Purchase order not found'
          });
          continue;
        }

        // Check if order can be deleted (business rules)
        if (order.status === 'confirmed' || order.status === 'partially_received' || order.status === 'received') {
          result.errors.push({
            orderId,
            message: `Cannot delete purchase order with status: ${order.status}`
          });
          continue;
        }

        // Create audit record before deletion
        const auditRecord = await auditService.createDeletionRecord({
          entityType: 'PurchaseOrder',
          entityId: orderId,
          entityData: {
            orderNumber: order.orderNumber,
            supplierId: order.supplierId,
            orderDate: order.orderDate,
            expectedDeliveryDate: order.expectedDeliveryDate,
            status: order.status,
            items: order.items,
            subtotal: order.subtotal,
            tax: order.tax,
            total: order.total,
            paymentTerms: order.paymentTerms,
            shippingTerms: order.shippingTerms,
            notes: order.notes
          },
          deletionReason,
          deletedBy: user.id,
          context: {
            department: context?.department || 'Procurement',
            fiscalYear: context?.fiscalYear || new Date().getFullYear().toString(),
            bulkOperation: true,
            totalOrders: orderIds.length
          }
        });

        // Delete the purchase order
        await purchaseOrderService.deletePurchaseOrder(orderId);

        result.deletedCount++;
        result.auditRecordsCreated++;

        logger.info('Purchase order deleted in bulk operation', LogCategory.PROCUREMENT, {
          orderNumber: order.orderNumber,
          orderId: order._id,
          deletedBy: user.id,
          auditRecordId: auditRecord._id,
          reason: deletionReason
        });

      } catch (error) {
        logger.error(`Error deleting purchase order ${orderId}`, LogCategory.PROCUREMENT, error);
        result.errors.push({
          orderId,
          message: error instanceof Error ? error.message : 'Unknown error occurred'
        });
      }
    }

    // Log bulk operation summary
    logger.info('Purchase order bulk delete operation completed', LogCategory.PROCUREMENT, {
      userId: user.id,
      requestedCount: result.requestedCount,
      deletedCount: result.deletedCount,
      errorCount: result.errors.length,
      auditRecordsCreated: result.auditRecordsCreated,
      deletionReason
    });

    return NextResponse.json({
      success: true,
      data: result,
      message: `Bulk delete completed. ${result.deletedCount} purchase orders deleted successfully.`
    });

  } catch (error) {
    logger.error('Error in purchase order bulk delete', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'PURCHASE_ORDER_BULK_DELETE_ERROR',
      error instanceof Error ? error.message : 'Failed to delete purchase orders',
      'Unable to complete the bulk delete operation. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try the operation again',
        'Check if purchase orders have active dependencies',
        'Ensure orders are in deletable status (draft, sent, cancelled)',
        'Contact support if the problem persists'
      ]
    );
  }
}

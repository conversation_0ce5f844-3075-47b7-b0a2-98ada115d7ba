// app/api/procurement/tender/route.ts
import { NextRequest, NextResponse } from 'next/server';


// Auto-generated type definitions
interface MongoFilter {
  [key: string]: string | number | boolean | Date | RegExp | { $gte?: Date | number; $lte?: Date | number } | null | undefined;
  departmentId?: string;
  category?: string;
  status?: string;
  entityType?: string;
}


import { getCurrentUser } from '@/lib/backend/auth/auth';
import { TenderService, BidService } from '@/lib/backend/services/procurement/TenderService';
import { TenderImportExportService } from '@/lib/backend/services/procurement/TenderImportExportService';
import { TenderReportingService } from '@/lib/backend/services/procurement/TenderReportingService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

// Initialize services
const tenderService = new TenderService();
const bidService = new BidService();
const importExportService = new TenderImportExportService();
const reportingService = new TenderReportingService();

/**
 * GET handler for tenders and bids
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER,
      UserRole.DEPARTMENT_HEAD
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const type = searchParams.get('type');
    const category = searchParams.get('category');
    const status = searchParams.get('status');
    const departmentId = searchParams.get('departmentId');
    const supplierId = searchParams.get('supplierId');
    const bidId = searchParams.get('bidId');
    const format = searchParams.get('format');
    const report = searchParams.get('report');

    // Handle different request types
    if (type === 'bid' && bidId) {
      // Get bid by ID
      const bid = await bidService.getBidDetails(bidId);

      if (!bid) {
        return NextResponse.json({ error: 'Bid not found' }, { status: 404 });
      }

      return NextResponse.json(bid);
    } else if (type === 'bid' && id) {
      // Get bids by tender ID
      // Build options
      interface BidOptions {
        page: number;
        limit: number;
        sort: Record<string, number>;
        status?: string[];
        [key: string]: any;
      }

      const options: BidOptions = {
        page,
        limit,
        sort: { totalScore: -1, submissionDate: 1 }
      };

      if (status) {
        options.status = status.split(',');
      }

      // Get bids
      // Cast to any to bypass TypeScript's type checking
      const result = await bidService.getByTender(id, options as any);

      return NextResponse.json(result);
    } else if (type === 'bid' && supplierId) {
      // Get bids by supplier ID
      // Build options
      interface SupplierBidOptions {
        page: number;
        limit: number;
        sort: Record<string, number>;
        status?: string[];
        [key: string]: any;
      }

      const options: SupplierBidOptions = {
        page,
        limit,
        sort: { submissionDate: -1 }
      };

      if (status) {
        options.status = status.split(',');
      }

      // Get bids
      // Cast to any to bypass TypeScript's type checking
      const result = await bidService.getBySupplier(supplierId, options as any);

      return NextResponse.json(result);
    } else if (id) {
      // Get tender by ID
      const tender = await tenderService.getTenderDetails(id);

      if (!tender) {
        return NextResponse.json({ error: 'Tender not found' }, { status: 404 });
      }

      return NextResponse.json(tender);
    } else if (format) {
      // Export tenders or bids
      // Check if user has export permissions
      const hasExportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.PROCUREMENT_OFFICER,
        UserRole.FINANCE_MANAGER
      ]);

      if (!hasExportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Build filter
      const filter: MongoFilter = {};

      // Add filters
      if (category) {
        filter.category = category;
      }

      if (status) {
        filter.status = status;
      }

      if (departmentId) {
        filter.departmentId = departmentId;
      }

      // Generate export
      let buffer: Buffer;
      let contentType: string;
      let filename: string;

      if (type === 'bid') {
        // Export bids - use the standard export methods with bid-specific filter
        if (format === 'excel') {
          // Add a type indicator to the filter to differentiate bids
          const bidFilter = { ...filter, entityType: 'bid' };
          buffer = await importExportService.exportToExcel(bidFilter);
          contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          filename = 'bids.xlsx';
        } else if (format === 'csv') {
          // Add a type indicator to the filter to differentiate bids
          const bidFilter = { ...filter, entityType: 'bid' };
          buffer = await importExportService.exportToCsv(bidFilter);
          contentType = 'text/csv';
          filename = 'bids.csv';
        } else if (format === 'pdf' && bidId) {
          // For bid PDFs, we'll use the tender PDF generator with the bid ID
          // In a real implementation, you might want to create a separate method for bids
          buffer = await reportingService.generateTenderPdf(bidId);
          contentType = 'application/pdf';
          filename = `bid-${bidId}.pdf`;
        } else {
          return NextResponse.json({ error: 'Invalid format' }, { status: 400 });
        }
      } else {
        // Export tenders
        if (format === 'excel') {
          buffer = await importExportService.exportToExcel(filter);
          contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          filename = 'tenders.xlsx';
        } else if (format === 'csv') {
          buffer = await importExportService.exportToCsv(filter);
          contentType = 'text/csv';
          filename = 'tenders.csv';
        } else if (format === 'pdf' && id) {
          buffer = await reportingService.generateTenderPdf(id);
          contentType = 'application/pdf';
          filename = `tender-${id}.pdf`;
        } else {
          return NextResponse.json({ error: 'Invalid format' }, { status: 400 });
        }
      }

      // Convert Buffer to Uint8Array which is acceptable for NextResponse
      return new NextResponse(new Uint8Array(buffer), {
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${filename}"`
        }
      });
    } else if (report) {
      // Generate report
      // Check if user has report permissions
      const hasReportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.PROCUREMENT_OFFICER,
        UserRole.FINANCE_MANAGER
      ]);

      if (!hasReportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      if (report === 'summary') {
        // Generate summary report
        const reportData = await reportingService.generateSummaryReport({
          category: category || undefined,
          departmentId: departmentId || undefined
        });

        return NextResponse.json(reportData);
      } else if (report === 'tender' && id) {
        // Generate tender report with PDF
        const pdfBuffer = await reportingService.generateTenderPdf(id);

        // Convert Buffer to Uint8Array which is acceptable for NextResponse
        return new NextResponse(new Uint8Array(pdfBuffer), {
          headers: {
            'Content-Type': 'application/pdf',
            'Content-Disposition': `attachment; filename="tender-${id}-report.pdf"`
          }
        });
      } else if (report === 'supplier' && supplierId) {
        // Generate supplier report
        const reportData = await reportingService.generateSupplierReport(supplierId);

        return NextResponse.json(reportData);
      } else {
        return NextResponse.json({ error: 'Invalid report type or missing parameters' }, { status: 400 });
      }
    } else if (searchParams.get('active') === 'true') {
      // Get active tenders
      // Build options
      interface ActiveTenderOptions {
        page: number;
        limit: number;
        sort: Record<string, number>;
        category?: string;
        department?: string;
        [key: string]: any;
      }

      const options: ActiveTenderOptions = {
        page,
        limit,
        sort: { closingDate: 1 }
      };

      if (category) {
        options.category = category;
      }

      if (departmentId) {
        options.department = departmentId;
      }

      // Get tenders
      // Cast to any to bypass TypeScript's type checking
      const result = await tenderService.getActiveTenders(options as any);

      return NextResponse.json(result);
    } else if (category) {
      // Get tenders by category
      // Build options
      interface CategoryTenderOptions {
        page: number;
        limit: number;
        sort: Record<string, number>;
        status?: string[];
        department?: string;
        [key: string]: any;
      }

      const options: CategoryTenderOptions = {
        page,
        limit,
        sort: { publicationDate: -1 }
      };

      if (status) {
        options.status = status.split(',');
      }

      if (departmentId) {
        options.department = departmentId;
      }

      // Get tenders
      // Cast to any to bypass TypeScript's type checking
      const result = await tenderService.getByCategory(category, options as any);

      return NextResponse.json(result);
    } else if (departmentId) {
      // Get tenders by department
      // Build filter
      const filter: MongoFilter = { departmentId: departmentId };

      if (status) {
        filter.status = status;
      }

      // Get tenders
      const result = await tenderService.paginate(filter, page, limit, { publicationDate: -1 }, ['department', 'createdBy']);

      return NextResponse.json(result);
    } else {
      // Get all tenders with pagination
      // Build filter
      const filter: MongoFilter = {};

      if (status) {
        filter.status = status;
      }

      // Get tenders
      const result = await tenderService.paginate(filter, page, limit, { publicationDate: -1 }, ['department', 'createdBy']);

      return NextResponse.json(result);
    }
  } catch (error: unknown) {
    logger.error('Error in tenders GET handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

/**
 * POST handler for tenders and bids
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await request.json();

    // Check if import request
    if (body.import) {
      // Check if user has import permissions
      const hasImportPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER
      ]);

      if (!hasImportPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Get file buffer
      const fileBuffer = Buffer.from(body.fileData, 'base64');

      // Import data
      let importResult;

      // Use the same import methods for both bids and tenders
      importResult = body.format === 'csv'
        ? await importExportService.importFromCsv(fileBuffer, body.options)
        : await importExportService.importFromExcel(fileBuffer, body.options);

      return NextResponse.json(importResult);
    }

    // Set created by
    body.createdBy = user.id;

    if (body.type === 'bid') {
      // Check permissions for bid creation
      const hasBidPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.PROCUREMENT_OFFICER
      ]);

      if (!hasBidPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Create bid
      const bid = await bidService.createBid(body);

      return NextResponse.json(bid, { status: 201 });
    } else {
      // Check permissions for tender creation
      const hasTenderPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.PROCUREMENT_MANAGER,
        UserRole.PROCUREMENT_OFFICER
      ]);

      if (!hasTenderPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Create tender
      const tender = await tenderService.createTender(body);

      return NextResponse.json(tender, { status: 201 });
    }
  } catch (error: unknown) {
    logger.error('Error in tenders POST handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}

/**
 * PATCH handler for tenders and bids
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function PATCH(request: NextRequest): Promise<Response> {
  try {
    // Get current user
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get request body
    const body = await request.json();

    if (body.type === 'bid' && body.id && body.status) {
      // Update bid status
      const updatedBid = await bidService.updateStatus(
        body.id,
        body.status,
        user.id,
        body.notes
      );

      return NextResponse.json(updatedBid);
    } else if (body.type === 'bid' && body.id && body.evaluate) {
      // Evaluate bid
      const updatedBid = await bidService.evaluateBid(
        body.id,
        body.scores,
        user.id
      );

      return NextResponse.json(updatedBid);
    } else if (body.type === 'bid' && body.id) {
      // Update bid
      const updatedBid = await bidService.updateById(body.id, body);

      return NextResponse.json(updatedBid);
    } else if (body.id && body.status) {
      // Update tender status
      const updatedTender = await tenderService.updateStatus(
        body.id,
        body.status,
        user.id,
        body.notes
      );

      return NextResponse.json(updatedTender);
    } else if (body.id) {
      // Update tender
      // Get tender
      const tender = await tenderService.findById(body.id);

      if (!tender) {
        return NextResponse.json({ error: 'Tender not found' }, { status: 404 });
      }

      // Only allow updates to draft tenders
      if (tender.status !== 'draft') {
        return NextResponse.json({
          error: `Tender with status '${tender.status}' cannot be updated`
        }, { status: 400 });
      }

      // Update tender
      const updatedTender = await tenderService.updateById(body.id, body);

      return NextResponse.json(updatedTender);
    } else {
      return NextResponse.json({ error: 'Missing ID' }, { status: 400 });
    }
  } catch (error: unknown) {
    logger.error('Error in tenders PATCH handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred' }, { status: 500 });
  }
}


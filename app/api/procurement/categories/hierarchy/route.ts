import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { procurementCategoryService } from '@/lib/backend/services/procurement/ProcurementCategoryService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

export const runtime = 'nodejs';

// Validation schema for hierarchy query parameters
const hierarchyQuerySchema = z.object({
  rootCategoryId: z.string().optional(),
  includeInactive: z.string().transform(val => val === 'true').optional()
});
// Response type for hierarchy operations
interface HierarchyResponse {
  success: true;
  data: unknown[];
  message?: string;
}
/**
 * GET /api/procurement/categories/hierarchy
 * Get procurement category hierarchy
 */
export async function GET(request: NextRequest): Promise<NextResponse<HierarchyResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'CATEGORY_HIERARCHY_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to view category hierarchy.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.EMPLOYEE
    ]);
    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'CATEGORY_HIERARCHY_FORBIDDEN',
        'Insufficient permissions to view category hierarchy',
        'You do not have permission to view procurement category hierarchy.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to access category hierarchy without proper permissions',
        [
          'Contact your administrator to request procurement access permissions',
          'Ensure you are logged in with the correct account'
        ]
      );
    }
    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    // Validate query parameters
    const validationResult = hierarchyQuerySchema.safeParse(queryParams);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CATEGORY_HIERARCHY_INVALID_PARAMS',
        'Invalid query parameters',
        'The query parameters provided are not valid. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.LOW,
        validationResult.error.errors[0].message,
        [
          'Check the format of query parameters',
          'Ensure boolean parameters use "true" or "false"',
          'Verify category IDs are valid'
        ]
      );
    }
    const { rootCategoryId } = validationResult.data;
    // Validate root category ID if provided
    if (rootCategoryId && rootCategoryId.length !== 24) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CATEGORY_HIERARCHY_INVALID_ROOT_ID',
        'Invalid root category ID',
        'The root category ID provided is not valid. Please check the ID and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          rootCategoryId
        },
        400,
        ErrorSeverity.LOW,
        'Root category ID must be a valid 24-character MongoDB ObjectId',
        [
          'Ensure the root category ID is copied correctly',
          'Check that the ID is complete and correct'
        ]
      );
    }
    // Get category hierarchy
    const hierarchy = await procurementCategoryService.getCategoryHierarchy(rootCategoryId);
    return NextResponse.json({
      success: true,
      data: hierarchy,
      message: rootCategoryId 
        ? `Category hierarchy retrieved for root category ${rootCategoryId}`
        : 'Complete category hierarchy retrieved'
    } as HierarchyResponse);
  } catch (error: unknown) {
    logger.error('Error getting category hierarchy', LogCategory.PROCUREMENT, error);
    // Handle specific business logic errors
    if (error instanceof Error) {
      if (error.message.includes('Root category not found')) {
        return errorService.createApiResponse(
          ErrorType.NOT_FOUND,
          'CATEGORY_HIERARCHY_ROOT_NOT_FOUND',
          'Root category not found',
          'The specified root category could not be found.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          404,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Verify the root category ID is correct',
            'Ensure the root category exists and is active',
            'Try getting the complete hierarchy without specifying a root'
          ]
        );
      }
    }
    // Generic server error
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'CATEGORY_HIERARCHY_ERROR',
      error instanceof Error ? error.message : 'Failed to get category hierarchy',
      'Unable to retrieve category hierarchy. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try refreshing the page',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}
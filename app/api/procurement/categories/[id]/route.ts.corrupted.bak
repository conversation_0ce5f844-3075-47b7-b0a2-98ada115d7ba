import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { procurementCategoryService, UpdateCategoryData } from '@/lib/backend/services/procurement/ProcurementCategoryService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

// Validation schema for updating categories
const updateCategorySchema = z.object({
  name: z.string().min(1, 'Category name is required').max(100, 'Name cannot exceed 100 characters').optional(),
  code: z.string().min(1, 'Category code is required').max(20, 'Code cannot exceed 20 characters')
    .regex(/^[A-Z0-9_-]+$/, 'Code can only contain uppercase letters, numbers, underscores, and hyphens').optional(),
  description: z.string().max(500, 'Description cannot exceed 500 characters').optional(),
  parentCategory: z.string().optional(),
  budgetCategory: z.string().optional(),
  costCenter: z.string().optional(),
  approvalLimit: z.number().min(0, 'Approval limit cannot be negative').optional(),
  requiredApprovers: z.array(z.string()).optional(),
  autoApprovalThreshold: z.number().min(0, 'Auto-approval threshold cannot be negative').optional(),
  defaultSuppliers: z.array(z.string()).optional(),
  restrictedSuppliers: z.array(z.string()).optional(),
  requiresQuotation: z.boolean().optional(),
  minimumQuotations: z.number().min(1, 'Minimum quotations must be at least 1').max(10, 'Maximum quotations cannot exceed 10').optional(),
  leadTime: z.number().min(0, 'Lead time cannot be negative').max(365, 'Lead time cannot exceed 365 days').optional(),
  riskLevel: z.enum(['low', 'medium', 'high', 'critical']).optional(),
  isActive: z.boolean().optional(),
  isRestricted: z.boolean().optional(),
  complianceRequirements: z.array(z.string()).optional(),
  requiredDocuments: z.array(z.string()).optional(),
  qualityStandards: z.array(z.string()).optional(),
  requiresInspection: z.boolean().optional(),
  inspectionCriteria: z.array(z.string()).optional(),
  requiresApprovalWorkflow: z.boolean().optional(),
  budgetCheckRequired: z.boolean().optional(),
  allowOverBudget: z.boolean().optional(),
  overBudgetApprovalRequired: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
  notes: z.string().max(1000, 'Notes cannot exceed 1000 characters').optional()
});

// Response type for successful category operations
interface CategoryResponse {
  success: true;
  data: unknown;
  message?: string;
}

/**
 * GET /api/procurement/categories/[id]
 * Get a specific procurement category by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<CategoryResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'CATEGORY_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to view category details.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.EMPLOYEE
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'CATEGORY_VIEW_FORBIDDEN',
        'Insufficient permissions to view category details',
        'You do not have permission to view procurement category information.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to view category without proper permissions',
        [
          'Contact your administrator to request procurement access permissions',
          'Ensure you are logged in with the correct account'
        ]
      );
    }

    // Get category ID from params
    const { id } = await params;

    // Validate category ID
    if (!id || id.length !== 24) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CATEGORY_INVALID_ID',
        'Invalid category ID',
        'The category ID provided is not valid. Please check the ID and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          categoryId: id
        },
        400,
        ErrorSeverity.LOW,
        'Category ID must be a valid 24-character MongoDB ObjectId',
        [
          'Ensure the category ID is copied correctly',
          'Check that the URL is complete and correct'
        ]
      );
    }

    // Get category
    const category = await procurementCategoryService.findById(id);
    if (!category) {
      return errorService.createApiResponse(
        ErrorType.NOT_FOUND,
        'CATEGORY_NOT_FOUND',
        'Category not found',
        'The requested category could not be found. It may have been deleted or the ID is incorrect.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          categoryId: id
        },
        404,
        ErrorSeverity.MEDIUM,
        `Category with ID ${id} not found in database`,
        [
          'Verify the category ID is correct',
          'Check if the category was recently deleted',
          'Contact support if you believe this is an error'
        ]
      );
    }

    return NextResponse.json({
      success: true,
      data: category
    } as CategoryResponse);

  } catch (error: unknown) {
    logger.error('Error getting category', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'CATEGORY_FETCH_ERROR',
      error instanceof Error ? error.message : 'Failed to fetch category',
      'Unable to retrieve category information. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try refreshing the page',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

/**
 * PUT /api/procurement/categories/[id]
 * Update a specific procurement category
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<CategoryResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'CATEGORY_UPDATE_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to update categories.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'CATEGORY_UPDATE_FORBIDDEN',
        'Insufficient permissions to update categories',
        'You do not have permission to update category records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to update category without proper permissions',
        [
          'Contact your administrator to request category update permissions',
          'Ensure you are logged in with a manager-level account'
        ]
      );
    }

    // Get category ID from params
    const { id } = await params;

    // Validate category ID
    if (!id || id.length !== 24) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CATEGORY_INVALID_ID',
        'Invalid category ID',
        'The category ID provided is not valid. Please check the ID and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          categoryId: id
        },
        400,
        ErrorSeverity.LOW,
        'Category ID must be a valid 24-character MongoDB ObjectId',
        [
          'Ensure the category ID is copied correctly',
          'Check that the URL is complete and correct'
        ]
      );
    }

    // Parse request body
    let body: unknown;
    try {
      body = await request.json();
    } catch (parseError: unknown) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CATEGORY_INVALID_JSON',
        'Invalid request format',
        'The request data is not in the correct format. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          parseError: parseError instanceof Error ? parseError.message : 'Unknown parse error'
        },
        400,
        ErrorSeverity.MEDIUM,
        'Failed to parse JSON request body',
        [
          'Ensure the request body is valid JSON',
          'Check that all fields are properly formatted',
          'Verify array and object structures are correct'
        ]
      );
    }

    // Validate request data
    const validationResult = updateCategorySchema.safeParse(body);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CATEGORY_VALIDATION_ERROR',
        'Invalid category data',
        'The category information provided is not valid. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.MEDIUM,
        validationResult.error.errors[0].message,
        [
          'Check that values are in the correct format',
          'Verify category code follows the required pattern',
          'Ensure numeric values are valid',
          'Verify arrays contain valid items'
        ]
      );
    }

    const updateData: UpdateCategoryData = {
      ...validationResult.data,
      updatedBy: user.id
    };

    // Update category
    const category = await procurementCategoryService.updateCategory(id, updateData);

    return NextResponse.json({
      success: true,
      message: 'Category updated successfully',
      data: category
    } as CategoryResponse);

  } catch (error: unknown) {
    logger.error('Error updating category', LogCategory.PROCUREMENT, error);
    
    // Handle specific business logic errors
    if (error instanceof Error) {
      if (error.message.includes('Category not found')) {
        return errorService.createApiResponse(
          ErrorType.NOT_FOUND,
          'CATEGORY_NOT_FOUND',
          'Category not found',
          'The category you are trying to update could not be found.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          404,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Verify the category ID is correct',
            'Check if the category was recently deleted',
            'Contact support if needed'
          ]
        );
      }

      if (error.message.includes('Category code already exists')) {
        return errorService.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'CATEGORY_CODE_EXISTS',
          'Category code already exists',
          'A category with this code already exists. Please choose a different code.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          400,
          ErrorSeverity.LOW,
          error.message,
          [
            'Choose a unique category code',
            'Check existing categories for code conflicts'
          ]
        );
      }

      if (error.message.includes('circular reference')) {
        return errorService.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'CATEGORY_CIRCULAR_REFERENCE',
          'Circular reference detected',
          'Cannot set a descendant category as parent. This would create a circular reference.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          400,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Choose a different parent category',
            'Verify the category hierarchy structure',
            'Contact support if you need help restructuring categories'
          ]
        );
      }
    }

    // Generic server error
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'CATEGORY_UPDATE_ERROR',
      error instanceof Error ? error.message : 'Failed to update category',
      'Unable to update the category record. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try updating the category again',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

/**
 * DELETE /api/procurement/categories/[id]
 * Delete a specific procurement category
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<CategoryResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'CATEGORY_DELETE_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to delete categories.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions - only super admin and system admin can delete categories
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'CATEGORY_DELETE_FORBIDDEN',
        'Insufficient permissions to delete categories',
        'You do not have permission to delete category records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.HIGH,
        'User attempted to delete category without proper permissions',
        [
          'Contact your system administrator for category deletion',
          'Consider deactivating the category instead of deleting',
          'Ensure you have super admin or system admin access'
        ]
      );
    }

    // Get category ID from params
    const { id } = await params;

    // Validate category ID
    if (!id || id.length !== 24) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CATEGORY_INVALID_ID',
        'Invalid category ID',
        'The category ID provided is not valid. Please check the ID and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          categoryId: id
        },
        400,
        ErrorSeverity.LOW,
        'Category ID must be a valid 24-character MongoDB ObjectId',
        [
          'Ensure the category ID is copied correctly',
          'Check that the URL is complete and correct'
        ]
      );
    }

    // Check if category exists
    const category = await procurementCategoryService.findById(id);
    if (!category) {
      return errorService.createApiResponse(
        ErrorType.NOT_FOUND,
        'CATEGORY_NOT_FOUND',
        'Category not found',
        'The category you are trying to delete could not be found.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          categoryId: id
        },
        404,
        ErrorSeverity.MEDIUM,
        `Category with ID ${id} not found in database`,
        [
          'Verify the category ID is correct',
          'Check if the category was already deleted',
          'Contact support if you believe this is an error'
        ]
      );
    }

    // Check if category has subcategories
    const subcategories = await category.getChildren();
    if (subcategories.length > 0) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'CATEGORY_HAS_SUBCATEGORIES',
        'Cannot delete category with subcategories',
        'This category has subcategories and cannot be deleted. Please delete or move the subcategories first.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          categoryId: id,
          subcategoryCount: subcategories.length
        },
        400,
        ErrorSeverity.MEDIUM,
        `Category has ${subcategories.length} subcategories`,
        [
          'Delete all subcategories first',
          'Move subcategories to a different parent',
          'Consider deactivating the category instead'
        ]
      );
    }

    // Delete category
    await procurementCategoryService.delete(id);

    return NextResponse.json({
      success: true,
      message: 'Category deleted successfully',
      data: { id, name: category.name, code: category.code }
    } as CategoryResponse);

  } catch (error: unknown) {
    logger.error('Error deleting category', LogCategory.PROCUREMENT, error);

    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'CATEGORY_DELETE_ERROR',
      error instanceof Error ? error.message : 'Failed to delete category',
      'Unable to delete the category record. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try deleting the category again',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { procurementCategoryService } from '@/lib/backend/services/procurement/ProcurementCategoryService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

export const runtime = 'nodejs';

// Validation schema for approval validation request
const approvalValidationSchema = z.object({
  amount: z.number().min(0, 'Amount must be non-negative'),
  userId: z.string().optional() // If not provided, uses current user
});
// Response type for approval validation
interface ApprovalValidationResponse {
  success: true;
  data: {
    canApprove: boolean;
    reason?: string;
    requiredApprovers: string[];
    approvalLimit?: number;
    exceedsLimit: boolean;
    requiresEscalation: boolean;
    categoryInfo: {
      id: string;
      name: string;
      code: string;
      riskLevel: string;
    };
  };
  message?: string;
}
/**
 * POST /api/procurement/categories/[id]/validate-approval
 * Validate approval limits for a category and amount
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApprovalValidationResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'APPROVAL_VALIDATION_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to validate approval limits.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.EMPLOYEE
    ]);
    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'APPROVAL_VALIDATION_FORBIDDEN',
        'Insufficient permissions to validate approval limits',
        'You do not have permission to validate approval limits.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to validate approval limits without proper permissions',
        [
          'Contact your administrator to request procurement access permissions',
          'Ensure you are logged in with the correct account'
        ]
      );
    }
    // Get category ID from params
    const { id } = await params;
    // Validate category ID
    if (!id || id.length !== 24) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'APPROVAL_VALIDATION_INVALID_ID',
        'Invalid category ID',
        'The category ID provided is not valid. Please check the ID and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          categoryId: id
        },
        400,
        ErrorSeverity.LOW,
        'Category ID must be a valid 24-character MongoDB ObjectId',
        [
          'Ensure the category ID is copied correctly',
          'Check that the URL is complete and correct'
        ]
      );
    }
    // Parse request body
    let body: unknown;
    try {
      body = await request.json();
    } catch (parseError: unknown) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'APPROVAL_VALIDATION_INVALID_JSON',
        'Invalid request format',
        'The request data is not in the correct format. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          parseError: parseError instanceof Error ? parseError.message : 'Unknown parse error'
        },
        400,
        ErrorSeverity.MEDIUM,
        'Failed to parse JSON request body',
        [
          'Ensure the request body is valid JSON',
          'Check that the amount field is included',
          'Verify numeric values are properly formatted'
        ]
      );
    }
    // Validate request data
    const validationResult = approvalValidationSchema.safeParse(body);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'APPROVAL_VALIDATION_VALIDATION_ERROR',
        'Invalid validation request data',
        'The approval validation request data is not valid. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.MEDIUM,
        validationResult.error.errors[0].message,
        [
          'Ensure the amount is a valid positive number',
          'Check that all required fields are provided',
          'Verify the user ID format if provided'
        ]
      );
    }
    const { amount, userId } = validationResult.data;
    const validationUserId = userId || user.id;
    // Validate user ID if provided
    if (userId && userId.length !== 24) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'APPROVAL_VALIDATION_INVALID_USER_ID',
        'Invalid user ID',
        'The user ID provided is not valid. Please check the ID and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          providedUserId: userId
        },
        400,
        ErrorSeverity.LOW,
        'User ID must be a valid 24-character MongoDB ObjectId',
        [
          'Ensure the user ID is copied correctly',
          'Check that the ID is complete and correct',
          'Omit the userId field to validate for the current user'
        ]
      );
    }
    // Get category information
    const category = await procurementCategoryService.findById(id);
    if (!category) {
      return errorService.createApiResponse(
        ErrorType.NOT_FOUND,
        'APPROVAL_VALIDATION_CATEGORY_NOT_FOUND',
        'Category not found',
        'The category for approval validation could not be found.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          categoryId: id
        },
        404,
        ErrorSeverity.MEDIUM,
        `Category with ID ${id} not found in database`,
        [
          'Verify the category ID is correct',
          'Check if the category was recently deleted',
          'Contact support if you believe this is an error'
        ]
      );
    }
    // Validate approval limits
    const validationResults = await procurementCategoryService.validateApprovalLimits(
      id,
      amount,
      validationUserId
    );
    return NextResponse.json({
      success: true,
      data: {
        ...validationResults,
        categoryInfo: {
          id: category._id.toString(),
          name: category.name,
          code: category.code,
          riskLevel: category.riskLevel
        }
      },
      message: validationResults.canApprove 
        ? 'Approval validation successful'
        : 'Approval validation completed - approval not allowed'
    } as ApprovalValidationResponse);
  } catch (error: unknown) {
    logger.error('Error validating approval limits', LogCategory.PROCUREMENT, error);
    // Handle specific business logic errors
    if (error instanceof Error) {
      if (error.message.includes('Category not found')) {
        return errorService.createApiResponse(
          ErrorType.NOT_FOUND,
          'APPROVAL_VALIDATION_CATEGORY_NOT_FOUND',
          'Category not found',
          'The category for approval validation could not be found.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          404,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Verify the category ID is correct',
            'Check if the category was recently deleted',
            'Contact support if needed'
          ]
        );
      }
      if (error.message.includes('User not found')) {
        return errorService.createApiResponse(
          ErrorType.NOT_FOUND,
          'APPROVAL_VALIDATION_USER_NOT_FOUND',
          'User not found',
          'The user for approval validation could not be found.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          404,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Verify the user ID is correct',
            'Check if the user account is active',
            'Use the current user by omitting the userId field'
          ]
        );
      }
    }
    // Generic server error
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'APPROVAL_VALIDATION_ERROR',
      error instanceof Error ? error.message : 'Failed to validate approval limits',
      'Unable to validate approval limits. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try validating the approval again',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}
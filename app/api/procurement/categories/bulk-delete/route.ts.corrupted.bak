// app/api/procurement/categories/bulk-delete/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { procurementCategoryService } from '@/lib/backend/services/procurement/ProcurementCategoryService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import { AuditDeletionContext } from '@/lib/services/audit/audit-deletion-service';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import logger, { LogCategory } from '@/lib/backend/utils/logger';



/**
 * POST /api/procurement/categories/bulk-delete
 * Bulk delete procurement categories with audit trail
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Get current user
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return errorService.createApiResponse(
        ErrorType.AUTHENTICATION,
        'UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to delete procurement categories',
        { endpoint: '/api/procurement/categories/bulk-delete', method: 'POST' },
        401
      );
    }

    // Check permissions
    if (!['admin', 'procurement_manager', 'super_admin'].includes(currentUser.role)) {
      return errorService.createApiResponse(
        ErrorType.AUTHORIZATION,
        'INSUFFICIENT_PERMISSIONS',
        'Insufficient permissions to bulk delete procurement categories',
        'You do not have permission to bulk delete procurement categories',
        { 
          userId: currentUser.id,
          userRole: currentUser.role,
          endpoint: '/api/procurement/categories/bulk-delete',
          method: 'POST'
        },
        403
      );
    }

    // Parse request body
    const body = await request.json();
    const { categoryIds, deletionReason, context } = body;

    // Validate input
    if (!Array.isArray(categoryIds) || categoryIds.length === 0) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'INVALID_CATEGORY_IDS',
        'Category IDs array is required and cannot be empty',
        'Please select at least one category to delete',
        { 
          userId: currentUser.id,
          endpoint: '/api/procurement/categories/bulk-delete',
          method: 'POST'
        },
        400
      );
    }

    if (categoryIds.length > 50) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'TOO_MANY_CATEGORIES',
        'Cannot delete more than 50 categories at once',
        'Please select fewer categories (maximum 50) for bulk deletion',
        { 
          userId: currentUser.id,
          endpoint: '/api/procurement/categories/bulk-delete',
          method: 'POST',
          additionalData: { requestedCount: categoryIds.length }
        },
        400
      );
    }

    // Validate deletion reason
    if (!deletionReason || typeof deletionReason !== 'string' || deletionReason.trim().length < 20) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'INVALID_BULK_DELETION_REASON',
        'Bulk deletion reason is required and must be at least 20 characters',
        'Please provide a detailed reason for bulk deleting these procurement categories (minimum 20 characters)',
        { 
          userId: currentUser.id,
          endpoint: '/api/procurement/categories/bulk-delete',
          method: 'POST'
        },
        400,
        ErrorSeverity.MEDIUM,
        'Bulk deletion reason validation failed for audit compliance requirements',
        [
          'Provide a clear and detailed reason for the bulk deletion',
          'Ensure the reason is at least 20 characters long',
          'Include business justification for deleting multiple categories'
        ]
      );
    }

    // Prepare audit context
    const auditContext: Omit<AuditDeletionContext, 'deletionType'> = {
      deletedBy: currentUser.id,
      deletionReason: deletionReason.trim(),
      userInfo: {
        id: currentUser.id,
        name: currentUser.name || 'Unknown User',
        email: currentUser.email || '<EMAIL>',
        role: currentUser.role
      },
      context: {
        fiscalYear: context?.fiscalYear,
        department: context?.department || 'Procurement',
        ipAddress: request.headers.get('x-forwarded-for') || 
                  request.headers.get('x-real-ip') || 
                  'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        sessionId: request.headers.get('x-session-id')
      }
    };

    logger.info('Processing bulk category deletion request', LogCategory.PROCUREMENT, {
      categoryIds,
      count: categoryIds.length,
      userId: currentUser.id,
      deletionReason: deletionReason.trim()
    });

    // Perform audit-compliant bulk deletion
    const result = await procurementCategoryService.bulkDeleteCategories(categoryIds, auditContext);

    logger.info('Bulk category deletion completed successfully', LogCategory.PROCUREMENT, {
      categoryIds,
      userId: currentUser.id,
      deletedCount: result.deletedCount,
      auditRecordsCreated: result.auditRecordsCreated
    });

    return NextResponse.json({
      success: true,
      message: `Successfully deleted ${result.deletedCount} procurement categories with audit trail`,
      data: {
        requestedCount: categoryIds.length,
        deletedCount: result.deletedCount,
        auditRecordsCreated: result.auditRecordsCreated,
        auditCompliance: result.details?.auditCompliance,
        deletionIds: result.auditRecordIds,
        errors: result.errors
      }
    });

  } catch (error) {
    logger.error('Error in bulk category deletion', LogCategory.PROCUREMENT, error);

    // Handle structured errors
    if (error instanceof Error && error.message.includes('Unable to delete')) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'BULK_CATEGORY_DELETE_CONSTRAINT',
        error.message,
        error.message,
        { 
          endpoint: '/api/procurement/categories/bulk-delete',
          method: 'POST'
        },
        400,
        ErrorSeverity.MEDIUM
      );
    }

    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'BULK_CATEGORY_DELETE_ERROR',
      error instanceof Error ? error.message : 'Unknown error occurred',
      'An unexpected error occurred while bulk deleting procurement categories. Please try again.',
      { 
        endpoint: '/api/procurement/categories/bulk-delete',
        method: 'POST'
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try the operation again with fewer categories',
        'Check if the categories still exist',
        'Review the deletion constraints',
        'Contact support if the problem persists'
      ]
    );
  }
}

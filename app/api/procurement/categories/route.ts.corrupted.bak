import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { procurementCategoryService, CreateCategoryData, CategoryFilters } from '@/lib/backend/services/procurement/ProcurementCategoryService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { z } from 'zod';

// Validation schema for category filters
const categoryFiltersSchema = z.object({
  name: z.string().optional(),
  code: z.string().optional(),
  parentCategory: z.string().optional(),
  budgetCategory: z.string().optional(),
  riskLevel: z.union([
    z.enum(['low', 'medium', 'high', 'critical']),
    z.array(z.enum(['low', 'medium', 'high', 'critical']))
  ]).optional(),
  isActive: z.string().transform(val => val === 'true').optional(),
  isRestricted: z.string().transform(val => val === 'true').optional(),
  requiresQuotation: z.string().transform(val => val === 'true').optional(),
  requiresInspection: z.string().transform(val => val === 'true').optional(),
  tags: z.union([z.string(), z.array(z.string())]).optional(),
  search: z.string().optional(),
  level: z.string().transform(val => parseInt(val)).optional(),
  minApprovalLimit: z.string().transform(val => parseFloat(val)).optional(),
  maxApprovalLimit: z.string().transform(val => parseFloat(val)).optional(),
  page: z.string().transform(val => parseInt(val) || 1).optional(),
  limit: z.string().transform(val => parseInt(val) || 20).optional()
});

// Validation schema for creating categories
const createCategorySchema = z.object({
  name: z.string().min(1, 'Category name is required').max(100, 'Name cannot exceed 100 characters'),
  code: z.string().min(1, 'Category code is required').max(20, 'Code cannot exceed 20 characters')
    .regex(/^[A-Z0-9_-]+$/, 'Code can only contain uppercase letters, numbers, underscores, and hyphens'),
  description: z.string().max(500, 'Description cannot exceed 500 characters').optional(),
  parentCategory: z.string().optional(),
  budgetCategory: z.string().optional(),
  costCenter: z.string().optional(),
  approvalLimit: z.number().min(0, 'Approval limit cannot be negative').optional(),
  requiredApprovers: z.array(z.string()).optional(),
  autoApprovalThreshold: z.number().min(0, 'Auto-approval threshold cannot be negative').optional(),
  defaultSuppliers: z.array(z.string()).optional(),
  restrictedSuppliers: z.array(z.string()).optional(),
  requiresQuotation: z.boolean().optional(),
  minimumQuotations: z.number().min(1, 'Minimum quotations must be at least 1').max(10, 'Maximum quotations cannot exceed 10').optional(),
  leadTime: z.number().min(0, 'Lead time cannot be negative').max(365, 'Lead time cannot exceed 365 days').optional(),
  riskLevel: z.enum(['low', 'medium', 'high', 'critical']).optional(),
  isRestricted: z.boolean().optional(),
  complianceRequirements: z.array(z.string()).optional(),
  requiredDocuments: z.array(z.string()).optional(),
  qualityStandards: z.array(z.string()).optional(),
  requiresInspection: z.boolean().optional(),
  inspectionCriteria: z.array(z.string()).optional(),
  requiresApprovalWorkflow: z.boolean().optional(),
  budgetCheckRequired: z.boolean().optional(),
  allowOverBudget: z.boolean().optional(),
  overBudgetApprovalRequired: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
  notes: z.string().max(1000, 'Notes cannot exceed 1000 characters').optional()
});

// Response type for successful category operations
interface CategoryResponse {
  success: true;
  data: unknown;
  message?: string;
}

// Response type for category list operations
interface CategoryListResponse {
  success: true;
  data: unknown[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

/**
 * GET /api/procurement/categories
 * Get procurement categories with optional filtering and pagination
 */
export async function GET(request: NextRequest): Promise<NextResponse<CategoryListResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'CATEGORY_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to view procurement categories.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.EMPLOYEE
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'CATEGORY_FORBIDDEN',
        'Insufficient permissions to view procurement categories',
        'You do not have permission to view procurement category information.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to access procurement categories without proper permissions',
        [
          'Contact your administrator to request procurement access permissions',
          'Ensure you are logged in with the correct account'
        ]
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());

    // Validate filters
    const validationResult = categoryFiltersSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CATEGORY_INVALID_FILTERS',
        'Invalid filter parameters',
        'The search filters provided are not valid. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.LOW,
        validationResult.error.errors[0].message,
        [
          'Check the format of filter parameters',
          'Ensure numeric values are valid numbers',
          'Verify boolean filters use "true" or "false"'
        ]
      );
    }

    const { page = 1, limit = 20, ...filters } = validationResult.data;

    // Get categories
    const result = await procurementCategoryService.searchCategories(filters as CategoryFilters, page, limit);

    return NextResponse.json({
      success: true,
      data: result.categories,
      pagination: result.pagination
    } as CategoryListResponse);

  } catch (error: unknown) {
    logger.error('Error getting procurement categories', LogCategory.PROCUREMENT, error);
    
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'CATEGORY_FETCH_ERROR',
      error instanceof Error ? error.message : 'Failed to fetch procurement categories',
      'Unable to retrieve procurement category information. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try refreshing the page',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

/**
 * POST /api/procurement/categories
 * Create a new procurement category
 */
export async function POST(request: NextRequest): Promise<NextResponse<CategoryResponse | { success: false; error: unknown }>> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return errorService.createApiResponse(
        ErrorType.UNAUTHORIZED,
        'CATEGORY_CREATE_UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to create procurement categories.',
        {
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        401,
        ErrorSeverity.HIGH
      );
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER
    ]);

    if (!hasPermission) {
      return errorService.createApiResponse(
        ErrorType.FORBIDDEN,
        'CATEGORY_CREATE_FORBIDDEN',
        'Insufficient permissions to create procurement categories',
        'You do not have permission to create procurement category records.',
        {
          userId: user.id,
          userRole: user.role,
          endpoint: request.nextUrl.pathname,
          method: request.method
        },
        403,
        ErrorSeverity.MEDIUM,
        'User attempted to create procurement category without proper permissions',
        [
          'Contact your administrator to request procurement category creation permissions',
          'Ensure you are logged in with a manager-level account'
        ]
      );
    }

    // Parse request body
    let body: unknown;
    try {
      body = await request.json();
    } catch (parseError: unknown) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CATEGORY_INVALID_JSON',
        'Invalid request format',
        'The request data is not in the correct format. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          parseError: parseError instanceof Error ? parseError.message : 'Unknown parse error'
        },
        400,
        ErrorSeverity.MEDIUM,
        'Failed to parse JSON request body',
        [
          'Ensure the request body is valid JSON',
          'Check that all required fields are included',
          'Verify array and object structures are correct'
        ]
      );
    }

    // Validate request data
    const validationResult = createCategorySchema.safeParse(body);
    if (!validationResult.success) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'CATEGORY_VALIDATION_ERROR',
        'Invalid category data',
        'The category information provided is not valid. Please check your input and try again.',
        {
          userId: user.id,
          endpoint: request.nextUrl.pathname,
          method: request.method,
          validationErrors: validationResult.error.errors
        },
        400,
        ErrorSeverity.MEDIUM,
        validationResult.error.errors[0].message,
        [
          'Ensure all required fields are filled',
          'Check that values are in the correct format',
          'Verify category code follows the required pattern',
          'Ensure arrays contain valid items'
        ]
      );
    }

    const categoryData: CreateCategoryData = {
      ...validationResult.data,
      createdBy: user.id
    };

    // Create category
    const category = await procurementCategoryService.createCategory(categoryData);

    return NextResponse.json({
      success: true,
      message: 'Procurement category created successfully',
      data: category
    } as CategoryResponse, { status: 201 });

  } catch (error: unknown) {
    logger.error('Error creating procurement category', LogCategory.PROCUREMENT, error);
    
    // Handle specific business logic errors
    if (error instanceof Error) {
      if (error.message.includes('Parent category not found')) {
        return errorService.createApiResponse(
          ErrorType.NOT_FOUND,
          'CATEGORY_PARENT_NOT_FOUND',
          'Parent category not found',
          'The specified parent category could not be found. Please verify the parent category ID.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          404,
          ErrorSeverity.MEDIUM,
          error.message,
          [
            'Verify the parent category ID is correct',
            'Ensure the parent category exists and is active',
            'Contact procurement team if needed'
          ]
        );
      }

      if (error.message.includes('Category code already exists')) {
        return errorService.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'CATEGORY_CODE_EXISTS',
          'Category code already exists',
          'A category with this code already exists. Please choose a different code.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          400,
          ErrorSeverity.LOW,
          error.message,
          [
            'Choose a unique category code',
            'Check existing categories for code conflicts',
            'Use a descriptive and unique identifier'
          ]
        );
      }

      if (error.message.includes('Maximum category depth exceeded')) {
        return errorService.createApiResponse(
          ErrorType.BUSINESS_LOGIC,
          'CATEGORY_MAX_DEPTH_EXCEEDED',
          'Maximum category depth exceeded',
          'The category hierarchy cannot exceed 10 levels deep.',
          {
            endpoint: request.nextUrl.pathname,
            method: request.method,
            timestamp: new Date().toISOString()
          },
          400,
          ErrorSeverity.LOW,
          error.message,
          [
            'Choose a parent category at a higher level',
            'Consider restructuring the category hierarchy',
            'Contact system administrator for guidance'
          ]
        );
      }
    }

    // Generic server error
    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'CATEGORY_CREATE_ERROR',
      error instanceof Error ? error.message : 'Failed to create procurement category',
      'Unable to create the procurement category record. This may be due to a temporary server issue.',
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try creating the category again',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    );
  }
}

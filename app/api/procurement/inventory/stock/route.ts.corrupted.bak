import { NextRequest, NextResponse } from 'next/server';
import { procurementInventoryService } from '@/lib/backend/services/procurement/ProcurementInventoryService';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions, UserRole } from '@/lib/backend/auth/permissions';
import { z } from 'zod';

const stockUpdateSchema = z.object({
  itemId: z.string(),
  quantity: z.number().min(0),
  operation: z.enum(['add', 'remove', 'set']),
  reason: z.string().optional()
});

/**
 * POST /api/procurement/inventory/stock
 * Update stock levels for an inventory item
 */
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user || !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = stockUpdateSchema.parse(body);

    const updatedItem = await procurementInventoryService.updateStock(
      validatedData.itemId,
      validatedData.quantity,
      validatedData.operation,
      user.id.toString(),
      validatedData.reason
    );

    return NextResponse.json({
      success: true,
      data: updatedItem,
      message: 'Stock updated successfully'
    });
  } catch (error) {
    console.error('Error updating stock:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update stock' },
      { status: 500 }
    );
  }
}

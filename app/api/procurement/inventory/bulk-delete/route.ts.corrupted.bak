// app/api/procurement/inventory/bulk-delete/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { procurementInventoryService } from '@/lib/backend/services/procurement/ProcurementInventoryService';
import { errorService, ErrorType, ErrorSeverity } from '@/lib/backend/services/error-service';
import { AuditDeletionContext } from '@/lib/services/audit/audit-deletion-service';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import logger, { LogCategory } from '@/lib/backend/utils/logger';



/**
 * POST /api/procurement/inventory/bulk-delete
 * Bulk delete procurement inventory items with audit trail
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Get current user
    const currentUser = await getCurrentUser(request);
    if (!currentUser) {
      return errorService.createApiResponse(
        ErrorType.AUTHENTICATION,
        'UNAUTHORIZED',
        'Authentication required',
        'You must be logged in to delete inventory items',
        { endpoint: '/api/procurement/inventory/bulk-delete', method: 'POST' },
        401
      );
    }

    // Check permissions
    if (!['admin', 'procurement_manager', 'inventory_manager', 'super_admin'].includes(currentUser.role)) {
      return errorService.createApiResponse(
        ErrorType.AUTHORIZATION,
        'INSUFFICIENT_PERMISSIONS',
        'Insufficient permissions to bulk delete inventory items',
        'You do not have permission to bulk delete inventory items',
        { 
          userId: currentUser.id,
          userRole: currentUser.role,
          endpoint: '/api/procurement/inventory/bulk-delete',
          method: 'POST'
        },
        403
      );
    }

    // Parse request body
    const body = await request.json();
    const { itemIds, deletionReason, context } = body;

    // Validate input
    if (!Array.isArray(itemIds) || itemIds.length === 0) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'INVALID_ITEM_IDS',
        'Item IDs array is required and cannot be empty',
        'Please select at least one inventory item to delete',
        { 
          userId: currentUser.id,
          endpoint: '/api/procurement/inventory/bulk-delete',
          method: 'POST'
        },
        400
      );
    }

    if (itemIds.length > 100) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'TOO_MANY_ITEMS',
        'Cannot delete more than 100 inventory items at once',
        'Please select fewer inventory items (maximum 100) for bulk deletion',
        { 
          userId: currentUser.id,
          endpoint: '/api/procurement/inventory/bulk-delete',
          method: 'POST',
          additionalData: { requestedCount: itemIds.length }
        },
        400
      );
    }

    // Validate deletion reason
    if (!deletionReason || typeof deletionReason !== 'string' || deletionReason.trim().length < 25) {
      return errorService.createApiResponse(
        ErrorType.VALIDATION,
        'INVALID_BULK_DELETION_REASON',
        'Bulk deletion reason is required and must be at least 25 characters',
        'Please provide a detailed reason for bulk deleting these inventory items (minimum 25 characters)',
        { 
          userId: currentUser.id,
          endpoint: '/api/procurement/inventory/bulk-delete',
          method: 'POST'
        },
        400,
        ErrorSeverity.MEDIUM,
        'Bulk deletion reason validation failed for audit compliance requirements',
        [
          'Provide a clear and detailed reason for the bulk deletion',
          'Ensure the reason is at least 25 characters long',
          'Include stock disposition for items with inventory',
          'Include business justification for deleting multiple items'
        ]
      );
    }

    // Prepare audit context
    const auditContext: Omit<AuditDeletionContext, 'deletionType'> = {
      deletedBy: currentUser.id,
      deletionReason: deletionReason.trim(),
      userInfo: {
        id: currentUser.id,
        name: currentUser.name || 'Unknown User',
        email: currentUser.email || '<EMAIL>',
        role: currentUser.role
      },
      context: {
        fiscalYear: context?.fiscalYear,
        department: context?.department || 'Procurement',
        ipAddress: request.headers.get('x-forwarded-for') || 
                  request.headers.get('x-real-ip') || 
                  'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        sessionId: request.headers.get('x-session-id')
      }
    };

    logger.info('Processing bulk inventory deletion request', LogCategory.PROCUREMENT, {
      itemIds,
      count: itemIds.length,
      userId: currentUser.id,
      deletionReason: deletionReason.trim()
    });

    // Perform audit-compliant bulk deletion
    const result = await procurementInventoryService.bulkDeleteInventoryItems(itemIds, auditContext);

    logger.info('Bulk inventory deletion completed successfully', LogCategory.PROCUREMENT, {
      itemIds,
      userId: currentUser.id,
      deletedCount: result.deletedCount,
      auditRecordsCreated: result.auditRecordsCreated
    });

    return NextResponse.json({
      success: true,
      message: `Successfully deleted ${result.deletedCount} inventory items with audit trail`,
      data: {
        requestedCount: itemIds.length,
        deletedCount: result.deletedCount,
        auditRecordsCreated: result.auditRecordsCreated,
        auditCompliance: result.details?.auditCompliance,
        deletionIds: result.auditRecordIds,
        errors: result.errors
      }
    });

  } catch (error) {
    logger.error('Error in bulk inventory deletion', LogCategory.PROCUREMENT, error);

    // Handle structured errors
    if (error instanceof Error && error.message.includes('Unable to delete')) {
      return errorService.createApiResponse(
        ErrorType.BUSINESS_LOGIC,
        'BULK_INVENTORY_DELETE_CONSTRAINT',
        error.message,
        error.message,
        { 
          endpoint: '/api/procurement/inventory/bulk-delete',
          method: 'POST'
        },
        400,
        ErrorSeverity.MEDIUM
      );
    }

    return errorService.createApiResponse(
      ErrorType.SYSTEM,
      'BULK_INVENTORY_DELETE_ERROR',
      error instanceof Error ? error.message : 'Unknown error occurred',
      'An unexpected error occurred while bulk deleting inventory items. Please try again.',
      { 
        endpoint: '/api/procurement/inventory/bulk-delete',
        method: 'POST'
      },
      500,
      ErrorSeverity.HIGH,
      error instanceof Error ? error.stack : undefined,
      [
        'Try the operation again with fewer items',
        'Check if the items still exist',
        'Review the deletion constraints',
        'Ensure no pending orders reference these items',
        'Contact support if the problem persists'
      ]
    );
  }
}

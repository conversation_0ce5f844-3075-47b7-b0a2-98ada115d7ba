import { NextRequest, NextResponse } from 'next/server';
import { procurementInventoryService } from '@/lib/backend/services/procurement/ProcurementInventoryService';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions, UserRole } from '@/lib/backend/auth/permissions';
import { z } from 'zod';

// Validation schemas
const createInventorySchema = z.object({
  name: z.string().min(1).max(200),
  description: z.string().optional(),
  category: z.string(),
  sku: z.string().optional(),
  currentStock: z.number().min(0),
  minimumStock: z.number().min(0),
  maximumStock: z.number().min(0).optional(),
  unit: z.string().min(1),
  unitPrice: z.number().min(0),
  location: z.string().min(1),
  warehouse: z.string().optional(),
  shelf: z.string().optional(),
  zone: z.string().optional(),
  reorderPoint: z.number().min(0).optional(),
  reorderQuantity: z.number().min(0).optional(),
  leadTime: z.number().min(0).optional(),
  preferredSuppliers: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  notes: z.string().optional(),
  qualityGrade: z.enum(['A', 'B', 'C']).optional(),
  expiryDate: z.string().optional(),
  batchNumber: z.string().optional(),
  serialNumbers: z.array(z.string()).optional()
});

const searchSchema = z.object({
  page: z.string().optional(),
  limit: z.string().optional(),
  category: z.string().optional(),
  location: z.string().optional(),
  status: z.string().optional(),
  lowStock: z.string().optional(),
  search: z.string().optional(),
  tags: z.string().optional()
});

/**
 * GET /api/procurement/inventory
 * Get inventory items with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user || !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.EMPLOYEE
    ])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const params = Object.fromEntries(searchParams.entries());
    
    const validatedParams = searchSchema.parse(params);
    
    const page = parseInt(validatedParams.page || '1');
    const limit = parseInt(validatedParams.limit || '20');
    
    const filters = {
      category: validatedParams.category,
      location: validatedParams.location,
      status: validatedParams.status,
      lowStock: validatedParams.lowStock === 'true',
      search: validatedParams.search,
      tags: validatedParams.tags ? validatedParams.tags.split(',') : undefined
    };

    const result = await procurementInventoryService.searchInventory(filters, page, limit);

    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error fetching inventory:', error);
    return NextResponse.json(
      { error: 'Failed to fetch inventory items' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/procurement/inventory
 * Create a new inventory item
 */
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user || !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createInventorySchema.parse(body);

    // Convert date strings to Date objects
    const inventoryData = {
      ...validatedData,
      expiryDate: validatedData.expiryDate ? new Date(validatedData.expiryDate) : undefined,
      createdBy: user.id
    };

    const item = await procurementInventoryService.createInventoryItem(inventoryData);

    return NextResponse.json({
      success: true,
      data: item
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating inventory item:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create inventory item' },
      { status: 500 }
    );
  }
}

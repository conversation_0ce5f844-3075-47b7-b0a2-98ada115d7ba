// app/api/procurement/inventory/bulk-import/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { UserRole } from '@/types/user-roles';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { procurementInventoryService } from '@/lib/backend/services/procurement/ProcurementInventoryService';
import { procurementCategoryService } from '@/lib/backend/services/procurement/ProcurementCategoryService';
import { connectToDatabase } from '@/lib/backend/database';
import mongoose from 'mongoose';
import * as XLSX from 'xlsx';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export const runtime = 'nodejs';

// Define the expected columns in the import file
const REQUIRED_COLUMNS = [
  'name',
  'category'
];

// Define a mapping between display names in the template and the expected field names
const COLUMN_DISPLAY_MAPPING: Record<string, string> = {
  'name': 'name',
  'Name': 'name',
  'SKU': 'sku',
  'Description': 'description',
  'Category': 'category',
  'Unit of Measure': 'unitOfMeasure',
  'Unit Price': 'unitPrice',
  'Current Stock': 'currentStock',
  'Minimum Stock': 'minimumStock',
  'Maximum Stock': 'maximumStock',
  'Reorder Point': 'reorderPoint',
  'Reorder Quantity': 'reorderQuantity',
  'Location': 'location',
  'Warehouse': 'warehouse',
  'Shelf': 'shelf',
  'Bin': 'bin',
  'Serial Number': 'serialNumber',
  'Batch Number': 'batchNumber',
  'Expiry Date': 'expiryDate',
  'Quality Grade': 'qualityGrade',
  'Condition': 'condition',
  'Status': 'status',
  'Preferred Suppliers': 'preferredSuppliers',
  'Lead Time': 'leadTime',
  'Safety Stock': 'safetyStock',
  'ABC Classification': 'abcClassification',
  'Tags': 'tags',
  'Notes': 'notes'
};

// Define roles that can manage inventory
const INVENTORY_ADMIN_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.PROCUREMENT_MANAGER,
  UserRole.PROCUREMENT_OFFICER,
  UserRole.INVENTORY_MANAGER
];

interface ImportResult {
  totalRows: number;
  successCount: number;
  errorCount: number;
  skippedCount: number;
  errors: Array<{ row: number; error: string }>;
  imported: Array<{ row: number; name: string; sku?: string; inventoryId?: string }>;
  skipped: Array<{ row: number; name: string; reason: string }>;
}

/**
 * POST handler for bulk importing procurement inventory items
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, INVENTORY_ADMIN_ROLES);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Connect to database
    await connectToDatabase();

    // Debug: Check model registration
    logger.debug('Model registration check', LogCategory.IMPORT, {
      registeredModels: Object.keys(mongoose.models),
      inventoryModelExists: !!mongoose.models.ProcurementInventory,
      userId: user.id
    });

    // Get form data
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Check file type
    const fileType = file.name.split('.').pop()?.toLowerCase();
    if (fileType !== 'csv' && fileType !== 'xlsx' && fileType !== 'xls') {
      return NextResponse.json({ error: 'Invalid file type. Please upload a CSV or Excel file' }, { status: 400 });
    }

    // Read file
    const buffer = await file.arrayBuffer();

    logger.info('Processing procurement inventory bulk import', LogCategory.IMPORT, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      userId: user.id
    });

    const workbook = XLSX.read(buffer, { type: 'array' });

    // Get first sheet
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    // Convert to JSON
    const rawData = XLSX.utils.sheet_to_json(worksheet, { defval: null });

    if (!rawData || rawData.length === 0) {
      return NextResponse.json({ error: 'No data found in the file' }, { status: 400 });
    }

    logger.info('File parsed successfully', LogCategory.IMPORT, {
      rowCount: rawData.length,
      userId: user.id
    });

    // Initialize result
    const result: ImportResult = {
      totalRows: rawData.length,
      successCount: 0,
      errorCount: 0,
      skippedCount: 0,
      errors: [],
      imported: [],
      skipped: []
    };

    // Get existing inventory items and categories for validation
    const existingItems = await procurementInventoryService.getAllInventoryItems();
    const existingItemNames = new Set(existingItems.map(item => item.name.toLowerCase()));
    const existingItemSKUs = new Set(existingItems.map(item => item.sku?.toLowerCase()).filter(Boolean));

    const existingCategories = await procurementCategoryService.getAllCategories();
    const categoryMap = new Map(existingCategories.map(cat => [cat.name.toLowerCase(), cat._id.toString()]));

    // Process each row
    for (let i = 0; i < rawData.length; i++) {
      const row = rawData[i] as Record<string, any>;

      try {
        // Normalize column names
        const normalizedRow: Record<string, any> = {};
        for (const [key, value] of Object.entries(row)) {
          const normalizedKey = COLUMN_DISPLAY_MAPPING[key] || key.toLowerCase();
          normalizedRow[normalizedKey] = value;
        }

        // Validate required fields
        if (!normalizedRow.name || typeof normalizedRow.name !== 'string' || normalizedRow.name.trim() === '') {
          result.errorCount++;
          result.errors.push({
            row: i + 1,
            error: 'Name is required and cannot be empty'
          });
          continue;
        }

        if (!normalizedRow.category || typeof normalizedRow.category !== 'string' || normalizedRow.category.trim() === '') {
          result.errorCount++;
          result.errors.push({
            row: i + 1,
            error: 'Category is required and cannot be empty'
          });
          continue;
        }

        const itemName = normalizedRow.name.trim();
        const categoryName = normalizedRow.category.trim();

        // Check if category exists
        const categoryId = categoryMap.get(categoryName.toLowerCase());
        if (!categoryId) {
          result.errorCount++;
          result.errors.push({
            row: i + 1,
            error: `Category "${categoryName}" does not exist. Please create the category first.`
          });
          continue;
        }

        // Check for duplicates
        if (existingItemNames.has(itemName.toLowerCase())) {
          result.skippedCount++;
          result.skipped.push({
            row: i + 1,
            name: itemName,
            reason: 'Item with this name already exists'
          });
          continue;
        }

        // Generate SKU if not provided
        let itemSKU = normalizedRow.sku?.trim();
        if (!itemSKU) {
          itemSKU = itemName.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 10) + Date.now().toString().slice(-4);
        }

        // Check for SKU duplicates
        if (existingItemSKUs.has(itemSKU.toLowerCase())) {
          // Generate unique SKU
          let counter = 1;
          let uniqueSKU = `${itemSKU}_${counter}`;
          while (existingItemSKUs.has(uniqueSKU.toLowerCase())) {
            counter++;
            uniqueSKU = `${itemSKU}_${counter}`;
          }
          itemSKU = uniqueSKU;
        }

        // Generate inventory ID
        const inventoryId = `INV-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;

        // Prepare inventory data
        const inventoryData: any = {
          inventoryId,
          name: itemName,
          sku: itemSKU,
          description: normalizedRow.description?.trim() || '',
          category: categoryId,
          unitOfMeasure: normalizedRow.unitOfMeasure || 'piece',
          unitPrice: normalizedRow.unitPrice ? parseFloat(normalizedRow.unitPrice) : 0,
          currentStock: normalizedRow.currentStock ? parseInt(normalizedRow.currentStock) : 0,
          minimumStock: normalizedRow.minimumStock ? parseInt(normalizedRow.minimumStock) : 0,
          maximumStock: normalizedRow.maximumStock ? parseInt(normalizedRow.maximumStock) : 0,
          reorderPoint: normalizedRow.reorderPoint ? parseInt(normalizedRow.reorderPoint) : 0,
          reorderQuantity: normalizedRow.reorderQuantity ? parseInt(normalizedRow.reorderQuantity) : 0,
          location: {
            warehouse: normalizedRow.warehouse || 'Main Warehouse',
            shelf: normalizedRow.shelf || '',
            bin: normalizedRow.bin || ''
          },
          serialNumber: normalizedRow.serialNumber?.trim() || '',
          batchNumber: normalizedRow.batchNumber?.trim() || '',
          qualityGrade: normalizedRow.qualityGrade || 'A',
          condition: normalizedRow.condition || 'new',
          status: normalizedRow.status || 'active',
          leadTime: normalizedRow.leadTime ? parseInt(normalizedRow.leadTime) : 0,
          safetyStock: normalizedRow.safetyStock ? parseInt(normalizedRow.safetyStock) : 0,
          abcClassification: normalizedRow.abcClassification || 'C',
          tags: normalizedRow.tags?.split(',').map((s: string) => s.trim()).filter(Boolean) || [],
          notes: normalizedRow.notes?.trim() || '',
          createdBy: user.id
        };

        // Handle expiry date
        if (normalizedRow.expiryDate) {
          try {
            inventoryData.expiryDate = new Date(normalizedRow.expiryDate);
          } catch (error) {
            // Invalid date format, skip this field
            logger.warn('Invalid expiry date format', LogCategory.IMPORT, {
              row: i + 1,
              expiryDate: normalizedRow.expiryDate,
              userId: user.id
            });
          }
        }

        // Calculate total value
        inventoryData.totalValue = inventoryData.unitPrice * inventoryData.currentStock;

        // Create inventory item
        const newItem = await procurementInventoryService.createInventoryItem(inventoryData);

        // Add to existing sets to prevent duplicates in subsequent rows
        existingItemNames.add(itemName.toLowerCase());
        existingItemSKUs.add(itemSKU.toLowerCase());

        result.successCount++;
        result.imported.push({
          row: i + 1,
          name: itemName,
          sku: itemSKU,
          inventoryId
        });

        logger.debug('Inventory item imported successfully', LogCategory.IMPORT, {
          row: i + 1,
          itemId: newItem.id,
          name: itemName,
          sku: itemSKU,
          inventoryId,
          userId: user.id
        });

      } catch (error: unknown) {
        result.errorCount++;

        // Log the error
        logger.error('Error processing inventory row', LogCategory.IMPORT, {
          rowIndex: i + 1,
          error: error instanceof Error ? error.message : 'An unknown error occurred',
          row: row,
          userId: user.id
        });

        result.errors.push({
          row: i + 1,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Log the final result
    logger.info('Procurement inventory bulk import completed', LogCategory.IMPORT, {
      totalRows: result.totalRows,
      successCount: result.successCount,
      errorCount: result.errorCount,
      skippedCount: result.skippedCount,
      userId: user.id
    });

    return NextResponse.json({
      status: 'success',
      data: result
    });

  } catch (error: unknown) {
    logger.error('Error in procurement inventory bulk import', LogCategory.IMPORT, error);

    return NextResponse.json({
      error: error instanceof Error ? error.message : 'An unexpected error occurred during import'
    }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { procurementInventoryService } from '@/lib/backend/services/procurement/ProcurementInventoryService';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions, UserRole } from '@/lib/backend/auth/permissions';

export const runtime = 'nodejs';

/**
 * GET /api/procurement/inventory/reorder
 * Get reorder suggestions for low stock items
 */
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user || !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER
    ])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    const suggestions = await procurementInventoryService.getReorderSuggestions();
    return NextResponse.json({
      success: true,
      data: suggestions
    });
  } catch (error) {
    console.error('Error fetching reorder suggestions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reorder suggestions' },
      { status: 500 }
    );
  }
}
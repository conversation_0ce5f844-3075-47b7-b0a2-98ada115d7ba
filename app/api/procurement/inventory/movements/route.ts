import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions, UserRole } from '@/lib/backend/auth/permissions';
import { z } from 'zod';

export const runtime = 'nodejs';

// Validation schema
const searchSchema = z.object({
  inventoryId: z.string().optional(),
  movementType: z.enum(['in', 'out', 'adjustment', 'transfer']).optional(),
  dateRange: z.enum(['7days', '30days', '90days', '1year']).optional(),
  limit: z.string().optional(),
  page: z.string().optional()
});
/**
 * GET /api/procurement/inventory/movements
 * Get stock movement history with filtering
 */
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user || !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER,
      UserRole.FINANCE_MANAGER,
      UserRole.DEPARTMENT_HEAD,
      UserRole.EMPLOYEE
    ])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    const { searchParams } = new URL(request.url);
    const params = Object.fromEntries(searchParams.entries());
    const validatedParams = searchSchema.parse(params);
    const limit = parseInt(validatedParams.limit || '50');
    const page = parseInt(validatedParams.page || '1');
    // Calculate date filter
    let dateFilter = new Date();
    switch (validatedParams.dateRange) {
      case '7days':
        dateFilter.setDate(dateFilter.getDate() - 7);
        break;
      case '30days':
        dateFilter.setDate(dateFilter.getDate() - 30);
        break;
      case '90days':
        dateFilter.setDate(dateFilter.getDate() - 90);
        break;
      case '1year':
        dateFilter.setFullYear(dateFilter.getFullYear() - 1);
        break;
      default:
        dateFilter.setDate(dateFilter.getDate() - 7);
    }
    // Mock data for demonstration - replace with actual database query
    const mockMovements = [
      {
        _id: 'mov_001',
        inventoryId: 'inv_001',
        itemName: 'A4 Copy Paper',
        movementType: 'in',
        quantity: 50,
        unit: 'ream',
        reason: 'Purchase Order PO-2024-001',
        reference: 'PO-2024-001',
        previousStock: 20,
        newStock: 70,
        unitPrice: 15.50,
        totalValue: 775.00,
        location: 'Main Warehouse',
        performedBy: 'John Banda',
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        notes: 'Regular stock replenishment'
      },
      {
        _id: 'mov_002',
        inventoryId: 'inv_001',
        itemName: 'A4 Copy Paper',
        movementType: 'out',
        quantity: 10,
        unit: 'ream',
        reason: 'Department Requisition',
        reference: 'REQ-2024-015',
        previousStock: 70,
        newStock: 60,
        location: 'Main Warehouse',
        performedBy: 'Mary Phiri',
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
        notes: 'HR Department request'
      },
      {
        _id: 'mov_003',
        inventoryId: 'inv_002',
        itemName: 'Dell Laptop Computer',
        movementType: 'in',
        quantity: 3,
        unit: 'piece',
        reason: 'Purchase Order PO-2024-002',
        reference: 'PO-2024-002',
        previousStock: 2,
        newStock: 5,
        unitPrice: 85000,
        totalValue: 255000,
        location: 'IT Storage',
        performedBy: 'Peter Mwale',
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        notes: 'IT equipment procurement'
      },
      {
        _id: 'mov_004',
        inventoryId: 'inv_002',
        itemName: 'Dell Laptop Computer',
        movementType: 'out',
        quantity: 1,
        unit: 'piece',
        reason: 'Employee Assignment',
        reference: 'EMP-2024-001',
        previousStock: 5,
        newStock: 4,
        location: 'IT Storage',
        performedBy: 'Peter Mwale',
        createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
        notes: 'Assigned to new employee'
      },
      {
        _id: 'mov_005',
        inventoryId: 'inv_003',
        itemName: 'Office Chair',
        movementType: 'adjustment',
        quantity: 2,
        unit: 'piece',
        reason: 'Stock Count Adjustment',
        reference: 'ADJ-2024-001',
        previousStock: 15,
        newStock: 13,
        location: 'Furniture Storage',
        performedBy: 'Sarah Tembo',
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
        notes: 'Physical count discrepancy - 2 chairs damaged'
      }
    ];
    // Apply filters
    let filteredMovements = mockMovements.filter(movement => {
      const movementDate = new Date(movement.createdAt);
      const isWithinDateRange = movementDate >= dateFilter;
      const matchesInventoryId = !validatedParams.inventoryId || 
        movement.inventoryId === validatedParams.inventoryId;
      const matchesMovementType = !validatedParams.movementType || 
        movement.movementType === validatedParams.movementType;
      return isWithinDateRange && matchesInventoryId && matchesMovementType;
    });
    // Sort by date (newest first)
    filteredMovements.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedMovements = filteredMovements.slice(startIndex, endIndex);
    const result = {
      data: paginatedMovements,
      pagination: {
        page,
        limit,
        total: filteredMovements.length,
        pages: Math.ceil(filteredMovements.length / limit)
      },
      summary: {
        totalMovements: filteredMovements.length,
        stockIn: filteredMovements.filter(m => m.movementType === 'in').length,
        stockOut: filteredMovements.filter(m => m.movementType === 'out').length,
        adjustments: filteredMovements.filter(m => m.movementType === 'adjustment').length,
        transfers: filteredMovements.filter(m => m.movementType === 'transfer').length
      }
    };
    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
      summary: result.summary
    });
  } catch (error) {
    console.error('Error fetching stock movements:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid parameters', details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to fetch stock movements' },
      { status: 500 }
    );
  }
}
/**
 * POST /api/procurement/inventory/movements
 * Create a new stock movement record
 */
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user || !hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.PROCUREMENT_OFFICER
    ])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    const body = await request.json();
    // Validate required fields
    const requiredFields = ['inventoryId', 'movementType', 'quantity', 'reason'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }
    // In a real implementation, you would:
    // 1. Validate the inventory item exists
    // 2. Calculate new stock levels
    // 3. Create the movement record
    // 4. Update the inventory item stock
    // 5. Create audit trail
    const movement = {
      _id: `mov_${Date.now()}`,
      ...body,
      performedBy: user.name || user.email,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    return NextResponse.json({
      success: true,
      data: movement
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating stock movement:', error);
    return NextResponse.json(
      { error: 'Failed to create stock movement' },
      { status: 500 }
    );
  }
}
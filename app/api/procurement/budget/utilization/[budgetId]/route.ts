import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { procurementBudgetIntegrationService } from '@/lib/services/procurement/budget-integration-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

/**
 * GET /api/procurement/budget/utilization/[budgetId]
 * Get budget utilization report for procurement activities
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ budgetId: string }> }
): Promise<NextResponse> {
  try {
    // Resolve the params promise
    const { budgetId } = await params;
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_DIRECTOR,
      UserRole.PROCUREMENT_MANAGER,
      UserRole.DEPARTMENT_HEAD
    ]);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    // Validate budget ID
    if (!mongoose.Types.ObjectId.isValid(budgetId)) {
      return NextResponse.json({
        error: 'Invalid budget ID',
        message: 'The provided budget ID is not valid'
      }, { status: 400 });
    }
    logger.info('Budget utilization report requested', LogCategory.PROCUREMENT, {
      budgetId,
      userId: user.id
    });
    // Get budget utilization
    const utilization = await procurementBudgetIntegrationService.getBudgetUtilization(budgetId);
    logger.info('Budget utilization report generated successfully', LogCategory.PROCUREMENT, {
      budgetId,
      totalBudgeted: utilization.totalBudgeted,
      totalActual: utilization.totalActual,
      categoriesCount: utilization.categories.length,
      userId: user.id
    });
    return NextResponse.json({
      success: true,
      utilization: {
        budgetId: utilization.budgetId,
        totalBudgeted: utilization.totalBudgeted,
        totalActual: utilization.totalActual,
        totalUtilizationPercentage: utilization.totalBudgeted > 0 
          ? (utilization.totalActual / utilization.totalBudgeted) * 100 
          : 0,
        categories: utilization.categories.map(category => ({
          categoryId: category.categoryId,
          name: category.name,
          budgeted: category.budgeted,
          actual: category.actual,
          allocated: category.allocated,
          available: category.available,
          utilizationPercentage: category.utilizationPercentage,
          status: category.utilizationPercentage > 100 ? 'overbudget' :
                  category.utilizationPercentage > 85 ? 'warning' :
                  category.utilizationPercentage > 70 ? 'caution' : 'normal'
        })),
        summary: {
          totalCategories: utilization.categories.length,
          overbudgetCategories: utilization.categories.filter(c => c.utilizationPercentage > 100).length,
          warningCategories: utilization.categories.filter(c => c.utilizationPercentage > 85 && c.utilizationPercentage <= 100).length,
          normalCategories: utilization.categories.filter(c => c.utilizationPercentage <= 85).length
        },
        generatedAt: new Date()
      }
    });
  } catch (error) {
    logger.error('Error getting budget utilization', LogCategory.PROCUREMENT, {
      budgetId: (await params).budgetId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return NextResponse.json({
      error: 'Internal server error',
      message: 'Unable to retrieve budget utilization. Please try again or contact support.',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
/**
 * POST /api/procurement/budget/utilization/[budgetId]
 * Refresh budget utilization data (force recalculation)
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ budgetId: string }> }
): Promise<NextResponse> {
  try {
    // Resolve the params promise
    const { budgetId } = await params;
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions (only admin and finance roles can refresh)
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_DIRECTOR,
      UserRole.PROCUREMENT_MANAGER
    ]);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    // Validate budget ID
    if (!mongoose.Types.ObjectId.isValid(budgetId)) {
      return NextResponse.json({
        error: 'Invalid budget ID',
        message: 'The provided budget ID is not valid'
      }, { status: 400 });
    }
    logger.info('Budget utilization refresh requested', LogCategory.PROCUREMENT, {
      budgetId,
      userId: user.id
    });
    // Force refresh by getting fresh utilization data
    const utilization = await procurementBudgetIntegrationService.getBudgetUtilization(budgetId);
    logger.info('Budget utilization refreshed successfully', LogCategory.PROCUREMENT, {
      budgetId,
      userId: user.id
    });
    return NextResponse.json({
      success: true,
      message: 'Budget utilization data refreshed successfully',
      refreshedAt: new Date(),
      utilization: {
        budgetId: utilization.budgetId,
        totalBudgeted: utilization.totalBudgeted,
        totalActual: utilization.totalActual,
        categoriesCount: utilization.categories.length
      }
    });
  } catch (error) {
    logger.error('Error refreshing budget utilization', LogCategory.PROCUREMENT, {
      budgetId: (await params).budgetId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return NextResponse.json({
      error: 'Internal server error',
      message: 'Unable to refresh budget utilization. Please try again or contact support.',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
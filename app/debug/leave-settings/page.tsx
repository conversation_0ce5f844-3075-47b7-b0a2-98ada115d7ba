"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Database, Settings, TestTube } from "lucide-react"
import { useLeaveSettings } from "@/hooks/use-leave-settings"

export default function LeaveSettingsDebugPage() {
  const [testing, setTesting] = useState(false)
  const { toast } = useToast()
  const { loading, settings, getSettings, updateSettings, resetSettings } = useLeaveSettings()

  const handleTestGetSettings = async () => {
    try {
      setTesting(true)
      const result = await getSettings()
      console.log('Settings retrieved:', result)
      
      toast({
        title: "Success",
        description: "Settings retrieved successfully. Check console for details.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to get settings",
        variant: "destructive",
      })
    } finally {
      setTesting(false)
    }
  }

  const handleTestUpdateSettings = async () => {
    try {
      setTesting(true)
      const testSettings = {
        carryOverEnabled: true,
        maxCarryOverDays: 10,
        minNoticeDays: 5,
        emailNotificationsEnabled: true,
      }
      
      const success = await updateSettings(testSettings)
      console.log('Update result:', success)
      
      if (success) {
        toast({
          title: "Success",
          description: "Settings updated successfully",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update settings",
        variant: "destructive",
      })
    } finally {
      setTesting(false)
    }
  }

  const handleTestResetSettings = async () => {
    try {
      setTesting(true)
      const success = await resetSettings()
      console.log('Reset result:', success)
      
      if (success) {
        toast({
          title: "Success",
          description: "Settings reset to defaults successfully",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to reset settings",
        variant: "destructive",
      })
    } finally {
      setTesting(false)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Leave Settings Debug</h1>
          <p className="text-muted-foreground">
            Debug and test leave settings functionality
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* API Testing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TestTube className="h-5 w-5" />
              API Testing
            </CardTitle>
            <CardDescription>
              Test leave settings API endpoints
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={handleTestGetSettings} 
              disabled={testing || loading}
              className="w-full"
            >
              {testing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Testing...
                </>
              ) : (
                'Test Get Settings'
              )}
            </Button>
            
            <Button 
              onClick={handleTestUpdateSettings} 
              disabled={testing || loading}
              variant="outline"
              className="w-full"
            >
              {testing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Testing...
                </>
              ) : (
                'Test Update Settings'
              )}
            </Button>
            
            <Button 
              onClick={handleTestResetSettings} 
              disabled={testing || loading}
              variant="outline"
              className="w-full"
            >
              {testing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Testing...
                </>
              ) : (
                'Test Reset Settings'
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Current Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Current Settings
            </CardTitle>
            <CardDescription>
              Current leave settings data
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading...</span>
              </div>
            ) : settings ? (
              <div className="space-y-2">
                <div className="text-sm">
                  <strong>Carry-over:</strong> {settings.carryOverEnabled ? 'Enabled' : 'Disabled'}
                </div>
                <div className="text-sm">
                  <strong>Max Carry-over Days:</strong> {settings.maxCarryOverDays}
                </div>
                <div className="text-sm">
                  <strong>Min Notice Days:</strong> {settings.minNoticeDays}
                </div>
                <div className="text-sm">
                  <strong>Approval Workflow:</strong> {settings.approvalWorkflow}
                </div>
                <div className="text-sm">
                  <strong>Email Notifications:</strong> {settings.emailNotificationsEnabled ? 'Enabled' : 'Disabled'}
                </div>
                <div className="text-sm">
                  <strong>Leave Year:</strong> {settings.defaultLeaveYear}
                </div>
                <div className="text-sm">
                  <strong>Accrual Frequency:</strong> {settings.leaveAccrualFrequency}
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">No settings loaded</p>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <p className="text-sm text-muted-foreground">
            1. Use "Test Get Settings" to retrieve current settings from the API
          </p>
          <p className="text-sm text-muted-foreground">
            2. Use "Test Update Settings" to test updating settings with sample data
          </p>
          <p className="text-sm text-muted-foreground">
            3. Use "Test Reset Settings" to reset settings to default values
          </p>
          <p className="text-sm text-muted-foreground">
            4. Check the browser console for detailed API responses
          </p>
          <p className="text-sm text-muted-foreground">
            5. Visit <code>/dashboard/leave/settings</code> to see the full settings page
          </p>
        </CardContent>
      </Card>
    </div>
  )
}

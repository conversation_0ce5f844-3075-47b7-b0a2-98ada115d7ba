"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Database, Download, FileText, FileSpreadsheet, FileImage } from "lucide-react"

export default function LeaveReportsDebugPage() {
  const [loading, setLoading] = useState(false)
  const [seedingData, setSeedingData] = useState(false)
  const [clearingData, setClearingData] = useState(false)
  const { toast } = useToast()

  const handleSeedData = async () => {
    try {
      setSeedingData(true)
      const response = await fetch('/api/debug/seed-sample-leaves', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to seed data')
      }

      toast({
        title: "Success",
        description: `Created ${result.data.created} sample leave records`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to seed data',
        variant: "destructive",
      })
    } finally {
      setSeedingData(false)
    }
  }

  const handleClearData = async () => {
    try {
      setClearingData(true)
      const response = await fetch('/api/debug/seed-sample-leaves', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to clear data')
      }

      toast({
        title: "Success",
        description: `Cleared ${result.data.deletedCount} sample leave records`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to clear data',
        variant: "destructive",
      })
    } finally {
      setClearingData(false)
    }
  }

  const handleTestExport = async (format: 'csv' | 'excel' | 'pdf') => {
    try {
      setLoading(true)
      
      const params = new URLSearchParams({
        timeRange: 'month',
        format
      })

      const response = await fetch(`/api/leave/reports/overview?${params.toString()}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to export report')
      }

      // Handle file download
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `test-leave-overview-report.${format === 'excel' ? 'xlsx' : format}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      toast({
        title: "Success",
        description: `Report exported as ${format.toUpperCase()}`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to export report',
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleTestReportData = async () => {
    try {
      setLoading(true)
      
      const params = new URLSearchParams({
        timeRange: 'month',
        format: 'json'
      })

      const response = await fetch(`/api/leave/reports/overview?${params.toString()}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to get report data')
      }

      const result = await response.json()
      
      console.log('Report data:', result)
      
      toast({
        title: "Success",
        description: `Report data retrieved. Check console for details.`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to get report data',
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Leave Reports Debug</h1>
          <p className="text-muted-foreground">
            Debug and test leave reports functionality
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Sample Data Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Sample Data Management
            </CardTitle>
            <CardDescription>
              Create or clear sample leave data for testing reports
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={handleSeedData} 
              disabled={seedingData}
              className="w-full"
            >
              {seedingData ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Sample Data...
                </>
              ) : (
                'Create Sample Leave Data'
              )}
            </Button>
            
            <Button 
              onClick={handleClearData} 
              disabled={clearingData}
              variant="outline"
              className="w-full"
            >
              {clearingData ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Clearing Sample Data...
                </>
              ) : (
                'Clear Sample Leave Data'
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Report Testing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Report Testing
            </CardTitle>
            <CardDescription>
              Test leave report generation and exports
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={handleTestReportData} 
              disabled={loading}
              variant="outline"
              className="w-full"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Testing...
                </>
              ) : (
                'Test Report Data (JSON)'
              )}
            </Button>

            <div className="grid grid-cols-3 gap-2">
              <Button 
                onClick={() => handleTestExport('csv')} 
                disabled={loading}
                size="sm"
                variant="outline"
              >
                <FileText className="mr-1 h-3 w-3" />
                CSV
              </Button>
              
              <Button 
                onClick={() => handleTestExport('excel')} 
                disabled={loading}
                size="sm"
                variant="outline"
              >
                <FileSpreadsheet className="mr-1 h-3 w-3" />
                Excel
              </Button>
              
              <Button 
                onClick={() => handleTestExport('pdf')} 
                disabled={loading}
                size="sm"
                variant="outline"
              >
                <FileImage className="mr-1 h-3 w-3" />
                PDF
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <p className="text-sm text-muted-foreground">
            1. First, create sample leave data using the "Create Sample Leave Data" button
          </p>
          <p className="text-sm text-muted-foreground">
            2. Test the report data generation using "Test Report Data (JSON)"
          </p>
          <p className="text-sm text-muted-foreground">
            3. Test the export functionality using the CSV, Excel, and PDF buttons
          </p>
          <p className="text-sm text-muted-foreground">
            4. Check the browser console for detailed report data
          </p>
          <p className="text-sm text-muted-foreground">
            5. Clear sample data when done testing
          </p>
        </CardContent>
      </Card>
    </div>
  )
}

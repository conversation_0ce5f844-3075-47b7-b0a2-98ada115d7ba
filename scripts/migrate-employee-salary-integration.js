/**
 * Migration script for Employee-Salary Integration
 * This script updates existing employees with new fields for salary calculation
 */

const mongoose = require('mongoose');
const { connectToDatabase } = require('../lib/backend/database');

// Import models
const Employee = require('../models/Employee').default;
const Role = require('../models/Role').default;
const EmployeeSalary = require('../models/payroll/EmployeeSalary').default;

/**
 * Main migration function
 */
async function migrateEmployeeSalaryIntegration() {
  try {
    console.log('🚀 Starting Employee-Salary Integration Migration...');
    
    // Connect to database
    await connectToDatabase();
    console.log('✅ Connected to database');
    
    // Step 1: Update employees with roleId based on position
    await updateEmployeeRoles();
    
    // Step 2: Set employment sub-types based on employment type
    await updateEmploymentSubTypes();
    
    // Step 3: Set hourly rates for part-time employees
    await updateHourlyRates();
    
    // Step 4: Set salary calculation rules
    await updateSalaryCalculationRules();
    
    // Step 5: Update existing EmployeeSalary records with calculation types
    await updateEmployeeSalaryCalculationTypes();
    
    console.log('✅ Migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

/**
 * Update employees with roleId based on position field
 */
async function updateEmployeeRoles() {
  console.log('📝 Updating employee roles...');
  
  try {
    // Get all employees without roleId
    const employees = await Employee.find({ 
      roleId: { $exists: false },
      position: { $exists: true, $ne: '' }
    });
    
    console.log(`Found ${employees.length} employees to update with roles`);
    
    let updatedCount = 0;
    
    for (const employee of employees) {
      try {
        // Try to find a role that matches the position
        const role = await Role.findOne({
          $or: [
            { name: { $regex: new RegExp(employee.position, 'i') } },
            { code: employee.position.toLowerCase().replace(/\s+/g, '_') }
          ]
        });
        
        if (role) {
          await Employee.findByIdAndUpdate(employee._id, {
            roleId: role._id
          });
          updatedCount++;
          console.log(`✅ Updated employee ${employee.firstName} ${employee.lastName} with role ${role.name}`);
        } else {
          console.log(`⚠️  No role found for position: ${employee.position} (Employee: ${employee.firstName} ${employee.lastName})`);
        }
      } catch (error) {
        console.error(`❌ Error updating employee ${employee._id}:`, error.message);
      }
    }
    
    console.log(`✅ Updated ${updatedCount} employees with roles`);
    
  } catch (error) {
    console.error('❌ Error updating employee roles:', error);
    throw error;
  }
}

/**
 * Set employment sub-types based on employment type
 */
async function updateEmploymentSubTypes() {
  console.log('📝 Updating employment sub-types...');
  
  try {
    // Set default sub-types based on employment type
    const updates = [
      {
        filter: { employmentType: 'full-time', employmentSubType: { $exists: false } },
        update: { employmentSubType: 'standard' }
      },
      {
        filter: { employmentType: 'part-time', employmentSubType: { $exists: false } },
        update: { employmentSubType: 'attendance-based' }
      },
      {
        filter: { employmentType: 'contract', employmentSubType: { $exists: false } },
        update: { employmentSubType: 'task-based' }
      },
      {
        filter: { employmentType: 'intern', employmentSubType: { $exists: false } },
        update: { employmentSubType: 'hybrid' }
      },
      {
        filter: { employmentType: { $in: ['temporary', 'volunteer'] }, employmentSubType: { $exists: false } },
        update: { employmentSubType: 'standard' }
      }
    ];
    
    let totalUpdated = 0;
    
    for (const { filter, update } of updates) {
      const result = await Employee.updateMany(filter, update);
      totalUpdated += result.modifiedCount;
      console.log(`✅ Updated ${result.modifiedCount} employees with sub-type: ${update.employmentSubType}`);
    }
    
    console.log(`✅ Total employees updated with sub-types: ${totalUpdated}`);
    
  } catch (error) {
    console.error('❌ Error updating employment sub-types:', error);
    throw error;
  }
}

/**
 * Set hourly rates for part-time and attendance-based employees
 */
async function updateHourlyRates() {
  console.log('📝 Updating hourly rates...');
  
  try {
    // Get employees who need hourly rates but don't have them
    const employees = await Employee.find({
      $or: [
        { employmentType: 'part-time' },
        { employmentSubType: { $in: ['attendance-based', 'hybrid'] } }
      ],
      hourlyRate: { $exists: false }
    }).populate('roleId');
    
    console.log(`Found ${employees.length} employees needing hourly rates`);
    
    let updatedCount = 0;
    
    for (const employee of employees) {
      try {
        let hourlyRate = 500; // Default hourly rate in MWK
        
        // Try to calculate from existing salary
        if (employee.salary && employee.salary > 0) {
          // Assume 160 hours per month for full-time equivalent
          hourlyRate = Math.round(employee.salary / 160);
        }
        
        // Adjust based on role if available
        if (employee.roleId && employee.roleId.name) {
          const roleName = employee.roleId.name.toLowerCase();
          if (roleName.includes('manager') || roleName.includes('director')) {
            hourlyRate = Math.max(hourlyRate, 2000);
          } else if (roleName.includes('senior') || roleName.includes('lead')) {
            hourlyRate = Math.max(hourlyRate, 1500);
          } else if (roleName.includes('junior') || roleName.includes('intern')) {
            hourlyRate = Math.max(hourlyRate, 300);
          }
        }
        
        await Employee.findByIdAndUpdate(employee._id, {
          hourlyRate: hourlyRate
        });
        
        updatedCount++;
        console.log(`✅ Set hourly rate ${hourlyRate} for ${employee.firstName} ${employee.lastName}`);
        
      } catch (error) {
        console.error(`❌ Error updating hourly rate for employee ${employee._id}:`, error.message);
      }
    }
    
    console.log(`✅ Updated ${updatedCount} employees with hourly rates`);
    
  } catch (error) {
    console.error('❌ Error updating hourly rates:', error);
    throw error;
  }
}

/**
 * Set default salary calculation rules for employees
 */
async function updateSalaryCalculationRules() {
  console.log('📝 Updating salary calculation rules...');
  
  try {
    const employees = await Employee.find({
      'salaryCalculationRules': { $exists: false }
    });
    
    console.log(`Found ${employees.length} employees needing salary calculation rules`);
    
    let updatedCount = 0;
    
    for (const employee of employees) {
      try {
        const rules = {
          attendanceRequired: ['part-time'].includes(employee.employmentType) || 
                             ['attendance-based', 'hybrid'].includes(employee.employmentSubType),
          taskCompletionRequired: ['contract', 'intern'].includes(employee.employmentType) || 
                                 ['task-based', 'hybrid'].includes(employee.employmentSubType),
          minimumHours: employee.employmentType === 'full-time' ? 160 : 
                       employee.employmentType === 'part-time' ? 80 : 0,
          overtimeMultiplier: 1.5,
          performanceBonusEnabled: ['intern', 'contract'].includes(employee.employmentType) || 
                                  ['task-based', 'hybrid'].includes(employee.employmentSubType)
        };
        
        await Employee.findByIdAndUpdate(employee._id, {
          salaryCalculationRules: rules
        });
        
        updatedCount++;
        
      } catch (error) {
        console.error(`❌ Error updating salary rules for employee ${employee._id}:`, error.message);
      }
    }
    
    console.log(`✅ Updated ${updatedCount} employees with salary calculation rules`);
    
  } catch (error) {
    console.error('❌ Error updating salary calculation rules:', error);
    throw error;
  }
}

/**
 * Update existing EmployeeSalary records with calculation types
 */
async function updateEmployeeSalaryCalculationTypes() {
  console.log('📝 Updating EmployeeSalary calculation types...');
  
  try {
    const employeeSalaries = await EmployeeSalary.find({
      calculationType: { $exists: false }
    }).populate('employeeId');
    
    console.log(`Found ${employeeSalaries.length} salary records to update`);
    
    let updatedCount = 0;
    
    for (const salary of employeeSalaries) {
      try {
        if (!salary.employeeId) continue;
        
        const employee = salary.employeeId;
        let calculationType = 'fixed';
        
        // Determine calculation type based on employee type
        if (employee.employmentType === 'part-time' || 
            employee.employmentSubType === 'attendance-based') {
          calculationType = 'hourly';
        } else if (employee.employmentType === 'contract' || 
                   employee.employmentSubType === 'task-based') {
          calculationType = 'task-based';
        } else if (employee.employmentSubType === 'hybrid') {
          calculationType = 'hybrid';
        }
        
        const updateData = { calculationType };
        
        // Add hourly rate if needed
        if (['hourly', 'hybrid'].includes(calculationType) && employee.hourlyRate) {
          updateData.hourlyRate = employee.hourlyRate;
        }
        
        await EmployeeSalary.findByIdAndUpdate(salary._id, updateData);
        updatedCount++;
        
      } catch (error) {
        console.error(`❌ Error updating salary record ${salary._id}:`, error.message);
      }
    }
    
    console.log(`✅ Updated ${updatedCount} salary records with calculation types`);
    
  } catch (error) {
    console.error('❌ Error updating salary calculation types:', error);
    throw error;
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateEmployeeSalaryIntegration()
    .then(() => {
      console.log('🎉 Migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration failed:', error);
      process.exit(1);
    });
}

module.exports = {
  migrateEmployeeSalaryIntegration,
  updateEmployeeRoles,
  updateEmploymentSubTypes,
  updateHourlyRates,
  updateSalaryCalculationRules,
  updateEmployeeSalaryCalculationTypes
};

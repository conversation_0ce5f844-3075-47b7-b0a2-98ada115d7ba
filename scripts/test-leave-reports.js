const mongoose = require('mongoose');

// Simple test script to check leave reports functionality
async function testLeaveReports() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/kawandama-hr';
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    // Check if Leave model exists
    const Leave = mongoose.models.Leave || mongoose.model('Leave', new mongoose.Schema({
      leaveId: String,
      employeeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Employee' },
      leaveTypeId: { type: mongoose.Schema.Types.ObjectId, ref: 'LeaveType' },
      startDate: Date,
      endDate: Date,
      duration: Number,
      reason: String,
      status: { type: String, enum: ['pending', 'approved', 'rejected', 'cancelled'] },
      approvedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      approvalDate: Date,
      rejectionReason: String,
      attachments: [String],
      notes: String,
      createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    }));

    // Check if LeaveType model exists
    const LeaveType = mongoose.models.LeaveType || mongoose.model('LeaveType', new mongoose.Schema({
      name: String,
      code: String,
      description: String,
      defaultDays: Number,
      isActive: Boolean,
      isPaid: Boolean,
      requiresApproval: Boolean,
      maxConsecutiveDays: Number,
      minNoticeInDays: Number,
      allowCarryOver: Boolean,
      maxCarryOverDays: Number,
      color: String,
      applicableRoles: [String],
      applicableDepartments: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Department' }],
      createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    }));

    // Check if Employee model exists
    const Employee = mongoose.models.Employee || mongoose.model('Employee', new mongoose.Schema({
      firstName: String,
      lastName: String,
      employeeId: String,
      email: String,
      departmentId: { type: mongoose.Schema.Types.ObjectId, ref: 'Department' },
      position: String,
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    }));

    console.log('\n=== DATABASE STATISTICS ===');
    
    // Count documents
    const leaveCount = await Leave.countDocuments();
    const leaveTypeCount = await LeaveType.countDocuments();
    const employeeCount = await Employee.countDocuments();
    
    console.log(`Leave records: ${leaveCount}`);
    console.log(`Leave types: ${leaveTypeCount}`);
    console.log(`Employees: ${employeeCount}`);

    if (leaveCount === 0) {
      console.log('\n⚠️  No leave records found! This is why reports are empty.');
      console.log('   You need to create sample leave data first.');
      console.log('   Use the debug page at /debug/leave-reports to create sample data.');
    } else {
      console.log('\n=== SAMPLE LEAVE RECORDS ===');
      const sampleLeaves = await Leave.find({}).limit(5).populate('leaveTypeId', 'name code');
      sampleLeaves.forEach((leave, index) => {
        console.log(`${index + 1}. ${leave.leaveId} - ${leave.leaveTypeId?.name || 'Unknown Type'} - ${leave.status} - ${leave.duration} days`);
      });

      console.log('\n=== LEAVE STATUS BREAKDOWN ===');
      const statusCounts = await Leave.aggregate([
        { $group: { _id: '$status', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]);
      statusCounts.forEach(status => {
        console.log(`${status._id}: ${status.count}`);
      });

      console.log('\n=== DATE RANGE ANALYSIS ===');
      const dateRange = await Leave.aggregate([
        {
          $group: {
            _id: null,
            minDate: { $min: '$startDate' },
            maxDate: { $max: '$startDate' },
            minCreated: { $min: '$createdAt' },
            maxCreated: { $max: '$createdAt' }
          }
        }
      ]);
      
      if (dateRange.length > 0) {
        const range = dateRange[0];
        console.log(`Leave date range: ${range.minDate?.toISOString().split('T')[0]} to ${range.maxDate?.toISOString().split('T')[0]}`);
        console.log(`Created date range: ${range.minCreated?.toISOString().split('T')[0]} to ${range.maxCreated?.toISOString().split('T')[0]}`);
      }
    }

    if (leaveTypeCount === 0) {
      console.log('\n⚠️  No leave types found! You need to seed leave types first.');
    } else {
      console.log('\n=== LEAVE TYPES ===');
      const leaveTypes = await LeaveType.find({ isActive: true }).select('name code');
      leaveTypes.forEach((type, index) => {
        console.log(`${index + 1}. ${type.name} (${type.code})`);
      });
    }

    if (employeeCount === 0) {
      console.log('\n⚠️  No employees found! You need to create employees first.');
    }

    console.log('\n=== RECOMMENDATIONS ===');
    if (leaveCount === 0) {
      console.log('1. Create sample leave data using /debug/leave-reports page');
      console.log('2. Or manually create leave requests through the HR module');
    }
    if (leaveTypeCount === 0) {
      console.log('3. Seed leave types using the seeder');
    }
    if (employeeCount === 0) {
      console.log('4. Create employee records first');
    }
    if (leaveCount > 0) {
      console.log('✅ You have leave data! Reports should work now.');
      console.log('   Try generating reports from /leave-management/reports');
    }

  } catch (error) {
    console.error('Error testing leave reports:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the test
testLeaveReports().catch(console.error);

/**
 * Test script for enhanced Employee and Salary models
 * This script validates that the new fields and models work correctly
 */

const mongoose = require('mongoose');
const { connectToDatabase } = require('../lib/backend/database');

// Import models
const Employee = require('../models/Employee').default;
const EmployeeSalary = require('../models/payroll/EmployeeSalary').default;
const EmployeeAttendanceSummary = require('../models/payroll/EmployeeAttendanceSummary').default;
const EmployeeTaskSummary = require('../models/payroll/EmployeeTaskSummary').default;
const SalaryCalculationRule = require('../models/payroll/SalaryCalculationRule').default;

/**
 * Main test function
 */
async function testEnhancedModels() {
  try {
    console.log('🧪 Starting Enhanced Models Test...');
    
    // Connect to database
    await connectToDatabase();
    console.log('✅ Connected to database');
    
    // Test Employee model enhancements
    await testEmployeeModel();
    
    // Test EmployeeSalary model enhancements
    await testEmployeeSalaryModel();
    
    // Test new models
    await testNewModels();
    
    console.log('✅ All tests passed successfully!');
    
  } catch (error) {
    console.error('❌ Tests failed:', error);
    throw error;
  }
}

/**
 * Test Employee model with new fields
 */
async function testEmployeeModel() {
  console.log('📝 Testing Employee model enhancements...');
  
  try {
    // Create a test employee with new fields
    const testEmployee = new Employee({
      employeeId: 'TEST-EMP-001',
      firstName: 'Test',
      lastName: 'Employee',
      email: '<EMAIL>',
      position: 'Software Developer',
      employmentType: 'part-time',
      employmentSubType: 'attendance-based',
      employmentStatus: 'active',
      hireDate: new Date(),
      hourlyRate: 1500,
      salaryCalculationRules: {
        attendanceRequired: true,
        taskCompletionRequired: false,
        minimumHours: 80,
        overtimeMultiplier: 1.5,
        performanceBonusEnabled: true
      },
      addedBy: new mongoose.Types.ObjectId(),
      lastModifiedBy: new mongoose.Types.ObjectId()
    });
    
    // Validate the model
    await testEmployee.validate();
    console.log('✅ Employee model validation passed');
    
    // Test employment sub-type validation
    const invalidEmployee = new Employee({
      employeeId: 'TEST-EMP-002',
      firstName: 'Invalid',
      lastName: 'Employee',
      email: '<EMAIL>',
      position: 'Tester',
      employmentType: 'part-time',
      employmentSubType: 'invalid-type', // This should fail
      employmentStatus: 'active',
      hireDate: new Date(),
      addedBy: new mongoose.Types.ObjectId(),
      lastModifiedBy: new mongoose.Types.ObjectId()
    });
    
    try {
      await invalidEmployee.validate();
      throw new Error('Validation should have failed for invalid employment sub-type');
    } catch (error) {
      if (error.message.includes('invalid-type')) {
        console.log('✅ Employment sub-type validation working correctly');
      } else {
        throw error;
      }
    }
    
    console.log('✅ Employee model tests passed');
    
  } catch (error) {
    console.error('❌ Employee model test failed:', error);
    throw error;
  }
}

/**
 * Test EmployeeSalary model with new fields
 */
async function testEmployeeSalaryModel() {
  console.log('📝 Testing EmployeeSalary model enhancements...');
  
  try {
    // Create a test salary record with new fields
    const testSalary = new EmployeeSalary({
      employeeId: new mongoose.Types.ObjectId(),
      salaryStructureId: new mongoose.Types.ObjectId(),
      basicSalary: 50000,
      currency: 'MWK',
      effectiveDate: new Date(),
      isActive: true,
      calculationType: 'hybrid',
      hourlyRate: 1500,
      taskRates: [
        {
          taskType: 'development',
          rate: 2000,
          rateType: 'per_hour',
          description: 'Software development tasks'
        },
        {
          taskType: 'testing',
          rate: 500,
          rateType: 'per_task',
          description: 'Testing and QA tasks'
        }
      ],
      attendanceRequirement: {
        minimumHoursPerDay: 4,
        minimumHoursPerWeek: 20,
        minimumHoursPerMonth: 80,
        overtimeThreshold: 8,
        lateDeductionRate: 0.1,
        absenceDeductionRate: 1.0
      },
      allowances: [],
      deductions: [],
      paymentMethod: 'bank_transfer',
      createdBy: new mongoose.Types.ObjectId()
    });
    
    // Validate the model
    await testSalary.validate();
    console.log('✅ EmployeeSalary model validation passed');
    
    // Test calculation type validation
    const invalidSalary = new EmployeeSalary({
      employeeId: new mongoose.Types.ObjectId(),
      salaryStructureId: new mongoose.Types.ObjectId(),
      basicSalary: 50000,
      currency: 'MWK',
      effectiveDate: new Date(),
      isActive: true,
      calculationType: 'hourly', // Requires hourlyRate
      // hourlyRate: missing - should fail validation
      allowances: [],
      deductions: [],
      paymentMethod: 'bank_transfer',
      createdBy: new mongoose.Types.ObjectId()
    });
    
    try {
      await invalidSalary.validate();
      throw new Error('Validation should have failed for missing hourly rate');
    } catch (error) {
      if (error.message.includes('hourly rate')) {
        console.log('✅ Hourly rate validation working correctly');
      } else {
        throw error;
      }
    }
    
    console.log('✅ EmployeeSalary model tests passed');
    
  } catch (error) {
    console.error('❌ EmployeeSalary model test failed:', error);
    throw error;
  }
}

/**
 * Test new models
 */
async function testNewModels() {
  console.log('📝 Testing new models...');
  
  try {
    // Test EmployeeAttendanceSummary
    const attendanceSummary = new EmployeeAttendanceSummary({
      employeeId: new mongoose.Types.ObjectId(),
      year: 2024,
      month: 1,
      totalWorkingDays: 22,
      daysPresent: 20,
      daysAbsent: 2,
      daysLate: 3,
      daysHalfDay: 1,
      daysOnLeave: 0,
      totalHours: 160,
      regularHours: 150,
      overtimeHours: 10,
      lateHours: 2,
      attendanceRate: 90.9,
      punctualityRate: 85,
      averageHoursPerDay: 8,
      calculatedBy: new mongoose.Types.ObjectId()
    });
    
    await attendanceSummary.validate();
    console.log('✅ EmployeeAttendanceSummary validation passed');
    
    // Test EmployeeTaskSummary
    const taskSummary = new EmployeeTaskSummary({
      employeeId: new mongoose.Types.ObjectId(),
      year: 2024,
      month: 1,
      totalTasksAssigned: 10,
      totalTasksCompleted: 8,
      totalTasksInProgress: 2,
      totalTasksOverdue: 1,
      totalEstimatedHours: 80,
      totalActualHours: 85,
      averageTaskCompletionTime: 10.6,
      efficiencyRatio: 1.06,
      completionRate: 80,
      onTimeCompletionRate: 75,
      averageQualityScore: 8.5,
      taskCompletions: [
        {
          taskId: new mongoose.Types.ObjectId(),
          taskTitle: 'Test Task',
          taskType: 'development',
          completedDate: new Date(),
          estimatedHours: 8,
          actualHours: 10,
          qualityScore: 9,
          difficultyLevel: 'medium',
          paymentAmount: 2000,
          paymentType: 'per_hour'
        }
      ],
      calculatedBy: new mongoose.Types.ObjectId()
    });
    
    await taskSummary.validate();
    console.log('✅ EmployeeTaskSummary validation passed');
    
    // Test SalaryCalculationRule
    const salaryRule = new SalaryCalculationRule({
      name: 'Perfect Attendance Bonus',
      description: 'Bonus for employees with perfect attendance',
      isActive: true,
      employmentTypes: ['full-time', 'part-time'],
      employmentSubTypes: ['standard', 'attendance-based'],
      priority: 80,
      executionOrder: 1,
      conditions: [
        {
          field: 'attendanceRate',
          operator: 'eq',
          value: 100
        }
      ],
      conditionsLogic: 'all',
      actions: [
        {
          type: 'add_bonus',
          amount: 5000,
          description: 'Perfect attendance bonus'
        }
      ],
      effectiveDate: new Date(),
      createdBy: new mongoose.Types.ObjectId()
    });
    
    await salaryRule.validate();
    console.log('✅ SalaryCalculationRule validation passed');
    
    console.log('✅ All new models tests passed');
    
  } catch (error) {
    console.error('❌ New models test failed:', error);
    throw error;
  }
}

// Run tests if called directly
if (require.main === module) {
  testEnhancedModels()
    .then(() => {
      console.log('🎉 All tests completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Tests failed:', error);
      process.exit(1);
    });
}

module.exports = {
  testEnhancedModels,
  testEmployeeModel,
  testEmployeeSalaryModel,
  testNewModels
};

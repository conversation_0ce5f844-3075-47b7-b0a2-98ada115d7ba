import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import Employee from '@/models/Employee';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';
import EmployeeAttendanceSummary from '@/models/payroll/EmployeeAttendanceSummary';
import EmployeeTaskSummary from '@/models/payroll/EmployeeTaskSummary';
import SalaryCalculationRule from '@/models/payroll/SalaryCalculationRule';

/**
 * Interface for salary calculation input
 */
export interface SalaryCalculationInput {
  employeeId: string;
  payPeriod: { month: number; year: number };
  attendanceData?: AttendanceSummary;
  taskData?: TaskSummary;
  overrides?: SalaryOverrides;
}

/**
 * Interface for attendance summary data
 */
export interface AttendanceSummary {
  totalHours: number;
  overtimeHours: number;
  daysPresent: number;
  daysAbsent: number;
  lateCount: number;
  attendanceRate: number;
  punctualityRate: number;
  averageHoursPerDay: number;
}

/**
 * Interface for task summary data
 */
export interface TaskSummary {
  tasksCompleted: number;
  totalTaskHours: number;
  projectsCompleted: number;
  performanceScore: number;
  completionRate: number;
  onTimeCompletionRate: number;
  averageQualityScore: number;
  efficiencyRatio: number;
}

/**
 * Interface for salary calculation overrides
 */
export interface SalaryOverrides {
  basicSalary?: number;
  hourlyRate?: number;
  bonusAmount?: number;
  deductionAmount?: number;
  skipRules?: boolean;
}

/**
 * Interface for salary calculation result
 */
export interface SalaryResult {
  employeeId: string;
  employeeName: string;
  employmentType: string;
  employmentSubType: string;
  calculationType: string;
  
  // Base calculations
  basicSalary: number;
  hourlyRate?: number;
  hoursWorked?: number;
  tasksCompleted?: number;
  
  // Components
  components: SalaryComponent[];
  grossSalary: number;
  totalDeductions: number;
  totalTax: number;
  netSalary: number;
  currency: string;
  
  // Performance metrics
  attendanceBonus?: number;
  taskBonus?: number;
  performanceBonus?: number;
  penalties?: number;
  
  // Calculation details
  calculationDetails: {
    method: string;
    rulesApplied: string[];
    attendanceData?: AttendanceSummary;
    taskData?: TaskSummary;
    overrides?: SalaryOverrides;
  };
  
  error?: string;
  message?: string;
}

/**
 * Interface for salary component
 */
export interface SalaryComponent {
  name: string;
  type: 'basic' | 'allowance' | 'deduction' | 'tax' | 'bonus' | 'penalty';
  amount: number;
  isTaxable: boolean;
  description?: string;
  calculationBasis?: string;
}

/**
 * Enhanced Payroll Calculation Service
 * Handles variable salary calculations for different employee types
 */
export class EnhancedPayrollCalculationService {
  
  /**
   * Calculate salary for any employee type with unified logic
   * @param input - Salary calculation input
   * @returns Calculated salary result
   */
  async calculateEmployeeSalary(input: SalaryCalculationInput): Promise<SalaryResult> {
    try {
      await connectToDatabase();
      
      // Get employee with role information
      const employee = await Employee.findById(input.employeeId)
        .populate('roleId')
        .populate('departmentId');
      
      if (!employee) {
        throw new Error(`Employee ${input.employeeId} not found`);
      }
      
      // Get employee salary configuration
      const employeeSalary = await EmployeeSalary.findOne({
        employeeId: input.employeeId,
        isActive: true,
        effectiveDate: { $lte: new Date() },
        $or: [
          { endDate: { $gt: new Date() } },
          { endDate: { $exists: false } },
          { endDate: null }
        ]
      }).sort({ effectiveDate: -1 });
      
      if (!employeeSalary) {
        return this.createErrorResult(input.employeeId, 'No active salary configuration found');
      }
      
      // Get attendance and task data if not provided
      const attendanceData = input.attendanceData || 
        await this.getAttendanceData(input.employeeId, input.payPeriod);
      const taskData = input.taskData || 
        await this.getTaskData(input.employeeId, input.payPeriod);
      
      // Route to appropriate calculation method based on employee type
      const calculationType = employeeSalary.calculationType || 'fixed';
      
      let result: SalaryResult;
      
      switch (calculationType) {
        case 'fixed':
          result = await this.calculateFixedSalary(employee, employeeSalary, input.payPeriod, input.overrides);
          break;
        case 'hourly':
          result = await this.calculateHourlySalary(employee, employeeSalary, attendanceData, input.payPeriod, input.overrides);
          break;
        case 'task-based':
          result = await this.calculateTaskBasedSalary(employee, employeeSalary, taskData, input.payPeriod, input.overrides);
          break;
        case 'hybrid':
          result = await this.calculateHybridSalary(employee, employeeSalary, attendanceData, taskData, input.payPeriod, input.overrides);
          break;
        default:
          throw new Error(`Unknown calculation type: ${calculationType}`);
      }
      
      // Apply salary calculation rules if not skipped
      if (!input.overrides?.skipRules) {
        result = await this.applySalaryRules(result, employee, attendanceData, taskData);
      }
      
      // Add calculation metadata
      result.calculationDetails = {
        method: calculationType,
        rulesApplied: result.calculationDetails?.rulesApplied || [],
        attendanceData,
        taskData,
        overrides: input.overrides
      };
      
      logger.info('Enhanced salary calculation completed', LogCategory.PAYROLL, {
        employeeId: input.employeeId,
        calculationType,
        grossSalary: result.grossSalary,
        netSalary: result.netSalary
      });
      
      return result;
      
    } catch (error) {
      logger.error('Error in enhanced salary calculation', LogCategory.PAYROLL, error);
      return this.createErrorResult(input.employeeId, error instanceof Error ? error.message : 'Unknown error');
    }
  }
  
  /**
   * Calculate fixed salary (traditional full-time employees)
   */
  async calculateFixedSalary(
    employee: any,
    employeeSalary: any,
    payPeriod: { month: number; year: number },
    overrides?: SalaryOverrides
  ): Promise<SalaryResult> {
    const basicSalary = overrides?.basicSalary || employeeSalary.basicSalary;
    const components: SalaryComponent[] = [];
    
    // Add basic salary component
    components.push({
      name: 'Basic Salary',
      type: 'basic',
      amount: basicSalary,
      isTaxable: true,
      description: 'Fixed monthly salary'
    });
    
    // Add allowances from salary configuration
    for (const allowance of employeeSalary.allowances || []) {
      const amount = allowance.amount || (allowance.percentage * basicSalary / 100);
      components.push({
        name: allowance.name,
        type: 'allowance',
        amount,
        isTaxable: allowance.isTaxable,
        description: `${allowance.name} allowance`
      });
    }
    
    // Add deductions from salary configuration
    for (const deduction of employeeSalary.deductions || []) {
      const amount = deduction.amount || (deduction.percentage * basicSalary / 100);
      components.push({
        name: deduction.name,
        type: 'deduction',
        amount: -Math.abs(amount),
        isTaxable: false,
        description: `${deduction.name} deduction`
      });
    }
    
    return this.calculateFinalSalary(employee, components, 'fixed');
  }
  
  /**
   * Get attendance data for the pay period
   */
  private async getAttendanceData(employeeId: string, payPeriod: { month: number; year: number }): Promise<AttendanceSummary | undefined> {
    try {
      const summary = await EmployeeAttendanceSummary.findOne({
        employeeId,
        year: payPeriod.year,
        month: payPeriod.month
      });
      
      if (!summary) return undefined;
      
      return {
        totalHours: summary.totalHours,
        overtimeHours: summary.overtimeHours,
        daysPresent: summary.daysPresent,
        daysAbsent: summary.daysAbsent,
        lateCount: summary.daysLate,
        attendanceRate: summary.attendanceRate,
        punctualityRate: summary.punctualityRate,
        averageHoursPerDay: summary.averageHoursPerDay
      };
    } catch (error) {
      logger.warn('Could not get attendance data', LogCategory.PAYROLL, { employeeId, payPeriod, error });
      return undefined;
    }
  }
  
  /**
   * Get task data for the pay period
   */
  private async getTaskData(employeeId: string, payPeriod: { month: number; year: number }): Promise<TaskSummary | undefined> {
    try {
      const summary = await EmployeeTaskSummary.findOne({
        employeeId,
        year: payPeriod.year,
        month: payPeriod.month
      });
      
      if (!summary) return undefined;
      
      return {
        tasksCompleted: summary.totalTasksCompleted,
        totalTaskHours: summary.totalActualHours,
        projectsCompleted: 0, // Will be calculated from task data
        performanceScore: summary.averageQualityScore,
        completionRate: summary.completionRate,
        onTimeCompletionRate: summary.onTimeCompletionRate,
        averageQualityScore: summary.averageQualityScore,
        efficiencyRatio: summary.efficiencyRatio
      };
    } catch (error) {
      logger.warn('Could not get task data', LogCategory.PAYROLL, { employeeId, payPeriod, error });
      return undefined;
    }
  }
  
  /**
   * Create error result
   */
  private createErrorResult(employeeId: string, message: string): SalaryResult {
    return {
      employeeId,
      employeeName: 'Unknown',
      employmentType: 'unknown',
      employmentSubType: 'unknown',
      calculationType: 'unknown',
      basicSalary: 0,
      components: [],
      grossSalary: 0,
      totalDeductions: 0,
      totalTax: 0,
      netSalary: 0,
      currency: 'MWK',
      calculationDetails: {
        method: 'error',
        rulesApplied: []
      },
      error: 'CALCULATION_ERROR',
      message
    };
  }
  
  /**
   * Calculate final salary from components
   */
  private async calculateFinalSalary(employee: any, components: SalaryComponent[], method: string): Promise<SalaryResult> {
    // Calculate gross salary
    const grossSalary = components
      .filter(c => ['basic', 'allowance', 'bonus'].includes(c.type))
      .reduce((sum, c) => sum + c.amount, 0);
    
    // Calculate taxable income
    const taxableIncome = components
      .filter(c => c.isTaxable && c.amount > 0)
      .reduce((sum, c) => sum + c.amount, 0);
    
    // Calculate tax
    const taxAmount = this.calculateTax(taxableIncome);
    if (taxAmount > 0) {
      components.push({
        name: 'Income Tax',
        type: 'tax',
        amount: -taxAmount,
        isTaxable: false,
        description: 'PAYE income tax'
      });
    }
    
    // Calculate total deductions
    const totalDeductions = components
      .filter(c => ['deduction', 'tax'].includes(c.type))
      .reduce((sum, c) => sum + Math.abs(c.amount), 0);
    
    // Calculate net salary
    const netSalary = grossSalary - totalDeductions;
    
    return {
      employeeId: employee._id.toString(),
      employeeName: `${employee.firstName} ${employee.lastName}`,
      employmentType: employee.employmentType,
      employmentSubType: employee.employmentSubType || 'standard',
      calculationType: method,
      basicSalary: components.find(c => c.type === 'basic')?.amount || 0,
      components,
      grossSalary,
      totalDeductions,
      totalTax: taxAmount,
      netSalary,
      currency: 'MWK',
      calculationDetails: {
        method,
        rulesApplied: []
      }
    };
  }
  
  /**
   * Calculate hourly salary (part-time, attendance-based employees)
   */
  async calculateHourlySalary(
    employee: any,
    employeeSalary: any,
    attendanceData: AttendanceSummary | undefined,
    payPeriod: { month: number; year: number },
    overrides?: SalaryOverrides
  ): Promise<SalaryResult> {
    const hourlyRate = overrides?.hourlyRate || employeeSalary.hourlyRate || employee.hourlyRate;

    if (!hourlyRate) {
      throw new Error('Hourly rate not configured for hourly employee');
    }

    if (!attendanceData) {
      throw new Error('Attendance data required for hourly salary calculation');
    }

    const components: SalaryComponent[] = [];

    // Calculate regular hours pay
    const regularHours = Math.min(attendanceData.totalHours, attendanceData.totalHours - attendanceData.overtimeHours);
    const regularPay = regularHours * hourlyRate;

    components.push({
      name: 'Regular Hours Pay',
      type: 'basic',
      amount: regularPay,
      isTaxable: true,
      description: `${regularHours} hours @ ${hourlyRate}/hour`,
      calculationBasis: 'attendance'
    });

    // Calculate overtime pay
    if (attendanceData.overtimeHours > 0) {
      const overtimeMultiplier = employee.salaryCalculationRules?.overtimeMultiplier || 1.5;
      const overtimePay = attendanceData.overtimeHours * hourlyRate * overtimeMultiplier;

      components.push({
        name: 'Overtime Pay',
        type: 'allowance',
        amount: overtimePay,
        isTaxable: true,
        description: `${attendanceData.overtimeHours} hours @ ${hourlyRate * overtimeMultiplier}/hour`,
        calculationBasis: 'attendance'
      });
    }

    // Apply attendance penalties
    if (attendanceData.lateCount > 0 && employeeSalary.attendanceRequirement?.lateDeductionRate) {
      const latePenalty = regularPay * employeeSalary.attendanceRequirement.lateDeductionRate * attendanceData.lateCount;
      components.push({
        name: 'Late Arrival Penalty',
        type: 'penalty',
        amount: -latePenalty,
        isTaxable: false,
        description: `${attendanceData.lateCount} late arrivals`,
        calculationBasis: 'attendance'
      });
    }

    return this.calculateFinalSalary(employee, components, 'hourly');
  }

  /**
   * Calculate task-based salary (contract, project-based employees)
   */
  async calculateTaskBasedSalary(
    employee: any,
    employeeSalary: any,
    taskData: TaskSummary | undefined,
    payPeriod: { month: number; year: number },
    overrides?: SalaryOverrides
  ): Promise<SalaryResult> {
    if (!taskData) {
      throw new Error('Task data required for task-based salary calculation');
    }

    const components: SalaryComponent[] = [];

    // Calculate task completion payments
    if (employeeSalary.taskRates && employeeSalary.taskRates.length > 0) {
      for (const taskRate of employeeSalary.taskRates) {
        let payment = 0;

        switch (taskRate.rateType) {
          case 'per_task':
            payment = taskData.tasksCompleted * taskRate.rate;
            break;
          case 'per_hour':
            payment = taskData.totalTaskHours * taskRate.rate;
            break;
          case 'per_project':
            payment = taskData.projectsCompleted * taskRate.rate;
            break;
        }

        if (payment > 0) {
          components.push({
            name: `${taskRate.taskType} Payment`,
            type: 'basic',
            amount: payment,
            isTaxable: true,
            description: taskRate.description || `Payment for ${taskRate.taskType} tasks`,
            calculationBasis: 'tasks'
          });
        }
      }
    } else {
      // Fallback to basic salary if no task rates configured
      const basicSalary = overrides?.basicSalary || employeeSalary.basicSalary;
      components.push({
        name: 'Basic Task Payment',
        type: 'basic',
        amount: basicSalary,
        isTaxable: true,
        description: 'Basic task-based payment',
        calculationBasis: 'tasks'
      });
    }

    // Apply performance bonuses
    if (taskData.averageQualityScore > 8) {
      const qualityBonus = components
        .filter(c => c.type === 'basic')
        .reduce((sum, c) => sum + c.amount, 0) * 0.1; // 10% bonus for high quality

      components.push({
        name: 'Quality Bonus',
        type: 'bonus',
        amount: qualityBonus,
        isTaxable: true,
        description: `Quality score: ${taskData.averageQualityScore}/10`,
        calculationBasis: 'performance'
      });
    }

    // Apply efficiency bonuses
    if (taskData.efficiencyRatio < 1) { // Completed tasks faster than estimated
      const efficiencyBonus = components
        .filter(c => c.type === 'basic')
        .reduce((sum, c) => sum + c.amount, 0) * 0.05; // 5% bonus for efficiency

      components.push({
        name: 'Efficiency Bonus',
        type: 'bonus',
        amount: efficiencyBonus,
        isTaxable: true,
        description: `Efficiency ratio: ${taskData.efficiencyRatio}`,
        calculationBasis: 'performance'
      });
    }

    return this.calculateFinalSalary(employee, components, 'task-based');
  }

  /**
   * Calculate hybrid salary (combination of fixed, hourly, and task-based)
   */
  async calculateHybridSalary(
    employee: any,
    employeeSalary: any,
    attendanceData: AttendanceSummary | undefined,
    taskData: TaskSummary | undefined,
    payPeriod: { month: number; year: number },
    overrides?: SalaryOverrides
  ): Promise<SalaryResult> {
    const components: SalaryComponent[] = [];

    // Base salary component (reduced for hybrid)
    const baseSalary = (overrides?.basicSalary || employeeSalary.basicSalary) * 0.6; // 60% of full salary
    components.push({
      name: 'Base Salary',
      type: 'basic',
      amount: baseSalary,
      isTaxable: true,
      description: 'Base component of hybrid salary',
      calculationBasis: 'fixed'
    });

    // Attendance component (if data available)
    if (attendanceData && employeeSalary.hourlyRate) {
      const attendanceBonus = Math.min(attendanceData.totalHours, 40) * employeeSalary.hourlyRate * 0.3; // 30% of hourly rate
      components.push({
        name: 'Attendance Component',
        type: 'allowance',
        amount: attendanceBonus,
        isTaxable: true,
        description: `${attendanceData.totalHours} hours attendance`,
        calculationBasis: 'attendance'
      });
    }

    // Task component (if data available)
    if (taskData && employeeSalary.taskRates && employeeSalary.taskRates.length > 0) {
      const taskBonus = taskData.tasksCompleted * (employeeSalary.taskRates[0]?.rate || 500) * 0.5; // 50% of task rate
      components.push({
        name: 'Task Component',
        type: 'allowance',
        amount: taskBonus,
        isTaxable: true,
        description: `${taskData.tasksCompleted} tasks completed`,
        calculationBasis: 'tasks'
      });
    }

    return this.calculateFinalSalary(employee, components, 'hybrid');
  }

  /**
   * Apply salary calculation rules
   */
  async applySalaryRules(
    result: SalaryResult,
    employee: any,
    attendanceData?: AttendanceSummary,
    taskData?: TaskSummary
  ): Promise<SalaryResult> {
    try {
      // Find applicable rules
      const rules = await SalaryCalculationRule.find({
        isActive: true,
        employmentTypes: employee.employmentType,
        employmentSubTypes: employee.employmentSubType || 'standard',
        effectiveDate: { $lte: new Date() },
        $or: [
          { expiryDate: { $exists: false } },
          { expiryDate: { $gte: new Date() } }
        ]
      }).sort({ priority: -1, executionOrder: 1 });

      const rulesApplied: string[] = [];

      for (const rule of rules) {
        // Check if rule conditions are met
        const conditionsMet = this.evaluateRuleConditions(rule, result, attendanceData, taskData);

        if (conditionsMet) {
          // Apply rule actions
          for (const action of rule.actions) {
            this.applyRuleAction(result, action, rule.name);
          }
          rulesApplied.push(rule.name);
        }
      }

      // Recalculate totals after applying rules
      result.grossSalary = result.components
        .filter(c => ['basic', 'allowance', 'bonus'].includes(c.type))
        .reduce((sum, c) => sum + c.amount, 0);

      result.totalDeductions = result.components
        .filter(c => ['deduction', 'tax', 'penalty'].includes(c.type))
        .reduce((sum, c) => sum + Math.abs(c.amount), 0);

      result.netSalary = result.grossSalary - result.totalDeductions;

      result.calculationDetails.rulesApplied = rulesApplied;

      return result;

    } catch (error) {
      logger.warn('Error applying salary rules', LogCategory.PAYROLL, { employeeId: result.employeeId, error });
      return result;
    }
  }

  /**
   * Evaluate rule conditions
   */
  private evaluateRuleConditions(
    rule: any,
    result: SalaryResult,
    attendanceData?: AttendanceSummary,
    taskData?: TaskSummary
  ): boolean {
    const data = {
      ...result,
      ...attendanceData,
      ...taskData
    };

    if (rule.conditionsLogic === 'all') {
      return rule.conditions.every((condition: any) => this.evaluateCondition(condition, data));
    } else {
      return rule.conditions.some((condition: any) => this.evaluateCondition(condition, data));
    }
  }

  /**
   * Evaluate single condition
   */
  private evaluateCondition(condition: any, data: any): boolean {
    const fieldValue = data[condition.field];
    const targetValue = condition.value;

    switch (condition.operator) {
      case 'gt': return fieldValue > targetValue;
      case 'gte': return fieldValue >= targetValue;
      case 'lt': return fieldValue < targetValue;
      case 'lte': return fieldValue <= targetValue;
      case 'eq': return fieldValue === targetValue;
      case 'ne': return fieldValue !== targetValue;
      case 'in': return Array.isArray(targetValue) && targetValue.includes(fieldValue);
      case 'nin': return Array.isArray(targetValue) && !targetValue.includes(fieldValue);
      default: return false;
    }
  }

  /**
   * Apply rule action
   */
  private applyRuleAction(result: SalaryResult, action: any, ruleName: string): void {
    switch (action.type) {
      case 'add_bonus':
        result.components.push({
          name: `${ruleName} Bonus`,
          type: 'bonus',
          amount: action.amount,
          isTaxable: true,
          description: action.description,
          calculationBasis: 'rule'
        });
        break;

      case 'apply_deduction':
        result.components.push({
          name: `${ruleName} Deduction`,
          type: 'deduction',
          amount: -Math.abs(action.amount),
          isTaxable: false,
          description: action.description,
          calculationBasis: 'rule'
        });
        break;

      case 'multiply_rate':
        // Apply percentage to all basic components
        result.components
          .filter(c => c.type === 'basic')
          .forEach(c => {
            c.amount *= (1 + action.percentage / 100);
          });
        break;
    }
  }

  /**
   * Calculate tax using Malawi PAYE brackets
   */
  private calculateTax(taxableAmount: number): number {
    if (taxableAmount <= 150000) return 0;
    if (taxableAmount <= 500000) return (taxableAmount - 150000) * 0.25;
    if (taxableAmount <= 2550000) return 87500 + (taxableAmount - 500000) * 0.3;
    return 702500 + (taxableAmount - 2550000) * 0.35;
  }
}

// Export singleton instance
export const enhancedPayrollCalculationService = new EnhancedPayrollCalculationService();

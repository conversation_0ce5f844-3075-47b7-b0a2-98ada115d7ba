import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import Employee from '@/models/Employee';
import Task from '@/models/project/Task';
import EmployeeTaskSummary from '@/models/payroll/EmployeeTaskSummary';
import EmployeeSalary from '@/models/payroll/EmployeeSalary';
import { startOfMonth, endOfMonth } from 'date-fns';

/**
 * Interface for task calculation options
 */
export interface TaskCalculationOptions {
  includeInProgressTasks?: boolean;
  qualityScoreThreshold?: number;
  onTimeThresholdDays?: number;
  performanceBonusThreshold?: number;
}

/**
 * Interface for task summary generation result
 */
export interface TaskSummaryResult {
  success: boolean;
  summary?: any;
  error?: string;
  message?: string;
}

/**
 * Task Payroll Integration Service
 * Handles integration between task management and payroll calculation
 */
export class TaskPayrollIntegrationService {
  
  /**
   * Generate monthly task summary for an employee
   * @param employeeId - Employee ID
   * @param year - Year
   * @param month - Month (1-12)
   * @param userId - User ID generating the summary
   * @param options - Calculation options
   * @returns Generated task summary
   */
  async generateMonthlySummary(
    employeeId: string,
    year: number,
    month: number,
    userId: string,
    options: TaskCalculationOptions = {}
  ): Promise<TaskSummaryResult> {
    try {
      await connectToDatabase();
      
      // Validate employee exists
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        return {
          success: false,
          error: 'EMPLOYEE_NOT_FOUND',
          message: `Employee ${employeeId} not found`
        };
      }
      
      // Check if summary already exists
      const existingSummary = await EmployeeTaskSummary.findOne({
        employeeId,
        year,
        month
      });
      
      if (existingSummary && existingSummary.isFinalized) {
        return {
          success: false,
          error: 'SUMMARY_ALREADY_FINALIZED',
          message: 'Task summary for this period is already finalized'
        };
      }
      
      // Get task records for the month
      const startDate = startOfMonth(new Date(year, month - 1));
      const endDate = endOfMonth(new Date(year, month - 1));
      
      // Get tasks assigned to employee in the period
      const assignedTasks = await Task.find({
        assignedTo: { $in: [employeeId] },
        $or: [
          { startDate: { $gte: startDate, $lte: endDate } },
          { dueDate: { $gte: startDate, $lte: endDate } },
          { completedDate: { $gte: startDate, $lte: endDate } }
        ]
      }).sort({ createdAt: 1 });
      
      // Get completed tasks in the period
      const completedTasks = await Task.find({
        assignedTo: { $in: [employeeId] },
        status: 'completed',
        completedDate: { $gte: startDate, $lte: endDate }
      });
      
      // Calculate task statistics
      const stats = await this.calculateTaskStatistics(
        assignedTasks,
        completedTasks,
        year,
        month,
        options
      );
      
      // Calculate task completions with payment details
      const taskCompletions = await this.calculateTaskCompletions(
        employee,
        completedTasks,
        options
      );
      
      // Calculate financial implications
      const financials = await this.calculateTaskFinancials(
        employee,
        stats,
        taskCompletions,
        options
      );
      
      // Create or update summary
      const summaryData = {
        employeeId,
        year,
        month,
        ...stats,
        ...financials,
        taskCompletions,
        calculatedAt: new Date(),
        calculatedBy: new mongoose.Types.ObjectId(userId),
        isFinalized: false
      };
      
      let summary;
      if (existingSummary) {
        summary = await EmployeeTaskSummary.findByIdAndUpdate(
          existingSummary._id,
          summaryData,
          { new: true }
        );
      } else {
        summary = new EmployeeTaskSummary(summaryData);
        await summary.save();
      }
      
      logger.info('Monthly task summary generated', LogCategory.PAYROLL, {
        employeeId,
        year,
        month,
        completionRate: stats.completionRate,
        totalTaskPayment: financials.totalTaskPayment
      });
      
      return {
        success: true,
        summary,
        message: 'Task summary generated successfully'
      };
      
    } catch (error) {
      logger.error('Error generating monthly task summary', LogCategory.PAYROLL, error);
      return {
        success: false,
        error: 'GENERATION_ERROR',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Calculate task statistics from records
   */
  private async calculateTaskStatistics(
    assignedTasks: any[],
    completedTasks: any[],
    year: number,
    month: number,
    options: TaskCalculationOptions
  ) {
    const onTimeThresholdDays = options.onTimeThresholdDays || 0;
    
    // Count tasks by status
    const totalTasksAssigned = assignedTasks.length;
    const totalTasksCompleted = completedTasks.length;
    const totalTasksInProgress = assignedTasks.filter(t => t.status === 'in-progress').length;
    const totalTasksOverdue = assignedTasks.filter(t => 
      t.status !== 'completed' && t.dueDate && new Date(t.dueDate) < new Date()
    ).length;
    
    // Calculate time statistics
    let totalEstimatedHours = 0;
    let totalActualHours = 0;
    let onTimeCompletions = 0;
    let qualityScores: number[] = [];
    
    for (const task of completedTasks) {
      if (task.estimatedHours) {
        totalEstimatedHours += task.estimatedHours;
      }
      if (task.actualHours) {
        totalActualHours += task.actualHours;
      }
      
      // Check if completed on time
      if (task.dueDate && task.completedDate) {
        const dueDate = new Date(task.dueDate);
        const completedDate = new Date(task.completedDate);
        const daysDifference = (completedDate.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24);
        
        if (daysDifference <= onTimeThresholdDays) {
          onTimeCompletions++;
        }
      }
      
      // Collect quality scores (if available in task comments or custom fields)
      // This would need to be implemented based on your quality scoring system
      // For now, we'll use a placeholder
      if (task.qualityScore) {
        qualityScores.push(task.qualityScore);
      }
    }
    
    // Calculate rates and averages
    const completionRate = totalTasksAssigned > 0 ? (totalTasksCompleted / totalTasksAssigned) * 100 : 0;
    const onTimeCompletionRate = totalTasksCompleted > 0 ? (onTimeCompletions / totalTasksCompleted) * 100 : 0;
    const averageTaskCompletionTime = totalTasksCompleted > 0 ? totalActualHours / totalTasksCompleted : 0;
    const efficiencyRatio = totalEstimatedHours > 0 ? totalActualHours / totalEstimatedHours : 0;
    const averageQualityScore = qualityScores.length > 0 ? 
      qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length : 0;
    
    return {
      totalTasksAssigned,
      totalTasksCompleted,
      totalTasksInProgress,
      totalTasksOverdue,
      totalEstimatedHours,
      totalActualHours,
      averageTaskCompletionTime: Math.round(averageTaskCompletionTime * 100) / 100,
      efficiencyRatio: Math.round(efficiencyRatio * 100) / 100,
      completionRate: Math.round(completionRate * 100) / 100,
      onTimeCompletionRate: Math.round(onTimeCompletionRate * 100) / 100,
      averageQualityScore: Math.round(averageQualityScore * 100) / 100
    };
  }
  
  /**
   * Calculate task completions with payment details
   */
  private async calculateTaskCompletions(
    employee: any,
    completedTasks: any[],
    options: TaskCalculationOptions
  ) {
    const taskCompletions = [];
    
    // Get employee's salary configuration for task rates
    const employeeSalary = await EmployeeSalary.findOne({
      employeeId: employee._id,
      isActive: true
    });
    
    const taskRates = employeeSalary?.taskRates || [];
    const defaultTaskRate = 1000; // Default rate per task
    
    for (const task of completedTasks) {
      // Determine task type (could be from task category or custom field)
      const taskType = task.category || 'general';
      
      // Find matching task rate
      const taskRate = taskRates.find(rate => rate.taskType === taskType) || {
        taskType: 'general',
        rate: defaultTaskRate,
        rateType: 'per_task'
      };
      
      // Calculate payment amount
      let paymentAmount = 0;
      switch (taskRate.rateType) {
        case 'per_task':
          paymentAmount = taskRate.rate;
          break;
        case 'per_hour':
          paymentAmount = (task.actualHours || task.estimatedHours || 1) * taskRate.rate;
          break;
        case 'per_project':
          // For project-based, we might need to check if this is the last task of a project
          paymentAmount = taskRate.rate;
          break;
      }
      
      // Apply quality multiplier if quality score is available
      if (task.qualityScore && task.qualityScore > 0) {
        const qualityMultiplier = Math.min(task.qualityScore / 10, 1.2); // Max 20% bonus for quality
        paymentAmount *= qualityMultiplier;
      }
      
      taskCompletions.push({
        taskId: task._id,
        taskTitle: task.title,
        taskType,
        completedDate: task.completedDate,
        estimatedHours: task.estimatedHours || 0,
        actualHours: task.actualHours || 0,
        qualityScore: task.qualityScore || 0,
        difficultyLevel: task.priority === 'urgent' ? 'expert' : 
                        task.priority === 'high' ? 'hard' :
                        task.priority === 'medium' ? 'medium' : 'easy',
        paymentAmount: Math.round(paymentAmount),
        paymentType: taskRate.rateType
      });
    }
    
    return taskCompletions;
  }
  
  /**
   * Calculate financial implications of task completion
   */
  private async calculateTaskFinancials(
    employee: any,
    stats: any,
    taskCompletions: any[],
    options: TaskCalculationOptions
  ) {
    // Calculate total task payment
    const totalTaskPayment = taskCompletions.reduce((sum, completion) => sum + completion.paymentAmount, 0);
    
    // Calculate performance bonuses
    let bonusPayment = 0;
    const performanceBonusThreshold = options.performanceBonusThreshold || 90;
    
    // Completion rate bonus
    if (stats.completionRate >= performanceBonusThreshold) {
      bonusPayment += totalTaskPayment * 0.1; // 10% bonus for high completion rate
    }
    
    // Quality bonus
    if (stats.averageQualityScore >= 8) {
      bonusPayment += totalTaskPayment * 0.05; // 5% bonus for high quality
    }
    
    // Efficiency bonus
    if (stats.efficiencyRatio < 1 && stats.efficiencyRatio > 0) {
      bonusPayment += totalTaskPayment * 0.05; // 5% bonus for efficiency
    }
    
    // Calculate penalties
    let penaltyDeduction = 0;
    
    // Late completion penalty
    if (stats.onTimeCompletionRate < 70) {
      penaltyDeduction += totalTaskPayment * 0.05; // 5% penalty for poor on-time performance
    }
    
    // Low quality penalty
    if (stats.averageQualityScore < 5 && stats.averageQualityScore > 0) {
      penaltyDeduction += totalTaskPayment * 0.1; // 10% penalty for low quality
    }
    
    return {
      totalTaskPayment: Math.round(totalTaskPayment),
      bonusPayment: Math.round(bonusPayment),
      penaltyDeduction: Math.round(penaltyDeduction)
    };
  }
  
  /**
   * Finalize task summary for payroll processing
   * @param employeeId - Employee ID
   * @param year - Year
   * @param month - Month
   * @param userId - User ID finalizing the summary
   * @returns Finalization result
   */
  async finalizeMonthlySummary(
    employeeId: string,
    year: number,
    month: number,
    userId: string
  ): Promise<TaskSummaryResult> {
    try {
      await connectToDatabase();
      
      const summary = await EmployeeTaskSummary.findOne({
        employeeId,
        year,
        month
      });
      
      if (!summary) {
        return {
          success: false,
          error: 'SUMMARY_NOT_FOUND',
          message: 'Task summary not found'
        };
      }
      
      if (summary.isFinalized) {
        return {
          success: false,
          error: 'ALREADY_FINALIZED',
          message: 'Summary is already finalized'
        };
      }
      
      summary.isFinalized = true;
      summary.finalizedAt = new Date();
      summary.finalizedBy = new mongoose.Types.ObjectId(userId);
      
      await summary.save();
      
      logger.info('Task summary finalized', LogCategory.PAYROLL, {
        employeeId,
        year,
        month,
        userId
      });
      
      return {
        success: true,
        summary,
        message: 'Task summary finalized successfully'
      };
      
    } catch (error) {
      logger.error('Error finalizing task summary', LogCategory.PAYROLL, error);
      return {
        success: false,
        error: 'FINALIZATION_ERROR',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Get task summary for payroll calculation
   * @param employeeId - Employee ID
   * @param year - Year
   * @param month - Month
   * @returns Task summary or null
   */
  async getTaskSummary(
    employeeId: string,
    year: number,
    month: number
  ): Promise<any | null> {
    try {
      await connectToDatabase();
      
      return await EmployeeTaskSummary.findOne({
        employeeId,
        year,
        month
      });
      
    } catch (error) {
      logger.error('Error getting task summary', LogCategory.PAYROLL, error);
      return null;
    }
  }
}

// Export singleton instance
export const taskPayrollIntegrationService = new TaskPayrollIntegrationService();

import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import Employee from '@/models/Employee';
import { 
  enhancedPayrollCalculationService,
  SalaryCalculationInput,
  SalaryResult 
} from './enhanced-payroll-calculation-service';
import { 
  attendancePayrollIntegrationService,
  AttendanceCalculationOptions 
} from './attendance-payroll-integration-service';
import { 
  taskPayrollIntegrationService,
  TaskCalculationOptions 
} from './task-payroll-integration-service';

/**
 * Interface for variable salary calculation options
 */
export interface VariableSalaryOptions {
  generateSummaries?: boolean;
  attendanceOptions?: AttendanceCalculationOptions;
  taskOptions?: TaskCalculationOptions;
  skipRules?: boolean;
  overrides?: {
    basicSalary?: number;
    hourlyRate?: number;
    bonusAmount?: number;
    deductionAmount?: number;
  };
}

/**
 * Interface for bulk salary calculation result
 */
export interface BulkSalaryResult {
  success: number;
  failed: number;
  results: SalaryResult[];
  errors: string[];
  totalGrossSalary: number;
  totalNetSalary: number;
}

/**
 * Variable Salary Service
 * Unified service for calculating variable salaries based on employee types,
 * attendance data, and task completion
 */
export class VariableSalaryService {
  
  /**
   * Calculate variable salary for a single employee
   * @param employeeId - Employee ID
   * @param payPeriod - Pay period (month and year)
   * @param userId - User ID performing the calculation
   * @param options - Calculation options
   * @returns Salary calculation result
   */
  async calculateEmployeeSalary(
    employeeId: string,
    payPeriod: { month: number; year: number },
    userId: string,
    options: VariableSalaryOptions = {}
  ): Promise<SalaryResult> {
    try {
      await connectToDatabase();
      
      // Validate employee exists
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        throw new Error(`Employee ${employeeId} not found`);
      }
      
      logger.info('Starting variable salary calculation', LogCategory.PAYROLL, {
        employeeId,
        payPeriod,
        employmentType: employee.employmentType,
        employmentSubType: employee.employmentSubType
      });
      
      // Generate attendance and task summaries if requested
      if (options.generateSummaries) {
        await this.generateMonthlySummaries(employeeId, payPeriod, userId, options);
      }
      
      // Get attendance data
      const attendanceData = await this.getAttendanceData(employeeId, payPeriod);
      
      // Get task data
      const taskData = await this.getTaskData(employeeId, payPeriod);
      
      // Prepare calculation input
      const calculationInput: SalaryCalculationInput = {
        employeeId,
        payPeriod,
        attendanceData,
        taskData,
        overrides: {
          ...options.overrides,
          skipRules: options.skipRules
        }
      };
      
      // Calculate salary using enhanced service
      const result = await enhancedPayrollCalculationService.calculateEmployeeSalary(calculationInput);
      
      logger.info('Variable salary calculation completed', LogCategory.PAYROLL, {
        employeeId,
        calculationType: result.calculationType,
        grossSalary: result.grossSalary,
        netSalary: result.netSalary,
        componentsCount: result.components.length
      });
      
      return result;
      
    } catch (error) {
      logger.error('Error in variable salary calculation', LogCategory.PAYROLL, error);
      throw error;
    }
  }
  
  /**
   * Calculate salaries for multiple employees
   * @param employeeIds - Array of employee IDs
   * @param payPeriod - Pay period
   * @param userId - User ID performing the calculation
   * @param options - Calculation options
   * @returns Bulk calculation results
   */
  async calculateBulkSalaries(
    employeeIds: string[],
    payPeriod: { month: number; year: number },
    userId: string,
    options: VariableSalaryOptions = {}
  ): Promise<BulkSalaryResult> {
    const results: SalaryResult[] = [];
    const errors: string[] = [];
    let success = 0;
    let failed = 0;
    let totalGrossSalary = 0;
    let totalNetSalary = 0;
    
    logger.info('Starting bulk salary calculation', LogCategory.PAYROLL, {
      employeeCount: employeeIds.length,
      payPeriod
    });
    
    // Generate summaries for all employees if requested
    if (options.generateSummaries) {
      await this.generateBulkSummaries(employeeIds, payPeriod, userId, options);
    }
    
    // Process each employee
    for (const employeeId of employeeIds) {
      try {
        const result = await this.calculateEmployeeSalary(employeeId, payPeriod, userId, {
          ...options,
          generateSummaries: false // Already generated above
        });
        
        results.push(result);
        
        if (result.error) {
          failed++;
          errors.push(`${result.employeeName}: ${result.message}`);
        } else {
          success++;
          totalGrossSalary += result.grossSalary;
          totalNetSalary += result.netSalary;
        }
        
      } catch (error) {
        failed++;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push(`Employee ${employeeId}: ${errorMessage}`);
        
        // Add error result
        results.push({
          employeeId,
          employeeName: 'Unknown',
          employmentType: 'unknown',
          employmentSubType: 'unknown',
          calculationType: 'unknown',
          basicSalary: 0,
          components: [],
          grossSalary: 0,
          totalDeductions: 0,
          totalTax: 0,
          netSalary: 0,
          currency: 'MWK',
          calculationDetails: {
            method: 'error',
            rulesApplied: []
          },
          error: 'CALCULATION_ERROR',
          message: errorMessage
        });
      }
    }
    
    logger.info('Bulk salary calculation completed', LogCategory.PAYROLL, {
      success,
      failed,
      totalGrossSalary,
      totalNetSalary
    });
    
    return {
      success,
      failed,
      results,
      errors,
      totalGrossSalary,
      totalNetSalary
    };
  }
  
  /**
   * Calculate salaries for all active employees
   * @param payPeriod - Pay period
   * @param userId - User ID performing the calculation
   * @param options - Calculation options
   * @returns Bulk calculation results
   */
  async calculateAllEmployeeSalaries(
    payPeriod: { month: number; year: number },
    userId: string,
    options: VariableSalaryOptions = {}
  ): Promise<BulkSalaryResult> {
    try {
      await connectToDatabase();
      
      // Get all active employees
      const employees = await Employee.find({
        employmentStatus: 'active'
      }).select('_id');
      
      const employeeIds = employees.map(emp => emp._id.toString());
      
      return await this.calculateBulkSalaries(employeeIds, payPeriod, userId, options);
      
    } catch (error) {
      logger.error('Error calculating all employee salaries', LogCategory.PAYROLL, error);
      throw error;
    }
  }
  
  /**
   * Preview salary calculation without saving
   * @param employeeId - Employee ID
   * @param payPeriod - Pay period
   * @param options - Calculation options
   * @returns Salary preview
   */
  async previewSalaryCalculation(
    employeeId: string,
    payPeriod: { month: number; year: number },
    options: VariableSalaryOptions = {}
  ): Promise<SalaryResult> {
    // Use a temporary user ID for preview
    const tempUserId = new mongoose.Types.ObjectId().toString();
    
    return await this.calculateEmployeeSalary(employeeId, payPeriod, tempUserId, {
      ...options,
      generateSummaries: false // Don't save summaries for preview
    });
  }
  
  /**
   * Generate monthly summaries for an employee
   */
  private async generateMonthlySummaries(
    employeeId: string,
    payPeriod: { month: number; year: number },
    userId: string,
    options: VariableSalaryOptions
  ): Promise<void> {
    try {
      // Generate attendance summary
      await attendancePayrollIntegrationService.generateMonthlySummary(
        employeeId,
        payPeriod.year,
        payPeriod.month,
        userId,
        options.attendanceOptions
      );
      
      // Generate task summary
      await taskPayrollIntegrationService.generateMonthlySummary(
        employeeId,
        payPeriod.year,
        payPeriod.month,
        userId,
        options.taskOptions
      );
      
    } catch (error) {
      logger.warn('Error generating monthly summaries', LogCategory.PAYROLL, {
        employeeId,
        payPeriod,
        error
      });
      // Don't throw error - continue with calculation using existing data
    }
  }
  
  /**
   * Generate summaries for multiple employees
   */
  private async generateBulkSummaries(
    employeeIds: string[],
    payPeriod: { month: number; year: number },
    userId: string,
    options: VariableSalaryOptions
  ): Promise<void> {
    try {
      // Generate attendance summaries for all employees
      await attendancePayrollIntegrationService.generateAllEmployeeSummaries(
        payPeriod.year,
        payPeriod.month,
        userId,
        options.attendanceOptions
      );
      
      // Generate task summaries for each employee
      for (const employeeId of employeeIds) {
        try {
          await taskPayrollIntegrationService.generateMonthlySummary(
            employeeId,
            payPeriod.year,
            payPeriod.month,
            userId,
            options.taskOptions
          );
        } catch (error) {
          logger.warn('Error generating task summary for employee', LogCategory.PAYROLL, {
            employeeId,
            error
          });
        }
      }
      
    } catch (error) {
      logger.warn('Error generating bulk summaries', LogCategory.PAYROLL, error);
    }
  }
  
  /**
   * Get attendance data for calculation
   */
  private async getAttendanceData(employeeId: string, payPeriod: { month: number; year: number }) {
    const summary = await attendancePayrollIntegrationService.getAttendanceSummary(
      employeeId,
      payPeriod.year,
      payPeriod.month
    );
    
    if (!summary) return undefined;
    
    return {
      totalHours: summary.totalHours,
      overtimeHours: summary.overtimeHours,
      daysPresent: summary.daysPresent,
      daysAbsent: summary.daysAbsent,
      lateCount: summary.daysLate,
      attendanceRate: summary.attendanceRate,
      punctualityRate: summary.punctualityRate,
      averageHoursPerDay: summary.averageHoursPerDay
    };
  }
  
  /**
   * Get task data for calculation
   */
  private async getTaskData(employeeId: string, payPeriod: { month: number; year: number }) {
    const summary = await taskPayrollIntegrationService.getTaskSummary(
      employeeId,
      payPeriod.year,
      payPeriod.month
    );
    
    if (!summary) return undefined;
    
    return {
      tasksCompleted: summary.totalTasksCompleted,
      totalTaskHours: summary.totalActualHours,
      projectsCompleted: 0, // Calculate from task data if needed
      performanceScore: summary.averageQualityScore,
      completionRate: summary.completionRate,
      onTimeCompletionRate: summary.onTimeCompletionRate,
      averageQualityScore: summary.averageQualityScore,
      efficiencyRatio: summary.efficiencyRatio
    };
  }
}

// Export singleton instance
export const variableSalaryService = new VariableSalaryService();

import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import Employee from '@/models/Employee';
import Attendance from '@/models/attendance/Attendance';
import EmployeeAttendanceSummary from '@/models/payroll/EmployeeAttendanceSummary';
import { startOfMonth, endOfMonth, getDaysInMonth, isWeekend } from 'date-fns';

/**
 * Interface for attendance calculation options
 */
export interface AttendanceCalculationOptions {
  includeWeekends?: boolean;
  includeHolidays?: boolean;
  workingHoursPerDay?: number;
  overtimeThreshold?: number;
  lateThresholdMinutes?: number;
}

/**
 * Interface for attendance summary generation result
 */
export interface AttendanceSummaryResult {
  success: boolean;
  summary?: any;
  error?: string;
  message?: string;
}

/**
 * Attendance Payroll Integration Service
 * Handles integration between attendance tracking and payroll calculation
 */
export class AttendancePayrollIntegrationService {
  
  /**
   * Generate monthly attendance summary for an employee
   * @param employeeId - Employee ID
   * @param year - Year
   * @param month - Month (1-12)
   * @param userId - User ID generating the summary
   * @param options - Calculation options
   * @returns Generated attendance summary
   */
  async generateMonthlySummary(
    employeeId: string,
    year: number,
    month: number,
    userId: string,
    options: AttendanceCalculationOptions = {}
  ): Promise<AttendanceSummaryResult> {
    try {
      await connectToDatabase();
      
      // Validate employee exists
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        return {
          success: false,
          error: 'EMPLOYEE_NOT_FOUND',
          message: `Employee ${employeeId} not found`
        };
      }
      
      // Check if summary already exists
      const existingSummary = await EmployeeAttendanceSummary.findOne({
        employeeId,
        year,
        month
      });
      
      if (existingSummary && existingSummary.isFinalized) {
        return {
          success: false,
          error: 'SUMMARY_ALREADY_FINALIZED',
          message: 'Attendance summary for this period is already finalized'
        };
      }
      
      // Get attendance records for the month
      const startDate = startOfMonth(new Date(year, month - 1));
      const endDate = endOfMonth(new Date(year, month - 1));
      
      const attendanceRecords = await Attendance.find({
        employeeId,
        date: {
          $gte: startDate,
          $lte: endDate
        }
      }).sort({ date: 1 });
      
      // Calculate attendance statistics
      const stats = await this.calculateAttendanceStatistics(
        attendanceRecords,
        year,
        month,
        options
      );
      
      // Calculate financial implications
      const financials = await this.calculateAttendanceFinancials(
        employee,
        stats,
        options
      );
      
      // Create or update summary
      const summaryData = {
        employeeId,
        year,
        month,
        ...stats,
        ...financials,
        calculatedAt: new Date(),
        calculatedBy: new mongoose.Types.ObjectId(userId),
        isFinalized: false
      };
      
      let summary;
      if (existingSummary) {
        summary = await EmployeeAttendanceSummary.findByIdAndUpdate(
          existingSummary._id,
          summaryData,
          { new: true }
        );
      } else {
        summary = new EmployeeAttendanceSummary(summaryData);
        await summary.save();
      }
      
      logger.info('Monthly attendance summary generated', LogCategory.PAYROLL, {
        employeeId,
        year,
        month,
        attendanceRate: stats.attendanceRate,
        totalHours: stats.totalHours
      });
      
      return {
        success: true,
        summary,
        message: 'Attendance summary generated successfully'
      };
      
    } catch (error) {
      logger.error('Error generating monthly attendance summary', LogCategory.PAYROLL, error);
      return {
        success: false,
        error: 'GENERATION_ERROR',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Calculate attendance statistics from records
   */
  private async calculateAttendanceStatistics(
    records: any[],
    year: number,
    month: number,
    options: AttendanceCalculationOptions
  ) {
    const workingHoursPerDay = options.workingHoursPerDay || 8;
    const overtimeThreshold = options.overtimeThreshold || 8;
    const lateThresholdMinutes = options.lateThresholdMinutes || 15;
    
    // Calculate total working days in month
    const daysInMonth = getDaysInMonth(new Date(year, month - 1));
    let totalWorkingDays = 0;
    
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month - 1, day);
      if (!options.includeWeekends && isWeekend(date)) {
        continue;
      }
      totalWorkingDays++;
    }
    
    // Initialize counters
    let daysPresent = 0;
    let daysAbsent = 0;
    let daysLate = 0;
    let daysHalfDay = 0;
    let daysOnLeave = 0;
    let totalHours = 0;
    let regularHours = 0;
    let overtimeHours = 0;
    let lateHours = 0;
    
    // Process each record
    for (const record of records) {
      switch (record.status) {
        case 'present':
          daysPresent++;
          if (record.workHours) {
            totalHours += record.workHours;
            if (record.workHours > overtimeThreshold) {
              regularHours += overtimeThreshold;
              overtimeHours += record.workHours - overtimeThreshold;
            } else {
              regularHours += record.workHours;
            }
          }
          break;
          
        case 'late':
          daysPresent++;
          daysLate++;
          if (record.workHours) {
            totalHours += record.workHours;
            regularHours += Math.min(record.workHours, overtimeThreshold);
            if (record.workHours > overtimeThreshold) {
              overtimeHours += record.workHours - overtimeThreshold;
            }
          }
          // Calculate late hours (assuming late arrival reduces work time)
          if (record.checkIn && record.checkOut) {
            const scheduledStart = new Date(record.date);
            scheduledStart.setHours(8, 0, 0, 0); // Assuming 8 AM start
            const actualStart = new Date(record.checkIn);
            const lateMinutes = Math.max(0, (actualStart.getTime() - scheduledStart.getTime()) / (1000 * 60));
            if (lateMinutes > lateThresholdMinutes) {
              lateHours += lateMinutes / 60;
            }
          }
          break;
          
        case 'half-day':
          daysPresent++;
          daysHalfDay++;
          if (record.workHours) {
            totalHours += record.workHours;
            regularHours += record.workHours;
          } else {
            // Assume half day is 4 hours
            totalHours += 4;
            regularHours += 4;
          }
          break;
          
        case 'absent':
          daysAbsent++;
          break;
          
        case 'leave':
          daysOnLeave++;
          break;
      }
    }
    
    // Calculate rates
    const attendanceRate = totalWorkingDays > 0 ? (daysPresent / totalWorkingDays) * 100 : 0;
    const punctualityRate = daysPresent > 0 ? ((daysPresent - daysLate) / daysPresent) * 100 : 0;
    const averageHoursPerDay = daysPresent > 0 ? totalHours / daysPresent : 0;
    
    return {
      totalWorkingDays,
      daysPresent,
      daysAbsent,
      daysLate,
      daysHalfDay,
      daysOnLeave,
      totalHours,
      regularHours,
      overtimeHours,
      lateHours,
      attendanceRate: Math.round(attendanceRate * 100) / 100,
      punctualityRate: Math.round(punctualityRate * 100) / 100,
      averageHoursPerDay: Math.round(averageHoursPerDay * 100) / 100
    };
  }
  
  /**
   * Calculate financial implications of attendance
   */
  private async calculateAttendanceFinancials(
    employee: any,
    stats: any,
    options: AttendanceCalculationOptions
  ) {
    let lateDeductions = 0;
    let absenceDeductions = 0;
    let overtimeBonus = 0;
    let perfectAttendanceBonus = 0;
    
    // Get employee's salary calculation rules
    const rules = employee.salaryCalculationRules || {};
    
    // Calculate late deductions
    if (stats.daysLate > 0 && rules.lateDeductionRate) {
      // Assuming daily rate calculation
      const dailyRate = employee.hourlyRate ? employee.hourlyRate * 8 : 1000; // Default daily rate
      lateDeductions = stats.daysLate * dailyRate * rules.lateDeductionRate;
    }
    
    // Calculate absence deductions
    if (stats.daysAbsent > 0 && rules.absenceDeductionRate) {
      const dailyRate = employee.hourlyRate ? employee.hourlyRate * 8 : 1000;
      absenceDeductions = stats.daysAbsent * dailyRate * rules.absenceDeductionRate;
    }
    
    // Calculate overtime bonus
    if (stats.overtimeHours > 0 && employee.hourlyRate) {
      const overtimeMultiplier = rules.overtimeMultiplier || 1.5;
      overtimeBonus = stats.overtimeHours * employee.hourlyRate * (overtimeMultiplier - 1);
    }
    
    // Calculate perfect attendance bonus
    if (stats.attendanceRate === 100 && stats.punctualityRate === 100) {
      perfectAttendanceBonus = 5000; // Fixed bonus amount
    }
    
    return {
      lateDeductions: Math.round(lateDeductions),
      absenceDeductions: Math.round(absenceDeductions),
      overtimeBonus: Math.round(overtimeBonus),
      perfectAttendanceBonus: Math.round(perfectAttendanceBonus)
    };
  }
  
  /**
   * Finalize attendance summary for payroll processing
   * @param employeeId - Employee ID
   * @param year - Year
   * @param month - Month
   * @param userId - User ID finalizing the summary
   * @returns Finalization result
   */
  async finalizeMonthlySummary(
    employeeId: string,
    year: number,
    month: number,
    userId: string
  ): Promise<AttendanceSummaryResult> {
    try {
      await connectToDatabase();
      
      const summary = await EmployeeAttendanceSummary.findOne({
        employeeId,
        year,
        month
      });
      
      if (!summary) {
        return {
          success: false,
          error: 'SUMMARY_NOT_FOUND',
          message: 'Attendance summary not found'
        };
      }
      
      if (summary.isFinalized) {
        return {
          success: false,
          error: 'ALREADY_FINALIZED',
          message: 'Summary is already finalized'
        };
      }
      
      summary.isFinalized = true;
      summary.finalizedAt = new Date();
      summary.finalizedBy = new mongoose.Types.ObjectId(userId);
      
      await summary.save();
      
      logger.info('Attendance summary finalized', LogCategory.PAYROLL, {
        employeeId,
        year,
        month,
        userId
      });
      
      return {
        success: true,
        summary,
        message: 'Attendance summary finalized successfully'
      };
      
    } catch (error) {
      logger.error('Error finalizing attendance summary', LogCategory.PAYROLL, error);
      return {
        success: false,
        error: 'FINALIZATION_ERROR',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Get attendance summary for payroll calculation
   * @param employeeId - Employee ID
   * @param year - Year
   * @param month - Month
   * @returns Attendance summary or null
   */
  async getAttendanceSummary(
    employeeId: string,
    year: number,
    month: number
  ): Promise<any | null> {
    try {
      await connectToDatabase();
      
      return await EmployeeAttendanceSummary.findOne({
        employeeId,
        year,
        month
      });
      
    } catch (error) {
      logger.error('Error getting attendance summary', LogCategory.PAYROLL, error);
      return null;
    }
  }
  
  /**
   * Generate attendance summaries for all employees for a given month
   * @param year - Year
   * @param month - Month
   * @param userId - User ID generating summaries
   * @param options - Calculation options
   * @returns Generation results
   */
  async generateAllEmployeeSummaries(
    year: number,
    month: number,
    userId: string,
    options: AttendanceCalculationOptions = {}
  ): Promise<{
    success: number;
    failed: number;
    errors: string[];
  }> {
    try {
      await connectToDatabase();
      
      // Get all active employees
      const employees = await Employee.find({
        employmentStatus: 'active'
      }).select('_id firstName lastName');
      
      let success = 0;
      let failed = 0;
      const errors: string[] = [];
      
      for (const employee of employees) {
        try {
          const result = await this.generateMonthlySummary(
            employee._id.toString(),
            year,
            month,
            userId,
            options
          );
          
          if (result.success) {
            success++;
          } else {
            failed++;
            errors.push(`${employee.firstName} ${employee.lastName}: ${result.message}`);
          }
        } catch (error) {
          failed++;
          errors.push(`${employee.firstName} ${employee.lastName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
      
      logger.info('Bulk attendance summary generation completed', LogCategory.PAYROLL, {
        year,
        month,
        success,
        failed,
        totalEmployees: employees.length
      });
      
      return { success, failed, errors };
      
    } catch (error) {
      logger.error('Error generating bulk attendance summaries', LogCategory.PAYROLL, error);
      throw error;
    }
  }
}

// Export singleton instance
export const attendancePayrollIntegrationService = new AttendancePayrollIntegrationService();

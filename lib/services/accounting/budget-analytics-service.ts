// lib/services/accounting/budget-analytics-service.ts
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import Budget from '@/models/accounting/Budget';
import BudgetCategory from '@/models/accounting/BudgetCategory';
import Income from '@/models/accounting/Income';
import Expense from '@/models/accounting/Expense';

export interface BudgetPerformanceMetrics {
  budgetId: string;
  budgetName: string;
  fiscalYear: string;
  totalBudgeted: number;
  totalActual: number;
  variance: number;
  variancePercentage: number;
  incomeMetrics: {
    budgeted: number;
    actual: number;
    variance: number;
    variancePercentage: number;
  };
  expenseMetrics: {
    budgeted: number;
    actual: number;
    variance: number;
    variancePercentage: number;
  };
  categoryBreakdown: Array<{
    categoryId: string;
    categoryName: string;
    type: 'income' | 'expense';
    budgeted: number;
    actual: number;
    variance: number;
    variancePercentage: number;
  }>;
}

export interface TrendAnalysis {
  period: string;
  budgeted: number;
  actual: number;
  variance: number;
  cumulativeBudgeted: number;
  cumulativeActual: number;
  cumulativeVariance: number;
}

export interface MultiYearComparison {
  fiscalYear: string;
  totalBudgeted: number;
  totalActual: number;
  variance: number;
  variancePercentage: number;
  growthRate: number;
}

export interface BudgetKPIs {
  budgetUtilization: number; // Actual / Budgeted
  budgetAccuracy: number; // 100 - |variance%|
  incomeRealization: number; // Actual Income / Budgeted Income
  expenseControl: number; // 100 - (Actual Expense / Budgeted Expense)
  netPosition: number; // Actual Income - Actual Expense
  budgetedNetPosition: number; // Budgeted Income - Budgeted Expense
}

/**
 * Advanced Budget Analytics Service
 * Provides comprehensive analytics and performance metrics for budget planning
 */
export class BudgetAnalyticsService {
  
  /**
   * Get comprehensive performance metrics for a budget
   */
  async getBudgetPerformanceMetrics(budgetId: string): Promise<BudgetPerformanceMetrics> {
    try {
      await connectToDatabase();
      
      logger.info('Getting budget performance metrics', LogCategory.ACCOUNTING, { budgetId });

      // Get budget details
      const budget = await Budget.findById(budgetId).lean();
      if (!budget) {
        throw new Error('Budget not found');
      }

      // Get budget categories
      const categories = await BudgetCategory.find({ budget: budgetId }).lean();

      // Calculate category-level metrics
      const categoryBreakdown = await Promise.all(
        categories.map(async (category) => {
          const actualAmount = await this.getCategoryActualAmount(category._id.toString(), category.type);
          const variance = actualAmount - (category.budgetedAmount || 0);
          const variancePercentage = category.budgetedAmount ? (variance / category.budgetedAmount) * 100 : 0;

          return {
            categoryId: category._id.toString(),
            categoryName: category.name,
            type: category.type,
            budgeted: category.budgetedAmount || 0,
            actual: actualAmount,
            variance,
            variancePercentage
          };
        })
      );

      // Calculate overall metrics
      const totalBudgetedIncome = categoryBreakdown
        .filter(c => c.type === 'income')
        .reduce((sum, c) => sum + c.budgeted, 0);
      
      const totalActualIncome = categoryBreakdown
        .filter(c => c.type === 'income')
        .reduce((sum, c) => sum + c.actual, 0);

      const totalBudgetedExpense = categoryBreakdown
        .filter(c => c.type === 'expense')
        .reduce((sum, c) => sum + c.budgeted, 0);
      
      const totalActualExpense = categoryBreakdown
        .filter(c => c.type === 'expense')
        .reduce((sum, c) => sum + c.actual, 0);

      const totalBudgeted = totalBudgetedIncome + totalBudgetedExpense;
      const totalActual = totalActualIncome + totalActualExpense;
      const variance = totalActual - totalBudgeted;
      const variancePercentage = totalBudgeted ? (variance / totalBudgeted) * 100 : 0;

      const incomeVariance = totalActualIncome - totalBudgetedIncome;
      const expenseVariance = totalActualExpense - totalBudgetedExpense;

      return {
        budgetId,
        budgetName: budget.name,
        fiscalYear: budget.fiscalYear,
        totalBudgeted,
        totalActual,
        variance,
        variancePercentage,
        incomeMetrics: {
          budgeted: totalBudgetedIncome,
          actual: totalActualIncome,
          variance: incomeVariance,
          variancePercentage: totalBudgetedIncome ? (incomeVariance / totalBudgetedIncome) * 100 : 0
        },
        expenseMetrics: {
          budgeted: totalBudgetedExpense,
          actual: totalActualExpense,
          variance: expenseVariance,
          variancePercentage: totalBudgetedExpense ? (expenseVariance / totalBudgetedExpense) * 100 : 0
        },
        categoryBreakdown
      };

    } catch (error) {
      logger.error('Error getting budget performance metrics', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get trend analysis for a budget over time
   */
  async getBudgetTrendAnalysis(
    budgetId: string, 
    periodType: 'monthly' | 'quarterly' = 'monthly'
  ): Promise<TrendAnalysis[]> {
    try {
      await connectToDatabase();
      
      logger.info('Getting budget trend analysis', LogCategory.ACCOUNTING, { budgetId, periodType });

      const budget = await Budget.findById(budgetId).lean();
      if (!budget) {
        throw new Error('Budget not found');
      }

      const startDate = new Date(budget.startDate);
      const endDate = new Date(budget.endDate);
      const trends: TrendAnalysis[] = [];

      // Generate periods based on type
      const periods = this.generatePeriods(startDate, endDate, periodType);
      
      let cumulativeBudgeted = 0;
      let cumulativeActual = 0;

      for (const period of periods) {
        const periodBudgeted = await this.getPeriodBudgetedAmount(budgetId, period.start, period.end);
        const periodActual = await this.getPeriodActualAmount(budgetId, period.start, period.end);
        
        cumulativeBudgeted += periodBudgeted;
        cumulativeActual += periodActual;
        
        const variance = periodActual - periodBudgeted;
        const cumulativeVariance = cumulativeActual - cumulativeBudgeted;

        trends.push({
          period: period.label,
          budgeted: periodBudgeted,
          actual: periodActual,
          variance,
          cumulativeBudgeted,
          cumulativeActual,
          cumulativeVariance
        });
      }

      return trends;

    } catch (error) {
      logger.error('Error getting budget trend analysis', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get multi-year budget comparison
   */
  async getMultiYearComparison(fiscalYears: string[]): Promise<MultiYearComparison[]> {
    try {
      await connectToDatabase();
      
      logger.info('Getting multi-year budget comparison', LogCategory.ACCOUNTING, { fiscalYears });

      const comparisons: MultiYearComparison[] = [];
      let previousYearBudgeted = 0;

      for (const fiscalYear of fiscalYears) {
        const budgets = await Budget.find({ fiscalYear }).lean();
        
        const totalBudgeted = budgets.reduce((sum, b) => sum + (b.totalIncome || 0) + (b.totalExpense || 0), 0);
        const totalActual = budgets.reduce((sum, b) => sum + (b.totalActualIncome || 0) + (b.totalActualExpense || 0), 0);
        const variance = totalActual - totalBudgeted;
        const variancePercentage = totalBudgeted ? (variance / totalBudgeted) * 100 : 0;
        const growthRate = previousYearBudgeted ? ((totalBudgeted - previousYearBudgeted) / previousYearBudgeted) * 100 : 0;

        comparisons.push({
          fiscalYear,
          totalBudgeted,
          totalActual,
          variance,
          variancePercentage,
          growthRate
        });

        previousYearBudgeted = totalBudgeted;
      }

      return comparisons;

    } catch (error) {
      logger.error('Error getting multi-year comparison', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Calculate budget KPIs
   */
  async getBudgetKPIs(budgetId: string): Promise<BudgetKPIs> {
    try {
      const metrics = await this.getBudgetPerformanceMetrics(budgetId);
      
      const budgetUtilization = metrics.totalBudgeted ? (metrics.totalActual / metrics.totalBudgeted) * 100 : 0;
      const budgetAccuracy = 100 - Math.abs(metrics.variancePercentage);
      const incomeRealization = metrics.incomeMetrics.budgeted ? 
        (metrics.incomeMetrics.actual / metrics.incomeMetrics.budgeted) * 100 : 0;
      const expenseControl = metrics.expenseMetrics.budgeted ? 
        100 - ((metrics.expenseMetrics.actual / metrics.expenseMetrics.budgeted) * 100) : 0;
      const netPosition = metrics.incomeMetrics.actual - metrics.expenseMetrics.actual;
      const budgetedNetPosition = metrics.incomeMetrics.budgeted - metrics.expenseMetrics.budgeted;

      return {
        budgetUtilization,
        budgetAccuracy,
        incomeRealization,
        expenseControl,
        netPosition,
        budgetedNetPosition
      };

    } catch (error) {
      logger.error('Error calculating budget KPIs', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Helper method to get actual amount for a category
   */
  private async getCategoryActualAmount(categoryId: string, type: 'income' | 'expense'): Promise<number> {
    try {
      if (type === 'income') {
        const incomes = await Income.find({ 
          budgetCategory: categoryId,
          status: { $in: ['received', 'approved'] }
        }).lean();
        return incomes.reduce((sum, income) => sum + (income.amount || 0), 0);
      } else {
        const expenses = await Expense.find({ 
          budgetCategory: categoryId,
          status: 'paid'
        }).lean();
        return expenses.reduce((sum, expense) => sum + (expense.amount || 0), 0);
      }
    } catch (error) {
      logger.error('Error getting category actual amount', LogCategory.ACCOUNTING, error);
      return 0;
    }
  }

  /**
   * Helper method to generate time periods
   */
  private generatePeriods(
    startDate: Date, 
    endDate: Date, 
    periodType: 'monthly' | 'quarterly'
  ): Array<{ start: Date; end: Date; label: string }> {
    const periods = [];
    const current = new Date(startDate);
    
    while (current < endDate) {
      const periodStart = new Date(current);
      const periodEnd = new Date(current);
      
      if (periodType === 'monthly') {
        periodEnd.setMonth(periodEnd.getMonth() + 1);
        periodEnd.setDate(0); // Last day of month
      } else {
        periodEnd.setMonth(periodEnd.getMonth() + 3);
        periodEnd.setDate(0); // Last day of quarter
      }
      
      if (periodEnd > endDate) {
        periodEnd.setTime(endDate.getTime());
      }
      
      const label = periodType === 'monthly' 
        ? `${periodStart.getFullYear()}-${String(periodStart.getMonth() + 1).padStart(2, '0')}`
        : `Q${Math.ceil((periodStart.getMonth() + 1) / 3)} ${periodStart.getFullYear()}`;
      
      periods.push({
        start: periodStart,
        end: periodEnd,
        label
      });
      
      current.setTime(periodEnd.getTime() + 1);
    }
    
    return periods;
  }

  /**
   * Helper method to get budgeted amount for a period
   */
  private async getPeriodBudgetedAmount(budgetId: string, startDate: Date, endDate: Date): Promise<number> {
    // For now, return proportional amount based on budget duration
    // This can be enhanced with more sophisticated period-based budgeting
    const budget = await Budget.findById(budgetId).lean();
    if (!budget) return 0;
    
    const totalBudgeted = (budget.totalIncome || 0) + (budget.totalExpense || 0);
    const budgetDuration = new Date(budget.endDate).getTime() - new Date(budget.startDate).getTime();
    const periodDuration = endDate.getTime() - startDate.getTime();
    
    return (totalBudgeted * periodDuration) / budgetDuration;
  }

  /**
   * Helper method to get actual amount for a period
   */
  private async getPeriodActualAmount(budgetId: string, startDate: Date, endDate: Date): Promise<number> {
    try {
      const [incomes, expenses] = await Promise.all([
        Income.find({
          budget: budgetId,
          date: { $gte: startDate, $lte: endDate },
          status: { $in: ['received', 'approved'] }
        }).lean(),
        Expense.find({
          budget: budgetId,
          date: { $gte: startDate, $lte: endDate },
          status: 'paid'
        }).lean()
      ]);

      const totalIncome = incomes.reduce((sum, income) => sum + (income.amount || 0), 0);
      const totalExpense = expenses.reduce((sum, expense) => sum + (expense.amount || 0), 0);
      
      return totalIncome + totalExpense;
    } catch (error) {
      logger.error('Error getting period actual amount', LogCategory.ACCOUNTING, error);
      return 0;
    }
  }
}

// Export singleton instance
export const budgetAnalyticsService = new BudgetAnalyticsService();

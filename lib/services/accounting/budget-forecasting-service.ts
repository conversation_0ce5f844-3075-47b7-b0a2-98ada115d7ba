// lib/services/accounting/budget-forecasting-service.ts
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import Budget from '@/models/accounting/Budget';
import BudgetCategory from '@/models/accounting/BudgetCategory';
import Income from '@/models/accounting/Income';
import Expense from '@/models/accounting/Expense';

export interface ForecastPeriod {
  period: string;
  startDate: Date;
  endDate: Date;
  forecastedIncome: number;
  forecastedExpense: number;
  forecastedNet: number;
  confidence: number; // 0-100
  factors: string[];
}

export interface BudgetForecast {
  budgetId: string;
  budgetName: string;
  forecastType: 'linear' | 'seasonal' | 'growth' | 'ai';
  forecastPeriods: ForecastPeriod[];
  totalForecastedIncome: number;
  totalForecastedExpense: number;
  totalForecastedNet: number;
  averageConfidence: number;
  riskFactors: string[];
  recommendations: string[];
}

export interface SeasonalPattern {
  month: number;
  multiplier: number;
  variance: number;
}

export interface GrowthTrend {
  incomeGrowthRate: number;
  expenseGrowthRate: number;
  volatility: number;
  trendStrength: number;
}

/**
 * Advanced Budget Forecasting Service
 * Provides AI-powered budget predictions and scenario analysis
 */
export class BudgetForecastingService {

  /**
   * Generate comprehensive budget forecast
   */
  async generateBudgetForecast(
    budgetId: string,
    forecastMonths: number = 12,
    forecastType: 'linear' | 'seasonal' | 'growth' | 'ai' = 'ai'
  ): Promise<BudgetForecast> {
    try {
      await connectToDatabase();
      
      logger.info('Generating budget forecast', LogCategory.ACCOUNTING, {
        budgetId,
        forecastMonths,
        forecastType
      });

      // Get budget and historical data
      const budget = await Budget.findById(budgetId).lean();
      if (!budget) {
        throw new Error('Budget not found');
      }

      const historicalData = await this.getHistoricalData(budgetId);
      
      let forecastPeriods: ForecastPeriod[] = [];
      let riskFactors: string[] = [];
      let recommendations: string[] = [];

      switch (forecastType) {
        case 'linear':
          forecastPeriods = await this.generateLinearForecast(historicalData, forecastMonths);
          break;
        case 'seasonal':
          forecastPeriods = await this.generateSeasonalForecast(historicalData, forecastMonths);
          break;
        case 'growth':
          forecastPeriods = await this.generateGrowthForecast(historicalData, forecastMonths);
          break;
        case 'ai':
          forecastPeriods = await this.generateAIForecast(historicalData, forecastMonths);
          break;
      }

      // Calculate totals
      const totalForecastedIncome = forecastPeriods.reduce((sum, p) => sum + p.forecastedIncome, 0);
      const totalForecastedExpense = forecastPeriods.reduce((sum, p) => sum + p.forecastedExpense, 0);
      const totalForecastedNet = totalForecastedIncome - totalForecastedExpense;
      const averageConfidence = forecastPeriods.reduce((sum, p) => sum + p.confidence, 0) / forecastPeriods.length;

      // Generate risk factors and recommendations
      riskFactors = this.identifyRiskFactors(forecastPeriods, historicalData);
      recommendations = this.generateRecommendations(forecastPeriods, historicalData);

      return {
        budgetId,
        budgetName: budget.name,
        forecastType,
        forecastPeriods,
        totalForecastedIncome,
        totalForecastedExpense,
        totalForecastedNet,
        averageConfidence,
        riskFactors,
        recommendations
      };

    } catch (error) {
      logger.error('Error generating budget forecast', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get historical data for forecasting
   */
  private async getHistoricalData(budgetId: string) {
    const [incomes, expenses] = await Promise.all([
      Income.find({ budget: budgetId }).sort({ date: 1 }).lean(),
      Expense.find({ budget: budgetId }).sort({ date: 1 }).lean()
    ]);

    // Group by month
    const monthlyData = new Map();
    
    [...incomes, ...expenses].forEach(transaction => {
      const date = new Date(transaction.date);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      
      if (!monthlyData.has(monthKey)) {
        monthlyData.set(monthKey, {
          period: monthKey,
          income: 0,
          expense: 0,
          transactions: 0
        });
      }
      
      const monthData = monthlyData.get(monthKey);
      if ('amount' in transaction) {
        if (incomes.includes(transaction as any)) {
          monthData.income += transaction.amount;
        } else {
          monthData.expense += transaction.amount;
        }
      }
      monthData.transactions++;
    });

    return Array.from(monthlyData.values()).sort((a, b) => a.period.localeCompare(b.period));
  }

  /**
   * Generate linear forecast based on historical trends
   */
  private async generateLinearForecast(historicalData: any[], forecastMonths: number): Promise<ForecastPeriod[]> {
    if (historicalData.length < 2) {
      throw new Error('Insufficient historical data for linear forecast');
    }

    // Calculate linear trends
    const incomeValues = historicalData.map(d => d.income);
    const expenseValues = historicalData.map(d => d.expense);
    
    const incomeSlope = this.calculateLinearSlope(incomeValues);
    const expenseSlope = this.calculateLinearSlope(expenseValues);
    
    const lastIncome = incomeValues[incomeValues.length - 1];
    const lastExpense = expenseValues[expenseValues.length - 1];

    const forecastPeriods: ForecastPeriod[] = [];
    const currentDate = new Date();

    for (let i = 1; i <= forecastMonths; i++) {
      const periodStart = new Date(currentDate.getFullYear(), currentDate.getMonth() + i, 1);
      const periodEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + i + 1, 0);
      
      const forecastedIncome = Math.max(0, lastIncome + (incomeSlope * i));
      const forecastedExpense = Math.max(0, lastExpense + (expenseSlope * i));
      
      forecastPeriods.push({
        period: `${periodStart.getFullYear()}-${String(periodStart.getMonth() + 1).padStart(2, '0')}`,
        startDate: periodStart,
        endDate: periodEnd,
        forecastedIncome,
        forecastedExpense,
        forecastedNet: forecastedIncome - forecastedExpense,
        confidence: Math.max(60, 90 - (i * 2)), // Decreasing confidence over time
        factors: ['Linear trend analysis', 'Historical performance']
      });
    }

    return forecastPeriods;
  }

  /**
   * Generate seasonal forecast considering seasonal patterns
   */
  private async generateSeasonalForecast(historicalData: any[], forecastMonths: number): Promise<ForecastPeriod[]> {
    // Detect seasonal patterns
    const seasonalPatterns = this.detectSeasonalPatterns(historicalData);
    
    // Calculate base values
    const avgIncome = historicalData.reduce((sum, d) => sum + d.income, 0) / historicalData.length;
    const avgExpense = historicalData.reduce((sum, d) => sum + d.expense, 0) / historicalData.length;

    const forecastPeriods: ForecastPeriod[] = [];
    const currentDate = new Date();

    for (let i = 1; i <= forecastMonths; i++) {
      const periodStart = new Date(currentDate.getFullYear(), currentDate.getMonth() + i, 1);
      const periodEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + i + 1, 0);
      const month = periodStart.getMonth() + 1;
      
      const seasonalMultiplier = seasonalPatterns.find(p => p.month === month)?.multiplier || 1;
      
      const forecastedIncome = avgIncome * seasonalMultiplier;
      const forecastedExpense = avgExpense * seasonalMultiplier;
      
      forecastPeriods.push({
        period: `${periodStart.getFullYear()}-${String(month).padStart(2, '0')}`,
        startDate: periodStart,
        endDate: periodEnd,
        forecastedIncome,
        forecastedExpense,
        forecastedNet: forecastedIncome - forecastedExpense,
        confidence: Math.max(70, 95 - (i * 1.5)),
        factors: ['Seasonal patterns', 'Historical averages', `${seasonalMultiplier.toFixed(2)}x seasonal multiplier`]
      });
    }

    return forecastPeriods;
  }

  /**
   * Generate growth-based forecast
   */
  private async generateGrowthForecast(historicalData: any[], forecastMonths: number): Promise<ForecastPeriod[]> {
    const growthTrend = this.calculateGrowthTrend(historicalData);
    
    const lastData = historicalData[historicalData.length - 1];
    const forecastPeriods: ForecastPeriod[] = [];
    const currentDate = new Date();

    for (let i = 1; i <= forecastMonths; i++) {
      const periodStart = new Date(currentDate.getFullYear(), currentDate.getMonth() + i, 1);
      const periodEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + i + 1, 0);
      
      const incomeGrowthFactor = Math.pow(1 + growthTrend.incomeGrowthRate / 100, i);
      const expenseGrowthFactor = Math.pow(1 + growthTrend.expenseGrowthRate / 100, i);
      
      const forecastedIncome = lastData.income * incomeGrowthFactor;
      const forecastedExpense = lastData.expense * expenseGrowthFactor;
      
      forecastPeriods.push({
        period: `${periodStart.getFullYear()}-${String(periodStart.getMonth() + 1).padStart(2, '0')}`,
        startDate: periodStart,
        endDate: periodEnd,
        forecastedIncome,
        forecastedExpense,
        forecastedNet: forecastedIncome - forecastedExpense,
        confidence: Math.max(65, 85 - (i * 1.8)),
        factors: [
          'Growth trend analysis',
          `${growthTrend.incomeGrowthRate.toFixed(1)}% income growth`,
          `${growthTrend.expenseGrowthRate.toFixed(1)}% expense growth`
        ]
      });
    }

    return forecastPeriods;
  }

  /**
   * Generate AI-powered forecast (simplified ML approach)
   */
  private async generateAIForecast(historicalData: any[], forecastMonths: number): Promise<ForecastPeriod[]> {
    // Combine multiple forecasting methods for AI approach
    const [linearForecast, seasonalForecast, growthForecast] = await Promise.all([
      this.generateLinearForecast(historicalData, forecastMonths),
      this.generateSeasonalForecast(historicalData, forecastMonths),
      this.generateGrowthForecast(historicalData, forecastMonths)
    ]);

    // Weighted ensemble of forecasts
    const forecastPeriods: ForecastPeriod[] = [];
    
    for (let i = 0; i < forecastMonths; i++) {
      const linear = linearForecast[i];
      const seasonal = seasonalForecast[i];
      const growth = growthForecast[i];
      
      // Weighted average (can be improved with ML models)
      const weights = { linear: 0.3, seasonal: 0.4, growth: 0.3 };
      
      const forecastedIncome = 
        (linear.forecastedIncome * weights.linear) +
        (seasonal.forecastedIncome * weights.seasonal) +
        (growth.forecastedIncome * weights.growth);
        
      const forecastedExpense = 
        (linear.forecastedExpense * weights.linear) +
        (seasonal.forecastedExpense * weights.seasonal) +
        (growth.forecastedExpense * weights.growth);
      
      const confidence = 
        (linear.confidence * weights.linear) +
        (seasonal.confidence * weights.seasonal) +
        (growth.confidence * weights.growth);

      forecastPeriods.push({
        period: linear.period,
        startDate: linear.startDate,
        endDate: linear.endDate,
        forecastedIncome,
        forecastedExpense,
        forecastedNet: forecastedIncome - forecastedExpense,
        confidence,
        factors: ['AI ensemble model', 'Multiple forecasting methods', 'Weighted predictions']
      });
    }

    return forecastPeriods;
  }

  /**
   * Helper methods for calculations
   */
  private calculateLinearSlope(values: number[]): number {
    const n = values.length;
    const xSum = (n * (n + 1)) / 2;
    const ySum = values.reduce((sum, val) => sum + val, 0);
    const xySum = values.reduce((sum, val, index) => sum + val * (index + 1), 0);
    const xSquaredSum = (n * (n + 1) * (2 * n + 1)) / 6;
    
    return (n * xySum - xSum * ySum) / (n * xSquaredSum - xSum * xSum);
  }

  private detectSeasonalPatterns(historicalData: any[]): SeasonalPattern[] {
    const monthlyAverages = new Map();
    
    historicalData.forEach(data => {
      const month = parseInt(data.period.split('-')[1]);
      if (!monthlyAverages.has(month)) {
        monthlyAverages.set(month, { income: [], expense: [] });
      }
      monthlyAverages.get(month).income.push(data.income);
      monthlyAverages.get(month).expense.push(data.expense);
    });

    const overallAvgIncome = historicalData.reduce((sum, d) => sum + d.income, 0) / historicalData.length;
    
    const patterns: SeasonalPattern[] = [];
    for (let month = 1; month <= 12; month++) {
      const monthData = monthlyAverages.get(month);
      if (monthData && monthData.income.length > 0) {
        const avgIncome = monthData.income.reduce((sum: number, val: number) => sum + val, 0) / monthData.income.length;
        const multiplier = overallAvgIncome > 0 ? avgIncome / overallAvgIncome : 1;
        const variance = this.calculateVariance(monthData.income);
        
        patterns.push({ month, multiplier, variance });
      } else {
        patterns.push({ month, multiplier: 1, variance: 0 });
      }
    }
    
    return patterns;
  }

  private calculateGrowthTrend(historicalData: any[]): GrowthTrend {
    if (historicalData.length < 2) {
      return { incomeGrowthRate: 0, expenseGrowthRate: 0, volatility: 0, trendStrength: 0 };
    }

    const incomeGrowthRates = [];
    const expenseGrowthRates = [];

    for (let i = 1; i < historicalData.length; i++) {
      const prevIncome = historicalData[i - 1].income;
      const currIncome = historicalData[i].income;
      const prevExpense = historicalData[i - 1].expense;
      const currExpense = historicalData[i].expense;

      if (prevIncome > 0) {
        incomeGrowthRates.push(((currIncome - prevIncome) / prevIncome) * 100);
      }
      if (prevExpense > 0) {
        expenseGrowthRates.push(((currExpense - prevExpense) / prevExpense) * 100);
      }
    }

    const avgIncomeGrowth = incomeGrowthRates.reduce((sum, rate) => sum + rate, 0) / incomeGrowthRates.length;
    const avgExpenseGrowth = expenseGrowthRates.reduce((sum, rate) => sum + rate, 0) / expenseGrowthRates.length;
    
    const incomeVolatility = this.calculateVariance(incomeGrowthRates);
    const expenseVolatility = this.calculateVariance(expenseGrowthRates);
    
    return {
      incomeGrowthRate: avgIncomeGrowth || 0,
      expenseGrowthRate: avgExpenseGrowth || 0,
      volatility: (incomeVolatility + expenseVolatility) / 2,
      trendStrength: Math.max(0, 100 - (incomeVolatility + expenseVolatility))
    };
  }

  private calculateVariance(values: number[]): number {
    if (values.length === 0) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    return squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
  }

  private identifyRiskFactors(forecastPeriods: ForecastPeriod[], historicalData: any[]): string[] {
    const riskFactors = [];
    
    // Check for declining trends
    const lastPeriods = forecastPeriods.slice(-3);
    const decliningIncome = lastPeriods.every((period, index) => 
      index === 0 || period.forecastedIncome < lastPeriods[index - 1].forecastedIncome
    );
    
    if (decliningIncome) {
      riskFactors.push('Declining income trend detected');
    }

    // Check for high volatility
    const avgConfidence = forecastPeriods.reduce((sum, p) => sum + p.confidence, 0) / forecastPeriods.length;
    if (avgConfidence < 70) {
      riskFactors.push('High forecast uncertainty due to data volatility');
    }

    // Check for negative net positions
    const negativeMonths = forecastPeriods.filter(p => p.forecastedNet < 0).length;
    if (negativeMonths > forecastPeriods.length * 0.3) {
      riskFactors.push('Multiple periods with negative net position forecasted');
    }

    return riskFactors;
  }

  private generateRecommendations(forecastPeriods: ForecastPeriod[], historicalData: any[]): string[] {
    const recommendations = [];
    
    // Income optimization
    const avgIncome = forecastPeriods.reduce((sum, p) => sum + p.forecastedIncome, 0) / forecastPeriods.length;
    const historicalAvgIncome = historicalData.reduce((sum, d) => sum + d.income, 0) / historicalData.length;
    
    if (avgIncome < historicalAvgIncome * 0.9) {
      recommendations.push('Consider income diversification strategies to improve revenue streams');
    }

    // Expense management
    const expenseGrowth = forecastPeriods.some((period, index) => 
      index > 0 && period.forecastedExpense > forecastPeriods[index - 1].forecastedExpense * 1.1
    );
    
    if (expenseGrowth) {
      recommendations.push('Implement expense control measures to manage growing costs');
    }

    // Cash flow management
    const negativeMonths = forecastPeriods.filter(p => p.forecastedNet < 0);
    if (negativeMonths.length > 0) {
      recommendations.push('Build cash reserves to handle periods with negative cash flow');
    }

    return recommendations;
  }
}

// Export singleton instance
export const budgetForecastingService = new BudgetForecastingService();

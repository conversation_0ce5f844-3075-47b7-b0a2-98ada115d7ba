// lib/services/dashboard/approval-service.ts
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import mongoose from 'mongoose';

// Import existing models
import Leave from '@/models/leave/Leave';
import Budget from '@/models/accounting/Budget';
import Income from '@/models/accounting/Income';
import Expense from '@/models/accounting/Expense';
import User from '@/models/User';
import Employee from '@/models/Employee';
import Department from '@/models/Department';

// Import types
import {
  DashboardApproval,
  ApprovalStats,
  ApprovalFilters,
  ApprovalType,
  ApprovalStatus,
  Priority,
  UrgencyLevel,
  ApprovalAction,
  ApprovalSourceData
} from '@/lib/types/dashboard-approvals';

/**
 * Service for managing dashboard approvals across all modules
 */
export class DashboardApprovalService {
  /**
   * Get pending approvals for dashboard display
   */
  async getPendingApprovals(
    userId: string,
    userRole: string,
    limit: number = 20,
    filters?: ApprovalFilters
  ): Promise<DashboardApproval[]> {
    try {
      await connectToDatabase();
      
      logger.info('Fetching pending approvals for dashboard', LogCategory.DASHBOARD, {
        userId,
        userRole,
        limit,
        filters
      });

      const approvals: DashboardApproval[] = [];

      // Fetch approvals from different modules in parallel
      const [
        leaveApprovals,
        budgetApprovals,
        incomeApprovals,
        expenditureApprovals
      ] = await Promise.all([
        this.getLeaveApprovals(userId, userRole, filters),
        this.getBudgetApprovals(userId, userRole, filters),
        this.getIncomeApprovals(userId, userRole, filters),
        this.getExpenditureApprovals(userId, userRole, filters)
      ]);

      // Combine all approvals
      approvals.push(...leaveApprovals);
      approvals.push(...budgetApprovals);
      approvals.push(...incomeApprovals);
      approvals.push(...expenditureApprovals);

      // Apply additional filters
      let filteredApprovals = this.applyFilters(approvals, filters);

      // Sort by priority and urgency
      filteredApprovals = this.sortApprovals(filteredApprovals);

      // Apply limit
      if (limit > 0) {
        filteredApprovals = filteredApprovals.slice(0, limit);
      }

      logger.info('Successfully fetched pending approvals', LogCategory.DASHBOARD, {
        userId,
        totalApprovals: filteredApprovals.length,
        byType: this.getApprovalCountsByType(filteredApprovals)
      });

      return filteredApprovals;
    } catch (error) {
      logger.error('Error fetching pending approvals', LogCategory.DASHBOARD, error);
      throw error;
    }
  }

  /**
   * Get approval statistics
   */
  async getApprovalStats(
    userId: string,
    userRole: string,
    period: 'day' | 'week' | 'month' = 'week'
  ): Promise<ApprovalStats> {
    try {
      await connectToDatabase();
      
      logger.info('Generating approval statistics', LogCategory.DASHBOARD, {
        userId,
        userRole,
        period
      });

      const now = new Date();
      const startDate = this.getStartDateForPeriod(period, now);

      // Get all approvals for the period
      const allApprovals = await this.getPendingApprovals(userId, userRole, 0);
      
      // Calculate statistics
      const stats: ApprovalStats = {
        totalPending: allApprovals.filter(a => a.status === ApprovalStatus.PENDING).length,
        totalOverdue: allApprovals.filter(a => a.isOverdue).length,
        totalToday: allApprovals.filter(a => this.isToday(a.submittedAt)).length,
        totalThisWeek: allApprovals.filter(a => this.isThisWeek(a.submittedAt)).length,
        
        byType: this.calculateTypeBreakdown(allApprovals),
        byPriority: this.calculatePriorityBreakdown(allApprovals),
        byUrgency: this.calculateUrgencyBreakdown(allApprovals),
        trends: await this.calculateTrends(period, now),
        
        averageApprovalTime: await this.calculateAverageApprovalTime(period),
        approvalRate: await this.calculateApprovalRate(period),
        overdueRate: allApprovals.length > 0 ? 
          (allApprovals.filter(a => a.isOverdue).length / allApprovals.length) * 100 : 0
      };

      logger.info('Successfully generated approval statistics', LogCategory.DASHBOARD, {
        userId,
        totalPending: stats.totalPending,
        totalOverdue: stats.totalOverdue
      });

      return stats;
    } catch (error) {
      logger.error('Error generating approval statistics', LogCategory.DASHBOARD, error);
      throw error;
    }
  }

  /**
   * Get leave approvals
   */
  private async getLeaveApprovals(
    userId: string,
    userRole: string,
    filters?: ApprovalFilters
  ): Promise<DashboardApproval[]> {
    try {
      const query: any = { status: 'pending' };
      
      // Apply date filters if provided
      if (filters?.dateRange) {
        query.createdAt = {};
        if (filters.dateRange.start) {
          query.createdAt.$gte = filters.dateRange.start;
        }
        if (filters.dateRange.end) {
          query.createdAt.$lte = filters.dateRange.end;
        }
      }

      const leaveRequests = await Leave.find(query)
        .populate('employeeId', 'firstName lastName avatar departmentId')
        .populate('leaveTypeId', 'name color')
        .populate('createdBy', 'firstName lastName email avatar')
        .populate({
          path: 'employeeId',
          populate: {
            path: 'departmentId',
            select: 'name'
          }
        })
        .sort({ createdAt: -1 })
        .lean();

      return leaveRequests.map(leave => this.transformLeaveToApproval(leave));
    } catch (error) {
      logger.warn('Error fetching leave approvals', LogCategory.DASHBOARD, error);
      return [];
    }
  }

  /**
   * Get budget approvals
   */
  private async getBudgetApprovals(
    userId: string,
    userRole: string,
    filters?: ApprovalFilters
  ): Promise<DashboardApproval[]> {
    try {
      const query: any = { 
        status: { $in: ['draft', 'submitted', 'pending_approval'] }
      };

      const budgets = await Budget.find(query)
        .populate('createdBy', 'firstName lastName email avatar')
        .populate('departmentId', 'name')
        .sort({ createdAt: -1 })
        .lean();

      return budgets.map(budget => this.transformBudgetToApproval(budget));
    } catch (error) {
      logger.warn('Error fetching budget approvals', LogCategory.DASHBOARD, error);
      return [];
    }
  }

  /**
   * Get income approvals
   */
  private async getIncomeApprovals(
    userId: string,
    userRole: string,
    filters?: ApprovalFilters
  ): Promise<DashboardApproval[]> {
    try {
      const query: any = { 
        'approvalWorkflow.status': 'pending'
      };

      const incomes = await Income.find(query)
        .populate('createdBy', 'firstName lastName email avatar')
        .sort({ createdAt: -1 })
        .lean();

      return incomes.map(income => this.transformIncomeToApproval(income));
    } catch (error) {
      logger.warn('Error fetching income approvals', LogCategory.DASHBOARD, error);
      return [];
    }
  }

  /**
   * Get expenditure approvals
   */
  private async getExpenditureApprovals(
    userId: string,
    userRole: string,
    filters?: ApprovalFilters
  ): Promise<DashboardApproval[]> {
    try {
      const query: any = { 
        'approvalWorkflow.status': 'pending'
      };

      const expenditures = await Expense.find(query)
        .populate('createdBy', 'firstName lastName email avatar')
        .sort({ createdAt: -1 })
        .lean();

      return expenditures.map(expenditure => this.transformExpenditureToApproval(expenditure));
    } catch (error) {
      logger.warn('Error fetching expenditure approvals', LogCategory.DASHBOARD, error);
      return [];
    }
  }

  /**
   * Transform leave request to dashboard approval
   */
  private transformLeaveToApproval(leave: any): DashboardApproval {
    const employee = leave.employeeId;
    const submitter = leave.createdBy;
    const leaveType = leave.leaveTypeId;
    
    const dueDate = new Date(leave.createdAt);
    dueDate.setDate(dueDate.getDate() + 3); // 3 days to approve
    
    const isOverdue = new Date() > dueDate;
    
    return {
      id: leave._id.toString(),
      type: ApprovalType.LEAVE_REQUEST,
      title: `${leaveType?.name || 'Leave'} Request`,
      description: `${employee?.firstName} ${employee?.lastName} - ${leave.duration} day(s)`,
      
      submittedBy: {
        id: submitter?._id?.toString() || '',
        name: submitter ? `${submitter.firstName} ${submitter.lastName}` : 'Unknown',
        email: submitter?.email || '',
        avatar: submitter?.avatar,
        department: employee?.departmentId?.name
      },
      
      employee: {
        id: employee?._id?.toString() || '',
        name: employee ? `${employee.firstName} ${employee.lastName}` : 'Unknown',
        avatar: employee?.avatar,
        department: employee?.departmentId?.name
      },
      
      submittedAt: leave.createdAt,
      dueDate,
      isOverdue,
      urgencyLevel: isOverdue ? UrgencyLevel.OVERDUE : 
                   (new Date() > new Date(dueDate.getTime() - 24 * 60 * 60 * 1000)) ? 
                   UrgencyLevel.URGENT : UrgencyLevel.NORMAL,
      
      currentLevel: 1,
      totalLevels: 2,
      
      status: ApprovalStatus.PENDING,
      priority: this.calculateLeavePriority(leave),
      
      metadata: {
        module: 'hr',
        entityId: leave._id.toString(),
        entityType: 'Leave',
        category: leaveType?.name || 'Leave'
      },
      
      availableActions: [
        ApprovalAction.APPROVE,
        ApprovalAction.REJECT,
        ApprovalAction.VIEW_DETAILS,
        ApprovalAction.COMMENT
      ],
      
      preview: {
        startDate: leave.startDate,
        endDate: leave.endDate,
        duration: leave.duration,
        department: employee?.departmentId?.name
      }
    };
  }

  /**
   * Transform budget to dashboard approval
   */
  private transformBudgetToApproval(budget: any): DashboardApproval {
    const submitter = budget.createdBy;
    const department = budget.departmentId;
    
    const dueDate = new Date(budget.createdAt);
    dueDate.setDate(dueDate.getDate() + 7); // 7 days to approve
    
    const isOverdue = new Date() > dueDate;
    
    return {
      id: budget._id.toString(),
      type: ApprovalType.BUDGET,
      title: `Budget: ${budget.name}`,
      description: `${budget.fiscalYear} - ${this.formatCurrency(budget.totalAmount)}`,
      amount: budget.totalAmount,
      currency: 'MWK',
      
      submittedBy: {
        id: submitter?._id?.toString() || '',
        name: submitter ? `${submitter.firstName} ${submitter.lastName}` : 'Unknown',
        email: submitter?.email || '',
        avatar: submitter?.avatar,
        department: department?.name
      },
      
      submittedAt: budget.createdAt,
      dueDate,
      isOverdue,
      urgencyLevel: isOverdue ? UrgencyLevel.OVERDUE : 
                   (budget.totalAmount > 1000000) ? UrgencyLevel.URGENT : UrgencyLevel.NORMAL,
      
      currentLevel: 1,
      totalLevels: this.getBudgetApprovalLevels(budget.totalAmount),
      
      status: ApprovalStatus.PENDING,
      priority: this.calculateBudgetPriority(budget),
      
      metadata: {
        module: 'accounting',
        entityId: budget._id.toString(),
        entityType: 'Budget',
        category: 'Budget Planning'
      },
      
      availableActions: [
        ApprovalAction.APPROVE,
        ApprovalAction.REJECT,
        ApprovalAction.VIEW_DETAILS,
        ApprovalAction.COMMENT
      ],
      
      preview: {
        budgetCategory: budget.category,
        department: department?.name
      }
    };
  }

  /**
   * Transform income to dashboard approval
   */
  private transformIncomeToApproval(income: any): DashboardApproval {
    const submitter = income.createdBy;
    
    const dueDate = new Date(income.createdAt);
    dueDate.setDate(dueDate.getDate() + 5); // 5 days to approve
    
    const isOverdue = new Date() > dueDate;
    
    return {
      id: income._id.toString(),
      type: ApprovalType.INCOME,
      title: `Income: ${income.source}`,
      description: `${income.category} - ${this.formatCurrency(income.amount)}`,
      amount: income.amount,
      currency: 'MWK',
      
      submittedBy: {
        id: submitter?._id?.toString() || '',
        name: submitter ? `${submitter.firstName} ${submitter.lastName}` : 'Unknown',
        email: submitter?.email || '',
        avatar: submitter?.avatar
      },
      
      submittedAt: income.createdAt,
      dueDate,
      isOverdue,
      urgencyLevel: isOverdue ? UrgencyLevel.OVERDUE : UrgencyLevel.NORMAL,
      
      currentLevel: income.approvalWorkflow?.currentLevel || 1,
      totalLevels: income.approvalWorkflow?.requiredApprovers?.length || 2,
      
      status: ApprovalStatus.PENDING,
      priority: this.calculateIncomePriority(income),
      
      metadata: {
        module: 'accounting',
        entityId: income._id.toString(),
        entityType: 'Income',
        category: income.category
      },
      
      availableActions: [
        ApprovalAction.APPROVE,
        ApprovalAction.REJECT,
        ApprovalAction.VIEW_DETAILS,
        ApprovalAction.COMMENT
      ]
    };
  }

  /**
   * Transform expenditure to dashboard approval
   */
  private transformExpenditureToApproval(expenditure: any): DashboardApproval {
    const submitter = expenditure.createdBy;
    
    const dueDate = new Date(expenditure.createdAt);
    dueDate.setDate(dueDate.getDate() + 5); // 5 days to approve
    
    const isOverdue = new Date() > dueDate;
    
    return {
      id: expenditure._id.toString(),
      type: ApprovalType.EXPENDITURE,
      title: `Expenditure: ${expenditure.description}`,
      description: `${expenditure.category} - ${this.formatCurrency(expenditure.amount)}`,
      amount: expenditure.amount,
      currency: 'MWK',
      
      submittedBy: {
        id: submitter?._id?.toString() || '',
        name: submitter ? `${submitter.firstName} ${submitter.lastName}` : 'Unknown',
        email: submitter?.email || '',
        avatar: submitter?.avatar
      },
      
      submittedAt: expenditure.createdAt,
      dueDate,
      isOverdue,
      urgencyLevel: isOverdue ? UrgencyLevel.OVERDUE : 
                   (expenditure.amount > 500000) ? UrgencyLevel.URGENT : UrgencyLevel.NORMAL,
      
      currentLevel: expenditure.approvalWorkflow?.currentLevel || 1,
      totalLevels: expenditure.approvalWorkflow?.requiredApprovers?.length || 2,
      
      status: ApprovalStatus.PENDING,
      priority: this.calculateExpenditurePriority(expenditure),
      
      metadata: {
        module: 'accounting',
        entityId: expenditure._id.toString(),
        entityType: 'Expenditure',
        category: expenditure.category
      },
      
      availableActions: [
        ApprovalAction.APPROVE,
        ApprovalAction.REJECT,
        ApprovalAction.VIEW_DETAILS,
        ApprovalAction.COMMENT
      ]
    };
  }

  // Helper methods for calculations and transformations
  private calculateLeavePriority(leave: any): Priority {
    const startDate = new Date(leave.startDate);
    const daysUntilStart = Math.ceil((startDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysUntilStart <= 1) return Priority.URGENT;
    if (daysUntilStart <= 3) return Priority.HIGH;
    if (daysUntilStart <= 7) return Priority.MEDIUM;
    return Priority.LOW;
  }

  private calculateBudgetPriority(budget: any): Priority {
    if (budget.totalAmount > 5000000) return Priority.CRITICAL;
    if (budget.totalAmount > 1000000) return Priority.HIGH;
    if (budget.totalAmount > 500000) return Priority.MEDIUM;
    return Priority.LOW;
  }

  private calculateIncomePriority(income: any): Priority {
    if (income.amount > 2000000) return Priority.HIGH;
    if (income.amount > 500000) return Priority.MEDIUM;
    return Priority.LOW;
  }

  private calculateExpenditurePriority(expenditure: any): Priority {
    if (expenditure.amount > 1000000) return Priority.HIGH;
    if (expenditure.amount > 500000) return Priority.MEDIUM;
    return Priority.LOW;
  }

  private getBudgetApprovalLevels(amount: number): number {
    if (amount > 5000000) return 4; // CEO approval required
    if (amount > 1000000) return 3; // Finance Manager
    if (amount > 500000) return 2; // Department Head
    return 1; // Supervisor
  }

  private formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0
    }).format(amount);
  }

  private applyFilters(approvals: DashboardApproval[], filters?: ApprovalFilters): DashboardApproval[] {
    if (!filters) return approvals;

    return approvals.filter(approval => {
      // Type filter
      if (filters.types && filters.types.length > 0) {
        if (!filters.types.includes(approval.type)) return false;
      }

      // Status filter
      if (filters.statuses && filters.statuses.length > 0) {
        if (!filters.statuses.includes(approval.status)) return false;
      }

      // Priority filter
      if (filters.priorities && filters.priorities.length > 0) {
        if (!filters.priorities.includes(approval.priority)) return false;
      }

      // Amount range filter
      if (filters.amountRange && approval.amount) {
        if (filters.amountRange.min && approval.amount < filters.amountRange.min) return false;
        if (filters.amountRange.max && approval.amount > filters.amountRange.max) return false;
      }

      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const searchableText = `${approval.title} ${approval.description} ${approval.submittedBy.name}`.toLowerCase();
        if (!searchableText.includes(searchLower)) return false;
      }

      return true;
    });
  }

  private sortApprovals(approvals: DashboardApproval[]): DashboardApproval[] {
    return approvals.sort((a, b) => {
      // First sort by urgency (overdue first)
      if (a.urgencyLevel !== b.urgencyLevel) {
        const urgencyOrder = { 
          [UrgencyLevel.OVERDUE]: 0, 
          [UrgencyLevel.CRITICAL]: 1, 
          [UrgencyLevel.URGENT]: 2, 
          [UrgencyLevel.NORMAL]: 3 
        };
        return urgencyOrder[a.urgencyLevel] - urgencyOrder[b.urgencyLevel];
      }

      // Then sort by priority
      if (a.priority !== b.priority) {
        const priorityOrder = { 
          [Priority.CRITICAL]: 0, 
          [Priority.URGENT]: 1, 
          [Priority.HIGH]: 2, 
          [Priority.MEDIUM]: 3, 
          [Priority.LOW]: 4 
        };
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      }

      // Finally sort by submission date (newest first)
      return new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime();
    });
  }

  private getApprovalCountsByType(approvals: DashboardApproval[]): Record<string, number> {
    const counts: Record<string, number> = {};
    approvals.forEach(approval => {
      counts[approval.type] = (counts[approval.type] || 0) + 1;
    });
    return counts;
  }

  private calculateTypeBreakdown(approvals: DashboardApproval[]): Array<{ type: ApprovalType; count: number; percentage: number }> {
    const counts = this.getApprovalCountsByType(approvals);
    const total = approvals.length;
    
    return Object.entries(counts).map(([type, count]) => ({
      type: type as ApprovalType,
      count,
      percentage: total > 0 ? Math.round((count / total) * 100) : 0
    }));
  }

  private calculatePriorityBreakdown(approvals: DashboardApproval[]): Array<{ priority: Priority; count: number; percentage: number }> {
    const counts: Record<Priority, number> = {
      [Priority.CRITICAL]: 0,
      [Priority.URGENT]: 0,
      [Priority.HIGH]: 0,
      [Priority.MEDIUM]: 0,
      [Priority.LOW]: 0
    };

    approvals.forEach(approval => {
      counts[approval.priority]++;
    });

    const total = approvals.length;
    return Object.entries(counts).map(([priority, count]) => ({
      priority: priority as Priority,
      count,
      percentage: total > 0 ? Math.round((count / total) * 100) : 0
    }));
  }

  private calculateUrgencyBreakdown(approvals: DashboardApproval[]): Array<{ urgency: UrgencyLevel; count: number; percentage: number }> {
    const counts: Record<UrgencyLevel, number> = {
      [UrgencyLevel.OVERDUE]: 0,
      [UrgencyLevel.CRITICAL]: 0,
      [UrgencyLevel.URGENT]: 0,
      [UrgencyLevel.NORMAL]: 0
    };

    approvals.forEach(approval => {
      counts[approval.urgencyLevel]++;
    });

    const total = approvals.length;
    return Object.entries(counts).map(([urgency, count]) => ({
      urgency: urgency as UrgencyLevel,
      count,
      percentage: total > 0 ? Math.round((count / total) * 100) : 0
    }));
  }

  private async calculateTrends(period: 'day' | 'week' | 'month', now: Date): Promise<Array<{ date: string; submitted: number; approved: number; rejected: number }>> {
    // This would require historical data tracking
    // For now, return empty array - can be implemented later
    return [];
  }

  private async calculateAverageApprovalTime(period: 'day' | 'week' | 'month'): Promise<number> {
    // This would require tracking approval completion times
    // For now, return a default value - can be implemented later
    return 24; // 24 hours average
  }

  private async calculateApprovalRate(period: 'day' | 'week' | 'month'): Promise<number> {
    // This would require historical approval data
    // For now, return a default value - can be implemented later
    return 85; // 85% approval rate
  }

  private getStartDateForPeriod(period: 'day' | 'week' | 'month', now: Date): Date {
    switch (period) {
      case 'day':
        return new Date(now.getFullYear(), now.getMonth(), now.getDate());
      case 'week':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case 'month':
        return new Date(now.getFullYear(), now.getMonth(), 1);
      default:
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }
  }

  private isToday(date: Date): boolean {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  }

  private isThisWeek(date: Date): boolean {
    const now = new Date();
    const weekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    return date >= weekStart;
  }
}

// Export singleton instance
export const dashboardApprovalService = new DashboardApprovalService();

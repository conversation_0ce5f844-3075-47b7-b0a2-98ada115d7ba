/**
 * Attendance API service for frontend integration
 */

import { AttendanceRecord } from '@/types/attendance';

export interface AttendanceApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  details?: any;
}

export interface AttendanceSummaryData {
  employeeId: string;
  employeeName: string;
  period: { year: number; month: number };
  summary: {
    totalWorkingDays: number;
    daysPresent: number;
    daysAbsent: number;
    daysLate: number;
    daysHalfDay: number;
    daysOnLeave: number;
    totalHours: number;
    regularHours: number;
    overtimeHours: number;
    lateHours: number;
    attendanceRate: number;
    punctualityRate: number;
    averageHoursPerDay: number;
    lateDeductions: number;
    absenceDeductions: number;
    overtimeBonus: number;
    perfectAttendanceBonus: number;
    calculatedAt: string;
    calculatedBy: string;
    isFinalized: boolean;
    finalizedAt?: string;
    finalizedBy?: string;
  };
}

export interface AttendanceOptions {
  includeWeekends?: boolean;
  includeHolidays?: boolean;
  workingHoursPerDay?: number;
  overtimeThreshold?: number;
  lateThresholdMinutes?: number;
  regenerate?: boolean;
}

export interface BulkAttendanceSummaryData {
  period: { year: number; month: number };
  summary: {
    totalEmployees: number;
    successful: number;
    failed: number;
    successRate: number;
  };
  errors: string[];
  options: AttendanceOptions;
}

export interface AttendanceSummaryStatus {
  period: { year: number; month: number };
  status: {
    totalActiveEmployees: number;
    summariesGenerated: number;
    summariesFinalized: number;
    summariesPending: number;
    summariesMissing: number;
    completionRate: number;
    finalizationRate: number;
  };
  summaries: {
    finalized: Array<{
      employeeId: string;
      employeeName: string;
      attendanceRate: number;
      totalHours: number;
      finalizedAt: string;
      finalizedBy: string;
    }>;
    pending: Array<{
      employeeId: string;
      employeeName: string;
      attendanceRate: number;
      totalHours: number;
      calculatedAt: string;
      calculatedBy: string;
    }>;
  };
}

/**
 * Attendance API service class
 */
export class AttendanceApiService {
  private baseUrl = '/api';

  /**
   * Get attendance records for a specific date range
   */
  async getAttendanceRecords(params: {
    startDate?: string;
    endDate?: string;
    employeeId?: string;
    department?: string;
    status?: string;
    page?: number;
    limit?: number;
  }): Promise<AttendanceApiResponse<{ records: AttendanceRecord[]; total: number }>> {
    try {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`${this.baseUrl}/attendance?${searchParams}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch attendance records');
      }

      return data;
    } catch (error) {
      console.error('Error fetching attendance records:', error);
      throw error;
    }
  }

  /**
   * Get attendance summary for an employee and period
   */
  async getAttendanceSummary(
    employeeId: string,
    period: string // Format: YYYY-MM
  ): Promise<AttendanceApiResponse<AttendanceSummaryData>> {
    try {
      const response = await fetch(`${this.baseUrl}/attendance/summary/${employeeId}/${period}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch attendance summary');
      }

      return data;
    } catch (error) {
      console.error('Error fetching attendance summary:', error);
      throw error;
    }
  }

  /**
   * Generate attendance summary for an employee and period
   */
  async generateAttendanceSummary(
    employeeId: string,
    period: string, // Format: YYYY-MM
    options: AttendanceOptions = {}
  ): Promise<AttendanceApiResponse<AttendanceSummaryData>> {
    try {
      const response = await fetch(`${this.baseUrl}/attendance/summary/${employeeId}/${period}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(options),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to generate attendance summary');
      }

      return data;
    } catch (error) {
      console.error('Error generating attendance summary:', error);
      throw error;
    }
  }

  /**
   * Finalize attendance summary for payroll processing
   */
  async finalizeAttendanceSummary(
    employeeId: string,
    period: string // Format: YYYY-MM
  ): Promise<AttendanceApiResponse<AttendanceSummaryData>> {
    try {
      const response = await fetch(`${this.baseUrl}/attendance/summary/${employeeId}/${period}`, {
        method: 'PATCH',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to finalize attendance summary');
      }

      return data;
    } catch (error) {
      console.error('Error finalizing attendance summary:', error);
      throw error;
    }
  }

  /**
   * Generate bulk attendance summaries for all employees
   */
  async generateBulkAttendanceSummaries(
    year: number,
    month: number,
    options: AttendanceOptions = {}
  ): Promise<AttendanceApiResponse<BulkAttendanceSummaryData>> {
    try {
      const response = await fetch(`${this.baseUrl}/attendance/summary/bulk`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ year, month, options }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to generate bulk attendance summaries');
      }

      return data;
    } catch (error) {
      console.error('Error generating bulk attendance summaries:', error);
      throw error;
    }
  }

  /**
   * Get bulk attendance summary status
   */
  async getBulkAttendanceSummaryStatus(
    year: number,
    month: number
  ): Promise<AttendanceApiResponse<AttendanceSummaryStatus>> {
    try {
      const response = await fetch(`${this.baseUrl}/attendance/summary/bulk?year=${year}&month=${month}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch bulk attendance summary status');
      }

      return data;
    } catch (error) {
      console.error('Error fetching bulk attendance summary status:', error);
      throw error;
    }
  }

  /**
   * Create or update attendance record
   */
  async saveAttendanceRecord(record: Partial<AttendanceRecord>): Promise<AttendanceApiResponse<AttendanceRecord>> {
    try {
      const method = record.id ? 'PUT' : 'POST';
      const url = record.id ? `${this.baseUrl}/attendance/${record.id}` : `${this.baseUrl}/attendance`;

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(record),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to save attendance record');
      }

      return data;
    } catch (error) {
      console.error('Error saving attendance record:', error);
      throw error;
    }
  }

  /**
   * Delete attendance record
   */
  async deleteAttendanceRecord(recordId: string): Promise<AttendanceApiResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/attendance/${recordId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to delete attendance record');
      }

      return data;
    } catch (error) {
      console.error('Error deleting attendance record:', error);
      throw error;
    }
  }

  /**
   * Get attendance statistics for dashboard
   */
  async getAttendanceStats(params: {
    startDate?: string;
    endDate?: string;
    department?: string;
  } = {}): Promise<AttendanceApiResponse<{
    totalEmployees: number;
    presentToday: number;
    absentToday: number;
    lateToday: number;
    averageAttendanceRate: number;
    totalHoursWorked: number;
    overtimeHours: number;
  }>> {
    try {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`${this.baseUrl}/attendance/stats?${searchParams}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch attendance statistics');
      }

      return data;
    } catch (error) {
      console.error('Error fetching attendance statistics:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const attendanceApi = new AttendanceApiService();

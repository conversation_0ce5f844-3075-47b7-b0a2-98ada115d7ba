/**
 * Task API service for frontend integration
 */

import { Task } from '@/types/task';

export interface TaskApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  details?: any;
}

export interface TaskSummaryData {
  employeeId: string;
  employeeName: string;
  period: { year: number; month: number };
  summary: {
    totalTasksAssigned: number;
    totalTasksCompleted: number;
    totalTasksInProgress: number;
    totalTasksOverdue: number;
    totalEstimatedHours: number;
    totalActualHours: number;
    averageTaskCompletionTime: number;
    efficiencyRatio: number;
    completionRate: number;
    onTimeCompletionRate: number;
    averageQualityScore: number;
    totalTaskPayment: number;
    bonusPayment: number;
    penaltyDeduction: number;
    taskCompletions: Array<{
      taskId: string;
      taskTitle: string;
      taskType: string;
      completedDate: string;
      estimatedHours: number;
      actualHours: number;
      qualityScore?: number;
      difficultyLevel?: 'easy' | 'medium' | 'hard' | 'expert';
      paymentAmount: number;
      paymentType: 'per_task' | 'per_hour' | 'per_project';
    }>;
    calculatedAt: string;
    calculatedBy: string;
    isFinalized: boolean;
    finalizedAt?: string;
    finalizedBy?: string;
  };
}

export interface TaskOptions {
  includeInProgressTasks?: boolean;
  qualityScoreThreshold?: number;
  onTimeThresholdDays?: number;
  performanceBonusThreshold?: number;
  regenerate?: boolean;
}

export interface TaskStats {
  totalTasks: number;
  completedTasks: number;
  inProgressTasks: number;
  overdueTasks: number;
  averageCompletionTime: number;
  averageQualityScore: number;
  totalHoursWorked: number;
  efficiencyRatio: number;
}

/**
 * Task API service class
 */
export class TaskApiService {
  private baseUrl = '/api';

  /**
   * Get tasks with filtering and pagination
   */
  async getTasks(params: {
    assignedTo?: string;
    assignedBy?: string;
    status?: string;
    priority?: string;
    category?: string;
    startDate?: string;
    endDate?: string;
    search?: string;
    page?: number;
    limit?: number;
  } = {}): Promise<TaskApiResponse<{ tasks: Task[]; total: number }>> {
    try {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`${this.baseUrl}/tasks?${searchParams}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch tasks');
      }

      return data;
    } catch (error) {
      console.error('Error fetching tasks:', error);
      throw error;
    }
  }

  /**
   * Get tasks assigned to current user
   */
  async getMyTasks(params: {
    status?: string;
    priority?: string;
    category?: string;
    search?: string;
    page?: number;
    limit?: number;
  } = {}): Promise<TaskApiResponse<{ tasks: Task[]; total: number }>> {
    try {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`${this.baseUrl}/tasks/my-tasks?${searchParams}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch my tasks');
      }

      return data;
    } catch (error) {
      console.error('Error fetching my tasks:', error);
      throw error;
    }
  }

  /**
   * Get team tasks (tasks assigned to team members)
   */
  async getTeamTasks(params: {
    teamId?: string;
    departmentId?: string;
    status?: string;
    priority?: string;
    search?: string;
    page?: number;
    limit?: number;
  } = {}): Promise<TaskApiResponse<{ tasks: Task[]; total: number; teamMembers: any[] }>> {
    try {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`${this.baseUrl}/tasks/team-tasks?${searchParams}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch team tasks');
      }

      return data;
    } catch (error) {
      console.error('Error fetching team tasks:', error);
      throw error;
    }
  }

  /**
   * Get task by ID
   */
  async getTask(taskId: string): Promise<TaskApiResponse<Task>> {
    try {
      const response = await fetch(`${this.baseUrl}/tasks/${taskId}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch task');
      }

      return data;
    } catch (error) {
      console.error('Error fetching task:', error);
      throw error;
    }
  }

  /**
   * Create or update task
   */
  async saveTask(task: Partial<Task>): Promise<TaskApiResponse<Task>> {
    try {
      const method = task.id ? 'PUT' : 'POST';
      const url = task.id ? `${this.baseUrl}/tasks/${task.id}` : `${this.baseUrl}/tasks`;

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(task),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to save task');
      }

      return data;
    } catch (error) {
      console.error('Error saving task:', error);
      throw error;
    }
  }

  /**
   * Update task status
   */
  async updateTaskStatus(
    taskId: string,
    status: string,
    actualHours?: number,
    qualityScore?: number
  ): Promise<TaskApiResponse<Task>> {
    try {
      const response = await fetch(`${this.baseUrl}/tasks/${taskId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status, actualHours, qualityScore }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to update task status');
      }

      return data;
    } catch (error) {
      console.error('Error updating task status:', error);
      throw error;
    }
  }

  /**
   * Delete task
   */
  async deleteTask(taskId: string): Promise<TaskApiResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/tasks/${taskId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to delete task');
      }

      return data;
    } catch (error) {
      console.error('Error deleting task:', error);
      throw error;
    }
  }

  /**
   * Get task summary for an employee and period
   */
  async getTaskSummary(
    employeeId: string,
    period: string // Format: YYYY-MM
  ): Promise<TaskApiResponse<TaskSummaryData>> {
    try {
      const response = await fetch(`${this.baseUrl}/tasks/summary/${employeeId}/${period}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch task summary');
      }

      return data;
    } catch (error) {
      console.error('Error fetching task summary:', error);
      throw error;
    }
  }

  /**
   * Generate task summary for an employee and period
   */
  async generateTaskSummary(
    employeeId: string,
    period: string, // Format: YYYY-MM
    options: TaskOptions = {}
  ): Promise<TaskApiResponse<TaskSummaryData>> {
    try {
      const response = await fetch(`${this.baseUrl}/tasks/summary/${employeeId}/${period}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(options),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to generate task summary');
      }

      return data;
    } catch (error) {
      console.error('Error generating task summary:', error);
      throw error;
    }
  }

  /**
   * Finalize task summary for payroll processing
   */
  async finalizeTaskSummary(
    employeeId: string,
    period: string // Format: YYYY-MM
  ): Promise<TaskApiResponse<TaskSummaryData>> {
    try {
      const response = await fetch(`${this.baseUrl}/tasks/summary/${employeeId}/${period}`, {
        method: 'PATCH',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to finalize task summary');
      }

      return data;
    } catch (error) {
      console.error('Error finalizing task summary:', error);
      throw error;
    }
  }

  /**
   * Get task statistics for dashboard
   */
  async getTaskStats(params: {
    employeeId?: string;
    teamId?: string;
    departmentId?: string;
    startDate?: string;
    endDate?: string;
  } = {}): Promise<TaskApiResponse<TaskStats>> {
    try {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`${this.baseUrl}/tasks/stats?${searchParams}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch task statistics');
      }

      return data;
    } catch (error) {
      console.error('Error fetching task statistics:', error);
      throw error;
    }
  }

  /**
   * Assign task to employees
   */
  async assignTask(
    taskId: string,
    employeeIds: string[]
  ): Promise<TaskApiResponse<Task>> {
    try {
      const response = await fetch(`${this.baseUrl}/tasks/${taskId}/assign`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ employeeIds }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to assign task');
      }

      return data;
    } catch (error) {
      console.error('Error assigning task:', error);
      throw error;
    }
  }

  /**
   * Add comment to task
   */
  async addTaskComment(
    taskId: string,
    comment: string
  ): Promise<TaskApiResponse<Task>> {
    try {
      const response = await fetch(`${this.baseUrl}/tasks/${taskId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ comment }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to add comment');
      }

      return data;
    } catch (error) {
      console.error('Error adding task comment:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const taskApi = new TaskApiService();

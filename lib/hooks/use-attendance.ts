/**
 * React hooks for attendance data management
 */

import { useState, useEffect, useCallback } from 'react';
import { attendanceApi, AttendanceApiResponse, AttendanceSummaryData, AttendanceOptions } from '@/lib/api/attendance-api';
import { AttendanceRecord } from '@/types/attendance';

export interface UseAttendanceRecordsOptions {
  startDate?: string;
  endDate?: string;
  employeeId?: string;
  department?: string;
  status?: string;
  page?: number;
  limit?: number;
  autoFetch?: boolean;
}

export interface UseAttendanceRecordsReturn {
  records: AttendanceRecord[];
  total: number;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  fetchMore: () => Promise<void>;
  hasMore: boolean;
}

/**
 * Hook for managing attendance records
 */
export function useAttendanceRecords(options: UseAttendanceRecordsOptions = {}): UseAttendanceRecordsReturn {
  const [records, setRecords] = useState<AttendanceRecord[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(options.page || 1);

  const fetchRecords = useCallback(async (page = 1, append = false) => {
    try {
      setLoading(true);
      setError(null);

      const response = await attendanceApi.getAttendanceRecords({
        ...options,
        page,
      });

      if (response.success && response.data) {
        if (append) {
          setRecords(prev => [...prev, ...response.data.records]);
        } else {
          setRecords(response.data.records);
        }
        setTotal(response.data.total);
        setCurrentPage(page);
      } else {
        // Check if this is a structured error response indicating no data available
        if (response.error && response.error.code === 'ATTENDANCE_RECORDS_UNAVAILABLE') {
          // This is expected - records are not available, not an error
          setError(null);
          setRecords([]);
          setTotal(0);
          setCurrentPage(page);
        } else {
          throw new Error(response.message || 'Failed to fetch attendance records');
        }
      }
    } catch (err) {
      console.error('Attendance records error:', err);

      // Handle different types of errors
      let errorMessage = 'Failed to fetch attendance records';

      if (err instanceof Error) {
        if (err.message.includes('not implemented') || err.message.includes('501')) {
          errorMessage = 'Attendance records are not yet available. This feature is under development.';
        } else if (err.message.includes('unavailable') || err.message.includes('RECORDS_UNAVAILABLE')) {
          // This is expected - no error, just no data
          setError(null);
          setRecords([]);
          setTotal(0);
          setLoading(false);
          return;
        } else if (err.message.includes('network') || err.message.includes('fetch')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);

      // Set empty records to prevent infinite loading
      if (!append) {
        setRecords([]);
        setTotal(0);
      }
    } finally {
      setLoading(false);
    }
  }, [options]);

  const refetch = useCallback(() => fetchRecords(1, false), [fetchRecords]);

  const fetchMore = useCallback(() => {
    if (!loading && hasMore) {
      fetchRecords(currentPage + 1, true);
    }
  }, [fetchRecords, currentPage, loading]);

  const hasMore = records.length < total;

  useEffect(() => {
    if (options.autoFetch !== false) {
      fetchRecords();
    }
  }, [fetchRecords, options.autoFetch]);

  return {
    records,
    total,
    loading,
    error,
    refetch,
    fetchMore,
    hasMore,
  };
}

export interface UseAttendanceSummaryOptions {
  employeeId: string;
  period: string; // Format: YYYY-MM
  autoFetch?: boolean;
}

export interface UseAttendanceSummaryReturn {
  summary: AttendanceSummaryData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  generate: (options?: AttendanceOptions) => Promise<void>;
  finalize: () => Promise<void>;
  generating: boolean;
  finalizing: boolean;
}

/**
 * Hook for managing attendance summary
 */
export function useAttendanceSummary(options: UseAttendanceSummaryOptions): UseAttendanceSummaryReturn {
  const [summary, setSummary] = useState<AttendanceSummaryData | null>(null);
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [finalizing, setFinalizing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSummary = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await attendanceApi.getAttendanceSummary(options.employeeId, options.period);

      if (response.success && response.data) {
        setSummary(response.data);
      } else {
        // Summary doesn't exist yet
        setSummary(null);
        if (response.error !== 'SUMMARY_NOT_FOUND') {
          setError(response.message || 'Failed to fetch attendance summary');
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching attendance summary:', err);
    } finally {
      setLoading(false);
    }
  }, [options.employeeId, options.period]);

  const generate = useCallback(async (generateOptions: AttendanceOptions = {}) => {
    try {
      setGenerating(true);
      setError(null);

      const response = await attendanceApi.generateAttendanceSummary(
        options.employeeId,
        options.period,
        generateOptions
      );

      if (response.success && response.data) {
        setSummary(response.data);
      } else {
        throw new Error(response.message || 'Failed to generate attendance summary');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error generating attendance summary:', err);
    } finally {
      setGenerating(false);
    }
  }, [options.employeeId, options.period]);

  const finalize = useCallback(async () => {
    try {
      setFinalizing(true);
      setError(null);

      const response = await attendanceApi.finalizeAttendanceSummary(options.employeeId, options.period);

      if (response.success && response.data) {
        setSummary(response.data);
      } else {
        throw new Error(response.message || 'Failed to finalize attendance summary');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error finalizing attendance summary:', err);
    } finally {
      setFinalizing(false);
    }
  }, [options.employeeId, options.period]);

  const refetch = useCallback(() => fetchSummary(), [fetchSummary]);

  useEffect(() => {
    if (options.autoFetch !== false) {
      fetchSummary();
    }
  }, [fetchSummary, options.autoFetch]);

  return {
    summary,
    loading,
    error,
    refetch,
    generate,
    finalize,
    generating,
    finalizing,
  };
}

export interface UseAttendanceStatsReturn {
  stats: {
    totalEmployees: number;
    presentToday: number;
    absentToday: number;
    lateToday: number;
    averageAttendanceRate: number;
    totalHoursWorked: number;
    overtimeHours: number;
  } | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Hook for attendance statistics
 */
export function useAttendanceStats(params: {
  startDate?: string;
  endDate?: string;
  department?: string;
  autoFetch?: boolean;
} = {}): UseAttendanceStatsReturn {
  const [stats, setStats] = useState<UseAttendanceStatsReturn['stats']>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await attendanceApi.getAttendanceStats(params);

      if (response.success && response.data) {
        setStats(response.data);
      } else {
        // Check if this is a structured error response indicating no data available
        if (response.error && response.error.code === 'ATTENDANCE_STATS_UNAVAILABLE') {
          // This is expected - stats are not available, not an error
          setError(null);
          setStats(null); // Set to null to indicate no data, not empty data
        } else {
          throw new Error(response.message || 'Failed to fetch attendance statistics');
        }
      }
    } catch (err) {
      console.error('Attendance stats error:', err);

      // Handle different types of errors
      let errorMessage = 'Failed to fetch attendance statistics';

      if (err instanceof Error) {
        if (err.message.includes('not implemented') || err.message.includes('501')) {
          errorMessage = 'Attendance statistics are not yet available. This feature is under development.';
        } else if (err.message.includes('unavailable') || err.message.includes('STATS_UNAVAILABLE')) {
          // This is expected - no error, just no data
          setError(null);
          setStats(null);
          setLoading(false);
          return;
        } else if (err.message.includes('network') || err.message.includes('fetch')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
      setStats(null); // Set to null instead of empty object
    } finally {
      setLoading(false);
    }
  }, [params]);

  const refetch = useCallback(() => fetchStats(), [fetchStats]);

  useEffect(() => {
    if (params.autoFetch !== false) {
      fetchStats();
    }
  }, [fetchStats, params.autoFetch]);

  return {
    stats,
    loading,
    error,
    refetch,
  };
}

export interface UseAttendanceRecordMutationReturn {
  save: (record: Partial<AttendanceRecord>) => Promise<AttendanceRecord>;
  remove: (recordId: string) => Promise<void>;
  saving: boolean;
  deleting: boolean;
  error: string | null;
}

/**
 * Hook for attendance record mutations (create, update, delete)
 */
export function useAttendanceRecordMutation(): UseAttendanceRecordMutationReturn {
  const [saving, setSaving] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const save = useCallback(async (record: Partial<AttendanceRecord>): Promise<AttendanceRecord> => {
    try {
      setSaving(true);
      setError(null);

      const response = await attendanceApi.saveAttendanceRecord(record);

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to save attendance record');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error saving attendance record:', err);
      throw err;
    } finally {
      setSaving(false);
    }
  }, []);

  const remove = useCallback(async (recordId: string): Promise<void> => {
    try {
      setDeleting(true);
      setError(null);

      const response = await attendanceApi.deleteAttendanceRecord(recordId);

      if (!response.success) {
        throw new Error(response.message || 'Failed to delete attendance record');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error deleting attendance record:', err);
      throw err;
    } finally {
      setDeleting(false);
    }
  }, []);

  return {
    save,
    remove,
    saving,
    deleting,
    error,
  };
}

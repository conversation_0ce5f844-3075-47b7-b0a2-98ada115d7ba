/**
 * React hooks for task data management
 */

import { useState, useEffect, useCallback } from 'react';
import { taskApi, TaskApiResponse, TaskSummaryData, TaskOptions, TaskStats } from '@/lib/api/task-api';
import { Task } from '@/types/task';

export interface UseTasksOptions {
  assignedTo?: string;
  assignedBy?: string;
  status?: string;
  priority?: string;
  category?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
  page?: number;
  limit?: number;
  autoFetch?: boolean;
}

export interface UseTasksReturn {
  tasks: Task[];
  total: number;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  fetchMore: () => Promise<void>;
  hasMore: boolean;
}

/**
 * Hook for managing tasks
 */
export function useTasks(options: UseTasksOptions = {}): UseTasksReturn {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(options.page || 1);

  const fetchTasks = useCallback(async (page = 1, append = false) => {
    try {
      setLoading(true);
      setError(null);

      const response = await taskApi.getTasks({
        ...options,
        page,
      });

      if (response.success && response.data) {
        if (append) {
          setTasks(prev => [...prev, ...response.data.tasks]);
        } else {
          setTasks(response.data.tasks);
        }
        setTotal(response.data.total);
        setCurrentPage(page);
      } else {
        throw new Error(response.message || 'Failed to fetch tasks');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching tasks:', err);
    } finally {
      setLoading(false);
    }
  }, [options]);

  const refetch = useCallback(() => fetchTasks(1, false), [fetchTasks]);

  const fetchMore = useCallback(() => {
    if (!loading && hasMore) {
      fetchTasks(currentPage + 1, true);
    }
  }, [fetchTasks, currentPage, loading]);

  const hasMore = tasks.length < total;

  useEffect(() => {
    if (options.autoFetch !== false) {
      fetchTasks();
    }
  }, [fetchTasks, options.autoFetch]);

  return {
    tasks,
    total,
    loading,
    error,
    refetch,
    fetchMore,
    hasMore,
  };
}

export interface UseMyTasksOptions {
  status?: string;
  priority?: string;
  category?: string;
  search?: string;
  page?: number;
  limit?: number;
  autoFetch?: boolean;
}

export interface UseMyTasksReturn {
  tasks: Task[];
  total: number;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  fetchMore: () => Promise<void>;
  hasMore: boolean;
}

/**
 * Hook for managing current user's tasks
 */
export function useMyTasks(options: UseMyTasksOptions = {}): UseMyTasksReturn {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(options.page || 1);

  const fetchTasks = useCallback(async (page = 1, append = false) => {
    try {
      setLoading(true);
      setError(null);

      const response = await taskApi.getMyTasks({
        ...options,
        page,
      });

      if (response.success && response.data) {
        if (append) {
          setTasks(prev => [...prev, ...response.data.tasks]);
        } else {
          setTasks(response.data.tasks);
        }
        setTotal(response.data.total);
        setCurrentPage(page);
      } else {
        throw new Error(response.message || 'Failed to fetch my tasks');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching my tasks:', err);
    } finally {
      setLoading(false);
    }
  }, [options]);

  const refetch = useCallback(() => fetchTasks(1, false), [fetchTasks]);

  const fetchMore = useCallback(() => {
    if (!loading && hasMore) {
      fetchTasks(currentPage + 1, true);
    }
  }, [fetchTasks, currentPage, loading]);

  const hasMore = tasks.length < total;

  useEffect(() => {
    if (options.autoFetch !== false) {
      fetchTasks();
    }
  }, [fetchTasks, options.autoFetch]);

  return {
    tasks,
    total,
    loading,
    error,
    refetch,
    fetchMore,
    hasMore,
  };
}

export interface UseTeamTasksOptions {
  teamId?: string;
  departmentId?: string;
  status?: string;
  priority?: string;
  search?: string;
  page?: number;
  limit?: number;
  autoFetch?: boolean;
}

export interface UseTeamTasksReturn {
  tasks: Task[];
  teamMembers: any[];
  total: number;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  fetchMore: () => Promise<void>;
  hasMore: boolean;
}

/**
 * Hook for managing team tasks
 */
export function useTeamTasks(options: UseTeamTasksOptions = {}): UseTeamTasksReturn {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [teamMembers, setTeamMembers] = useState<any[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(options.page || 1);

  const fetchTasks = useCallback(async (page = 1, append = false) => {
    try {
      setLoading(true);
      setError(null);

      const response = await taskApi.getTeamTasks({
        ...options,
        page,
      });

      if (response.success && response.data) {
        if (append) {
          setTasks(prev => [...prev, ...response.data.tasks]);
        } else {
          setTasks(response.data.tasks);
          setTeamMembers(response.data.teamMembers || []);
        }
        setTotal(response.data.total);
        setCurrentPage(page);
      } else {
        throw new Error(response.message || 'Failed to fetch team tasks');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching team tasks:', err);
    } finally {
      setLoading(false);
    }
  }, [options]);

  const refetch = useCallback(() => fetchTasks(1, false), [fetchTasks]);

  const fetchMore = useCallback(() => {
    if (!loading && hasMore) {
      fetchTasks(currentPage + 1, true);
    }
  }, [fetchTasks, currentPage, loading]);

  const hasMore = tasks.length < total;

  useEffect(() => {
    if (options.autoFetch !== false) {
      fetchTasks();
    }
  }, [fetchTasks, options.autoFetch]);

  return {
    tasks,
    teamMembers,
    total,
    loading,
    error,
    refetch,
    fetchMore,
    hasMore,
  };
}

export interface UseTaskSummaryOptions {
  employeeId: string;
  period: string; // Format: YYYY-MM
  autoFetch?: boolean;
}

export interface UseTaskSummaryReturn {
  summary: TaskSummaryData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  generate: (options?: TaskOptions) => Promise<void>;
  finalize: () => Promise<void>;
  generating: boolean;
  finalizing: boolean;
}

/**
 * Hook for managing task summary
 */
export function useTaskSummary(options: UseTaskSummaryOptions): UseTaskSummaryReturn {
  const [summary, setSummary] = useState<TaskSummaryData | null>(null);
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [finalizing, setFinalizing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSummary = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await taskApi.getTaskSummary(options.employeeId, options.period);

      if (response.success && response.data) {
        setSummary(response.data);
      } else {
        // Summary doesn't exist yet
        setSummary(null);
        if (response.error !== 'SUMMARY_NOT_FOUND') {
          setError(response.message || 'Failed to fetch task summary');
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching task summary:', err);
    } finally {
      setLoading(false);
    }
  }, [options.employeeId, options.period]);

  const generate = useCallback(async (generateOptions: TaskOptions = {}) => {
    try {
      setGenerating(true);
      setError(null);

      const response = await taskApi.generateTaskSummary(
        options.employeeId,
        options.period,
        generateOptions
      );

      if (response.success && response.data) {
        setSummary(response.data);
      } else {
        throw new Error(response.message || 'Failed to generate task summary');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error generating task summary:', err);
    } finally {
      setGenerating(false);
    }
  }, [options.employeeId, options.period]);

  const finalize = useCallback(async () => {
    try {
      setFinalizing(true);
      setError(null);

      const response = await taskApi.finalizeTaskSummary(options.employeeId, options.period);

      if (response.success && response.data) {
        setSummary(response.data);
      } else {
        throw new Error(response.message || 'Failed to finalize task summary');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error finalizing task summary:', err);
    } finally {
      setFinalizing(false);
    }
  }, [options.employeeId, options.period]);

  const refetch = useCallback(() => fetchSummary(), [fetchSummary]);

  useEffect(() => {
    if (options.autoFetch !== false) {
      fetchSummary();
    }
  }, [fetchSummary, options.autoFetch]);

  return {
    summary,
    loading,
    error,
    refetch,
    generate,
    finalize,
    generating,
    finalizing,
  };
}

export interface UseTaskMutationReturn {
  save: (task: Partial<Task>) => Promise<Task>;
  updateStatus: (taskId: string, status: string, actualHours?: number, qualityScore?: number) => Promise<Task>;
  remove: (taskId: string) => Promise<void>;
  assign: (taskId: string, employeeIds: string[]) => Promise<Task>;
  addComment: (taskId: string, comment: string) => Promise<Task>;
  saving: boolean;
  updating: boolean;
  deleting: boolean;
  assigning: boolean;
  commenting: boolean;
  error: string | null;
}

/**
 * Hook for task mutations (create, update, delete, etc.)
 */
export function useTaskMutation(): UseTaskMutationReturn {
  const [saving, setSaving] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [assigning, setAssigning] = useState(false);
  const [commenting, setCommenting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const save = useCallback(async (task: Partial<Task>): Promise<Task> => {
    try {
      setSaving(true);
      setError(null);

      const response = await taskApi.saveTask(task);

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to save task');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error saving task:', err);
      throw err;
    } finally {
      setSaving(false);
    }
  }, []);

  const updateStatus = useCallback(async (
    taskId: string,
    status: string,
    actualHours?: number,
    qualityScore?: number
  ): Promise<Task> => {
    try {
      setUpdating(true);
      setError(null);

      const response = await taskApi.updateTaskStatus(taskId, status, actualHours, qualityScore);

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update task status');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error updating task status:', err);
      throw err;
    } finally {
      setUpdating(false);
    }
  }, []);

  const remove = useCallback(async (taskId: string): Promise<void> => {
    try {
      setDeleting(true);
      setError(null);

      const response = await taskApi.deleteTask(taskId);

      if (!response.success) {
        throw new Error(response.message || 'Failed to delete task');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error deleting task:', err);
      throw err;
    } finally {
      setDeleting(false);
    }
  }, []);

  const assign = useCallback(async (taskId: string, employeeIds: string[]): Promise<Task> => {
    try {
      setAssigning(true);
      setError(null);

      const response = await taskApi.assignTask(taskId, employeeIds);

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to assign task');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error assigning task:', err);
      throw err;
    } finally {
      setAssigning(false);
    }
  }, []);

  const addComment = useCallback(async (taskId: string, comment: string): Promise<Task> => {
    try {
      setCommenting(true);
      setError(null);

      const response = await taskApi.addTaskComment(taskId, comment);

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to add comment');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error adding comment:', err);
      throw err;
    } finally {
      setCommenting(false);
    }
  }, []);

  return {
    save,
    updateStatus,
    remove,
    assign,
    addComment,
    saving,
    updating,
    deleting,
    assigning,
    commenting,
    error,
  };
}

import { connectToDatabase } from '@/lib/backend/database';
import Leave from '@/models/leave/Leave';
import LeaveType from '@/models/leave/LeaveType';
import Employee from '@/models/Employee';
import User from '@/models/User';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import mongoose from 'mongoose';

interface SampleLeaveData {
  employeeId: mongoose.Types.ObjectId;
  leaveTypeId: mongoose.Types.ObjectId;
  startDate: Date;
  endDate: Date;
  duration: number;
  reason: string;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  createdBy: mongoose.Types.ObjectId;
  approvedBy?: mongoose.Types.ObjectId;
  approvalDate?: Date;
}

/**
 * Generate sample leave data for testing reports
 */
export async function seedSampleLeaveData(): Promise<{ created: number; skipped: number; total: number }> {
  try {
    // Connect to database
    await connectToDatabase();

    // Check if leave records already exist
    const existingCount = await Leave.countDocuments();
    if (existingCount > 10) {
      logger.info(`Leave records already exist (${existingCount} found). Skipping sample data creation.`, LogCategory.SYSTEM);
      return { created: 0, skipped: existingCount, total: existingCount };
    }

    // Get required data
    const employees = await Employee.find({}).limit(10);
    const leaveTypes = await LeaveType.find({ isActive: true });
    const systemAdmin = await User.findOne({ role: { $in: ['super_admin', 'system_admin'] } });

    if (employees.length === 0) {
      throw new Error('No employees found. Please create employees first.');
    }

    if (leaveTypes.length === 0) {
      throw new Error('No leave types found. Please seed leave types first.');
    }

    if (!systemAdmin) {
      throw new Error('No system admin found. Please create a system admin user first.');
    }

    logger.info(`Found ${employees.length} employees and ${leaveTypes.length} leave types`, LogCategory.SYSTEM);

    const sampleLeaves: SampleLeaveData[] = [];
    let leaveIdCounter = 1;

    // Generate sample leave data for the past 12 months
    const now = new Date();
    const startOfYear = new Date(now.getFullYear(), 0, 1);
    const endOfYear = new Date(now.getFullYear(), 11, 31);

    // Create various leave scenarios
    for (let i = 0; i < employees.length; i++) {
      const employee = employees[i];
      
      // Create 2-4 leave requests per employee
      const leaveCount = Math.floor(Math.random() * 3) + 2;
      
      for (let j = 0; j < leaveCount; j++) {
        const leaveType = leaveTypes[Math.floor(Math.random() * leaveTypes.length)];
        
        // Generate random dates within the year
        const startDate = new Date(
          startOfYear.getTime() + Math.random() * (endOfYear.getTime() - startOfYear.getTime())
        );
        
        // Duration between 1-10 days
        const duration = Math.floor(Math.random() * 10) + 1;
        const endDate = new Date(startDate.getTime() + (duration - 1) * 24 * 60 * 60 * 1000);
        
        // Random status with higher probability for approved
        const statusRandom = Math.random();
        let status: 'pending' | 'approved' | 'rejected' | 'cancelled';
        if (statusRandom < 0.7) status = 'approved';
        else if (statusRandom < 0.85) status = 'pending';
        else if (statusRandom < 0.95) status = 'rejected';
        else status = 'cancelled';

        const leaveData: SampleLeaveData = {
          employeeId: employee._id,
          leaveTypeId: leaveType._id,
          startDate,
          endDate,
          duration,
          reason: `Sample ${leaveType.name.toLowerCase()} request for testing`,
          status,
          createdBy: systemAdmin._id
        };

        // Add approval data for approved/rejected leaves
        if (status === 'approved' || status === 'rejected') {
          leaveData.approvedBy = systemAdmin._id;
          leaveData.approvalDate = new Date(startDate.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000);
        }

        sampleLeaves.push(leaveData);
      }
    }

    // Create leave records
    let createdCount = 0;
    let skippedCount = 0;

    for (const leaveData of sampleLeaves) {
      try {
        // Generate unique leave ID
        const leaveId = `LV${new Date().getFullYear()}${String(leaveIdCounter).padStart(4, '0')}`;
        leaveIdCounter++;

        const leave = new Leave({
          leaveId,
          ...leaveData
        });

        await leave.save();
        createdCount++;

        logger.info(`Created sample leave: ${leaveId} for employee ${leaveData.employeeId}`, LogCategory.SYSTEM);
      } catch (error) {
        logger.error(`Error creating sample leave`, LogCategory.SYSTEM, error);
        skippedCount++;
      }
    }

    const totalCount = createdCount + skippedCount;
    
    logger.info(`Sample leave data seeding completed`, LogCategory.SYSTEM, {
      created: createdCount,
      skipped: skippedCount,
      total: totalCount
    });

    return { created: createdCount, skipped: skippedCount, total: totalCount };

  } catch (error) {
    logger.error('Error seeding sample leave data', LogCategory.SYSTEM, error);
    throw error;
  }
}

/**
 * Remove all sample leave data
 */
export async function clearSampleLeaveData(): Promise<number> {
  try {
    await connectToDatabase();
    
    const result = await Leave.deleteMany({
      reason: { $regex: /^Sample .* request for testing$/ }
    });

    logger.info(`Cleared ${result.deletedCount} sample leave records`, LogCategory.SYSTEM);
    return result.deletedCount;
  } catch (error) {
    logger.error('Error clearing sample leave data', LogCategory.SYSTEM, error);
    throw error;
  }
}

import mongoose from 'mongoose';
import UserSession, { IUserSession } from '@/models/UserSession';
import User, { IUser } from '@/models/User';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { generateToken, verifyToken } from '@/lib/backend/auth/token';
import crypto from 'crypto';

/**
 * Service for managing user sessions
 */
export class SessionService {
  /**
   * Create a new user session
   */
  async createSession(
    user: IUser,
    deviceInfo: {
      deviceId?: string;
      deviceName: string;
      deviceType: string;
      browser: string;
      operatingSystem: string;
      ipAddress: string;
      macAddress?: string;
      location?: {
        country?: string;
        region?: string;
        city?: string;
        latitude?: number;
        longitude?: number;
      };
    }
  ): Promise<IUserSession | { error: string; activeSession?: IUserSession }> {
    try {
      logger.info('Creating new user session', LogCategory.AUTH, {
        userId: user._id.toString(),
        deviceName: deviceInfo.deviceName,
        ipAddress: deviceInfo.ipAddress
      });

      await connectToDatabase();

      // Generate a unique device ID if not provided
      const deviceId = deviceInfo.deviceId || this.generateDeviceId(
        deviceInfo.ipAddress,
        deviceInfo.deviceName,
        deviceInfo.browser,
        deviceInfo.operatingSystem,
        deviceInfo.macAddress
      );

      // Check if there's an existing active session for this device
      const existingSession = await UserSession.findOne({
        userId: user._id,
        deviceId,
        isActive: true,
        expiresAt: { $gt: new Date() }
      });

      if (existingSession) {
        logger.info('Found existing session for device, updating it', LogCategory.AUTH, {
          userId: user._id.toString(),
          deviceId,
          sessionId: existingSession._id.toString()
        });

        // Update the existing session
        existingSession.lastActive = new Date();
        existingSession.ipAddress = deviceInfo.ipAddress;
        existingSession.location = deviceInfo.location;

        // Set expiration date (7 days from now by default)
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 7);
        existingSession.expiresAt = expiresAt;

        await existingSession.save();
        return existingSession;
      }

      // Check if user already has active sessions
      const activeSessions = await UserSession.findActiveSessionsByUser(user._id.toString());

      // Check if single device login is enforced and user already has an active session on another device
      if (user.singleDeviceLogin && activeSessions.length > 0) {
        const activeSession = activeSessions[0];

        logger.warn('Single device login enforced: User already has an active session on another device', LogCategory.AUTH, {
          userId: user._id.toString(),
          currentDeviceId: deviceId,
          activeSessionDeviceId: activeSession.deviceId,
          activeSessionId: activeSession._id.toString()
        });

        return {
          error: 'SINGLE_DEVICE_LOGIN_ENFORCED',
          activeSession: activeSession
        };
      }

      // Generate token
      const token = generateToken(user._id.toString(), user.role);

      // Set expiration date (7 days from now by default)
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7);

      // Create new session
      const session = new UserSession({
        userId: user._id,
        deviceId,
        deviceName: deviceInfo.deviceName,
        deviceType: deviceInfo.deviceType,
        browser: deviceInfo.browser,
        operatingSystem: deviceInfo.operatingSystem,
        ipAddress: deviceInfo.ipAddress,
        macAddress: deviceInfo.macAddress,
        location: deviceInfo.location,
        isTrusted: activeSessions.length === 0, // First device is automatically trusted
        isActive: true,
        lastActive: new Date(),
        loginTime: new Date(),
        expiresAt,
        token
      });

      await session.save();

      logger.info('User session created successfully', LogCategory.AUTH, {
        userId: user._id.toString(),
        deviceId,
        sessionId: session._id.toString(),
        isTrusted: session.isTrusted
      });

      return session;
    } catch (error: any) {
      logger.error('Error creating user session', LogCategory.AUTH, error instanceof Error ? error : new Error(error?.message || 'Unknown error'), {
        userId: user._id.toString(),
        deviceName: deviceInfo.deviceName,
        ipAddress: deviceInfo.ipAddress
      });
      throw error;
    }
  }

  /**
   * Get active session by token
   */
  async getSessionByToken(token: string): Promise<IUserSession | null> {
    try {
      logger.debug('Getting session by token', LogCategory.AUTH);

      await connectToDatabase();

      const session = await UserSession.findByToken(token);

      if (!session) {
        logger.debug('No active session found for token', LogCategory.AUTH);
        return null;
      }

      logger.debug('Found active session for token', LogCategory.AUTH, {
        userId: session.userId.toString(),
        deviceId: session.deviceId,
        sessionId: session._id.toString()
      });

      return session;
    } catch (error: any) {
      logger.error('Error getting session by token', LogCategory.AUTH, error instanceof Error ? error : new Error(error?.message || 'Unknown error'));
      return null;
    }
  }

  // Throttle map to prevent excessive session updates
  private sessionUpdateThrottle = new Map<string, number>();
  private readonly SESSION_UPDATE_INTERVAL = 60000; // 1 minute

  /**
   * Update session last active time (throttled)
   */
  async updateSessionActivity(sessionId: string): Promise<void> {
    try {
      // Check if we've updated this session recently
      const lastUpdate = this.sessionUpdateThrottle.get(sessionId);
      const now = Date.now();

      if (lastUpdate && (now - lastUpdate) < this.SESSION_UPDATE_INTERVAL) {
        // Skip update if we've updated within the last minute
        return;
      }

      logger.debug('Updating session activity', LogCategory.AUTH, { sessionId });

      await connectToDatabase();

      await UserSession.findByIdAndUpdate(
        sessionId,
        { lastActive: new Date() }
      );

      // Update throttle map
      this.sessionUpdateThrottle.set(sessionId, now);

      logger.debug('Session activity updated', LogCategory.AUTH, { sessionId });
    } catch (error: any) {
      logger.error('Error updating session activity', LogCategory.AUTH, error instanceof Error ? error : new Error(error?.message || 'Unknown error'), { sessionId });
    }
  }

  /**
   * End a user session
   */
  async endSession(token: string): Promise<boolean> {
    try {
      logger.info('Ending user session', LogCategory.AUTH);

      await connectToDatabase();

      const session = await UserSession.findOne({ token, isActive: true });

      if (!session) {
        logger.warn('No active session found to end', LogCategory.AUTH);
        return false;
      }

      session.isActive = false;
      session.logoutTime = new Date();
      await session.save();

      logger.info('User session ended successfully', LogCategory.AUTH, {
        userId: session.userId.toString(),
        deviceId: session.deviceId,
        sessionId: session._id.toString()
      });

      return true;
    } catch (error: any) {
      logger.error('Error ending user session', LogCategory.AUTH, error instanceof Error ? error : new Error(error?.message || 'Unknown error'));
      return false;
    }
  }

  /**
   * End all sessions for a user
   */
  async endAllUserSessions(userId: string, exceptSessionId?: string): Promise<number> {
    try {
      logger.info('Ending all user sessions', LogCategory.AUTH, { userId, exceptSessionId });

      await connectToDatabase();

      const query: any = {
        userId: new mongoose.Types.ObjectId(userId),
        isActive: true
      };

      // Exclude the current session if specified
      if (exceptSessionId) {
        query._id = { $ne: new mongoose.Types.ObjectId(exceptSessionId) };
      }

      const result = await UserSession.updateMany(
        query,
        {
          isActive: false,
          logoutTime: new Date()
        }
      );

      logger.info(`Ended ${result.modifiedCount} user sessions`, LogCategory.AUTH, { userId });

      return result.modifiedCount;
    } catch (error: any) {
      logger.error('Error ending all user sessions', LogCategory.AUTH, error instanceof Error ? error : new Error(error?.message || 'Unknown error'), { userId });
      return 0;
    }
  }

  /**
   * Get all active sessions for a user
   */
  async getUserActiveSessions(userId: string): Promise<IUserSession[]> {
    try {
      logger.debug('Getting active sessions for user', LogCategory.AUTH, { userId });

      await connectToDatabase();

      const sessions = await UserSession.findActiveSessionsByUser(userId);

      logger.debug(`Found ${sessions.length} active sessions for user`, LogCategory.AUTH, { userId });

      return sessions;
    } catch (error: any) {
      logger.error('Error getting user active sessions', LogCategory.AUTH, error instanceof Error ? error : new Error(error?.message || 'Unknown error'), { userId });
      return [];
    }
  }

  /**
   * Get all active sessions (for admin)
   */
  async getAllActiveSessions(limit: number = 100, skip: number = 0): Promise<{
    sessions: IUserSession[];
    total: number;
  }> {
    try {
      logger.debug('Getting all active sessions', LogCategory.AUTH, { limit, skip });

      await connectToDatabase();

      const query = {
        isActive: true,
        expiresAt: { $gt: new Date() }
      };

      const [sessions, total] = await Promise.all([
        UserSession.find(query)
          .sort({ lastActive: -1 })
          .skip(skip)
          .limit(limit)
          .populate('userId', 'firstName lastName email role'),
        UserSession.countDocuments(query)
      ]);

      logger.debug(`Found ${total} active sessions`, LogCategory.AUTH);

      return { sessions, total };
    } catch (error: any) {
      logger.error('Error getting all active sessions', LogCategory.AUTH, error instanceof Error ? error : new Error(error?.message || 'Unknown error'));
      return { sessions: [], total: 0 };
    }
  }

  /**
   * Trust a device
   */
  async trustDevice(sessionId: string): Promise<boolean> {
    try {
      logger.info('Trusting device', LogCategory.AUTH, { sessionId });

      await connectToDatabase();

      const session = await UserSession.findById(sessionId);

      if (!session) {
        logger.warn('Session not found for trusting device', LogCategory.AUTH, { sessionId });
        return false;
      }

      session.isTrusted = true;
      await session.save();

      logger.info('Device trusted successfully', LogCategory.AUTH, {
        userId: session.userId.toString(),
        deviceId: session.deviceId,
        sessionId: session._id.toString()
      });

      return true;
    } catch (error: any) {
      logger.error('Error trusting device', LogCategory.AUTH, error instanceof Error ? error : new Error(error?.message || 'Unknown error'), { sessionId });
      return false;
    }
  }

  /**
   * Generate a unique device ID based on device information
   */
  generateDeviceId(
    ipAddress: string,
    deviceName: string,
    browser: string,
    operatingSystem: string,
    macAddress?: string
  ): string {
    // Create a string with all the device information
    const deviceInfo = `${ipAddress}|${deviceName}|${browser}|${operatingSystem}|${macAddress || ''}`;

    // Generate a hash of the device information
    return crypto
      .createHash('sha256')
      .update(deviceInfo)
      .digest('hex');
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    try {
      logger.info('Cleaning up expired sessions', LogCategory.AUTH);

      await connectToDatabase();

      const result = await UserSession.updateMany(
        {
          isActive: true,
          expiresAt: { $lte: new Date() }
        },
        {
          isActive: false,
          logoutTime: new Date()
        }
      );

      logger.info(`Cleaned up ${result.modifiedCount} expired sessions`, LogCategory.AUTH);

      return result.modifiedCount;
    } catch (error: any) {
      logger.error('Error cleaning up expired sessions', LogCategory.AUTH, error instanceof Error ? error : new Error(error?.message || 'Unknown error'));
      return 0;
    }
  }
}

// Export a singleton instance
export const sessionService = new SessionService();

// lib/backend/auth/auth.ts
import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import User, { IUser } from '@/models/User';
import { UserRole, UserStatus } from '@/types/user-roles';
import { connectToDatabase } from '@/lib/backend/database';
import { generateToken, verifyToken, JwtPayload } from './token';
import crypto from 'crypto';
import logger, { LogCategory } from '../utils/logger';
import { sessionService } from '../services/auth/SessionService';
import UserSession, { IUserSession } from '@/models/UserSession';
import { blockingService } from '../services/auth/BlockingService';
import { DeviceDetector } from '../utils/device-detector';
import mongoose from 'mongoose';

// Environment variables (should be in .env file)
const JWT_COOKIE_EXPIRES_IN = process.env.JWT_COOKIE_EXPIRES_IN || '7';

// Export authOptions for compatibility with next-auth
export const authOptions = {
  // This is a placeholder to maintain compatibility with code that expects authOptions
  // The actual authentication is handled by the custom auth system
  session: {
    strategy: 'jwt',
    maxAge: parseInt(JWT_COOKIE_EXPIRES_IN) * 24 * 60 * 60, // days to seconds
  },
  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key',
};

/**
 * Generate JWT token for a user
 */
export const generateUserToken = (user: IUser): string => {
  // Ensure user._id is a valid ObjectId
  if (!user._id) {
    throw new Error('User ID is required to generate token');
  }
  return generateToken(user._id.toString(), user.role);
};

/**
 * Set token cookie in response
 */
export const setTokenCookie = (response: NextResponse, token: string): void => {
  // Calculate expiry date
  const expiresIn = parseInt(JWT_COOKIE_EXPIRES_IN) * 24 * 60 * 60 * 1000; // days to ms
  const expiryDate = new Date(Date.now() + expiresIn);

  // Set cookie
  response.cookies.set({
    name: 'token',
    value: token,
    expires: expiryDate,
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/'
  });
};

/**
 * Clear token cookie in response
 */
export const clearTokenCookie = (response: NextResponse): void => {
  response.cookies.set({
    name: 'token',
    value: '',
    expires: new Date(0),
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/'
  });
};

// Re-export verifyToken for convenience
export { verifyToken };

/**
 * Get current user from token
 */
export const getCurrentUser = async (req: NextRequest): Promise<IUser | null> => {
  const requestPath = req.nextUrl?.pathname || 'unknown path';
  logger.debug('Attempting to get current user', LogCategory.AUTH, { path: requestPath });

  try {
    // Connect to the database
    await connectToDatabase();

    // Get token from cookies
    const token = req.cookies.get('token')?.value;
    if (!token) {
      logger.debug('No authentication token found in request', LogCategory.AUTH, { path: requestPath });
      return null;
    }

    // First check if there's a valid session for this token
    const session = await sessionService.getSessionByToken(token);
    if (!session) {
      logger.warn('No active session found for token', LogCategory.AUTH, { path: requestPath });
      return null;
    }

    // Verify token
    logger.debug('Verifying authentication token', LogCategory.AUTH);
    const decoded = verifyToken(token);
    if (!decoded) {
      logger.warn('Invalid or expired token - JWT verification failed', LogCategory.AUTH, {
        path: requestPath,
        tokenLength: token.length,
        sessionExists: !!session
      });

      // If JWT verification fails but session exists, it means the token was signed with a different secret
      // End the session to clean up the database
      if (session) {
        logger.info('Ending session due to JWT verification failure', LogCategory.AUTH, {
          sessionId: session._id.toString(),
          userId: session.userId.toString()
        });
        await sessionService.endSession(token);
      }

      return null;
    }

    // Find user by id
    logger.debug('Finding user by ID from token', LogCategory.AUTH, { userId: decoded.id });
    const user = await User.findById(decoded.id).select('+password');

    if (!user) {
      logger.warn('User from token not found in database', LogCategory.AUTH, {
        userId: decoded.id,
        path: requestPath
      });
      return null;
    }

    if (user.status !== UserStatus.ACTIVE) {
      logger.warn('User account not active', LogCategory.AUTH, {
        userId: (user._id as mongoose.Types.ObjectId).toString(),
        status: user.status,
        path: requestPath
      });
      return null;
    }

    // Update session last active time
    await sessionService.updateSessionActivity((session._id as mongoose.Types.ObjectId).toString());

    logger.debug('Current user retrieved successfully', LogCategory.AUTH, {
      userId: (user._id as mongoose.Types.ObjectId).toString(),
      email: user.email,
      role: user.role,
      path: requestPath
    });

    return user;
  } catch (error: any) {
    logger.error('Error getting current user', LogCategory.AUTH, error instanceof Error ? error : new Error(error?.message || 'Unknown error'), { path: requestPath });
    return null;
  }
};

/**
 * Check if user has a specific role
 */
export const hasRole = (user: IUser, roles: UserRole | UserRole[]): boolean => {
  if (!user) return false;

  // Convert single role to array
  const rolesToCheck = Array.isArray(roles) ? roles : [roles];

  // Check if user's role is in the roles array
  return rolesToCheck.includes(user.role);
};

/**
 * Register a new user
 */
export const registerUser = async (
  email: string,
  password: string,
  firstName: string,
  lastName: string,
  role: UserRole = UserRole.EMPLOYEE,
  status: UserStatus = UserStatus.ACTIVE
): Promise<IUser> => {
  logger.info('User registration attempt', LogCategory.AUTH, {
    email,
    firstName,
    lastName,
    role,
    status
  });

  try {
    // Connect to the database
    await connectToDatabase();

    // Check if user already exists
    logger.debug('Checking if user already exists', LogCategory.AUTH, { email });
    const existingUser = await User.findByEmail(email);

    if (existingUser) {
      logger.warn('Registration failed: Email already in use', LogCategory.AUTH, {
        email,
        existingUserId: (existingUser._id as mongoose.Types.ObjectId).toString()
      });
      throw new Error('User with this email already exists');
    }

    // Create new user
    logger.debug('Creating new user', LogCategory.AUTH, {
      email,
      firstName,
      lastName,
      role,
      status
    });

    const user = await User.create({
      email,
      password,
      firstName,
      lastName,
      role,
      status
    });

    logger.info('User registered successfully', LogCategory.AUTH, {
      userId: (user._id as mongoose.Types.ObjectId).toString(),
      email: user.email,
      role: user.role,
      status: user.status
    });

    return user;
  } catch (error: any) {
    logger.error('Registration error', LogCategory.AUTH, error instanceof Error ? error : new Error(error?.message || 'Unknown error'), {
      email,
      firstName,
      lastName
    });
    throw error;
  }
};

/**
 * Login user
 */
export const loginUser = async (
  email: string,
  password: string,
  req?: NextRequest
): Promise<{ user: IUser; token: string; requiresDeviceVerification: boolean; sessionId: string }> => {
  logger.info('Login attempt', LogCategory.AUTH, { email });

  try {
    // Connect to the database
    await connectToDatabase();

    // Check if user exists
    logger.debug('Finding user by email', LogCategory.AUTH, { email });
    const user = await User.findByEmail(email).select('+password');

    if (!user) {
      logger.warn('Login failed: User not found', LogCategory.AUTH, { email });
      throw new Error('Invalid email or password');
    }

    // Check if user is active
    if (user.status !== UserStatus.ACTIVE) {
      logger.warn('Login failed: User account not active', LogCategory.AUTH, {
        email,
        userId: (user._id as mongoose.Types.ObjectId).toString(),
        status: user.status
      });
      throw new Error('Your account is not active. Please contact an administrator.');
    }

    // Check if password is correct
    logger.debug('Verifying password', LogCategory.AUTH, { userId: (user._id as mongoose.Types.ObjectId).toString() });
    const isPasswordCorrect = await user.comparePassword(password);

    if (!isPasswordCorrect) {
      logger.warn('Login failed: Incorrect password', LogCategory.AUTH, {
        email,
        userId: (user._id as mongoose.Types.ObjectId).toString()
      });
      throw new Error('Invalid email or password');
    }

    // Update last login
    logger.debug('Updating last login timestamp', LogCategory.AUTH, { userId: (user._id as mongoose.Types.ObjectId).toString() });
    user.lastLogin = new Date();
    await user.save({ validateBeforeSave: false });

    // Get device information if request is provided
    let deviceInfo;
    if (req) {
      deviceInfo = DeviceDetector.detectDevice(req);
    } else {
      // Default device info if request is not provided
      deviceInfo = {
        deviceName: 'Unknown Device',
        deviceType: 'unknown',
        browser: 'Unknown Browser',
        browserVersion: '',
        operatingSystem: 'Unknown OS',
        osVersion: '',
        ipAddress: '0.0.0.0'
      };
    }

    // Check if IP is blocked
    if (deviceInfo.ipAddress) {
      logger.debug('Checking if IP address is blocked', LogCategory.AUTH, {
        ipAddress: deviceInfo.ipAddress,
        userId: (user._id as mongoose.Types.ObjectId).toString()
      });

      const { isBlocked: isIpBlocked, entity: ipBlockEntity } = await blockingService.isIpBlocked(deviceInfo.ipAddress);

      if (isIpBlocked) {
        logger.warn('Login failed: IP address is blocked', LogCategory.AUTH, {
          email,
          userId: (user._id as mongoose.Types.ObjectId).toString(),
          ipAddress: deviceInfo.ipAddress,
          blockId: ipBlockEntity?.id ? ipBlockEntity.id.toString() : undefined,
          blockReason: ipBlockEntity?.reason
        });

        throw new Error('Access denied: Your IP address has been blocked. Please contact an administrator.');
      }
    }

    // Check if country is blocked (if location information is available)
    if (deviceInfo.location && deviceInfo.location.country) {
      logger.debug('Checking if country is blocked', LogCategory.AUTH, {
        country: deviceInfo.location.country,
        userId: (user._id as mongoose.Types.ObjectId).toString()
      });

      const { isBlocked: isCountryBlocked, entity: countryBlockEntity } =
        await blockingService.isCountryBlocked(deviceInfo.location.country);

      if (isCountryBlocked) {
        logger.warn('Login failed: Country is blocked', LogCategory.AUTH, {
          email,
          userId: (user._id as mongoose.Types.ObjectId).toString(),
          country: deviceInfo.location.country,
          blockId: countryBlockEntity?.id ? countryBlockEntity.id.toString() : undefined,
          blockReason: countryBlockEntity?.reason
        });

        throw new Error('Access denied: Logins from your country are not permitted. Please contact an administrator.');
      }
    }

    // Generate device ID for checking if device is blocked
    const deviceId = sessionService.generateDeviceId(
      deviceInfo.ipAddress,
      deviceInfo.deviceName,
      `${deviceInfo.browser} ${deviceInfo.browserVersion}`.trim(),
      `${deviceInfo.operatingSystem} ${deviceInfo.osVersion}`.trim()
    );

    // Check if device is blocked
    if (deviceId) {
      logger.debug('Checking if device is blocked', LogCategory.AUTH, {
        deviceId,
        userId: (user._id as mongoose.Types.ObjectId).toString()
      });

      const { isBlocked: isDeviceBlocked, entity: deviceBlockEntity } =
        await blockingService.isDeviceBlocked(deviceId);

      if (isDeviceBlocked) {
        logger.warn('Login failed: Device is blocked', LogCategory.AUTH, {
          email,
          userId: (user._id as mongoose.Types.ObjectId).toString(),
          deviceId,
          blockId: deviceBlockEntity?.id ? deviceBlockEntity.id.toString() : undefined,
          blockReason: deviceBlockEntity?.reason
        });

        throw new Error('Access denied: This device has been blocked. Please contact an administrator.');
      }
    }

    // Create or update session
    const sessionResult = await sessionService.createSession(user, {
      deviceId,
      deviceName: deviceInfo.deviceName,
      deviceType: deviceInfo.deviceType,
      browser: `${deviceInfo.browser} ${deviceInfo.browserVersion}`.trim(),
      operatingSystem: `${deviceInfo.operatingSystem} ${deviceInfo.osVersion}`.trim(),
      ipAddress: deviceInfo.ipAddress,
      location: deviceInfo.location
    });

    // Check if single device login is enforced and user already has an active session
    if ('error' in sessionResult && sessionResult.error === 'SINGLE_DEVICE_LOGIN_ENFORCED') {
      logger.warn('Login failed: Single device login enforced', LogCategory.AUTH, {
        email,
        userId: (user._id as mongoose.Types.ObjectId).toString(),
        deviceId,
        activeSessionDeviceId: sessionResult.activeSession?.deviceId
      });

      throw new Error(
        'This account is already logged in on another device. ' +
        'Please log out from the other device before logging in here.'
      );
    }

    // If we get here, sessionResult is an IUserSession
    const session = sessionResult as IUserSession;

    // Check if this is a new device that requires verification
    const requiresDeviceVerification = !session.isTrusted;

    // Remove password from response
    user.password = undefined as any;

    logger.info('Login successful', LogCategory.AUTH, {
      userId: (user._id as mongoose.Types.ObjectId).toString(),
      email: user.email,
      role: user.role,
      lastLogin: user.lastLogin,
      deviceName: deviceInfo.deviceName,
      requiresDeviceVerification
    });

    // Ensure session._id is a valid ObjectId
    const sessionId = session._id ? session._id.toString() : '';

    return {
      user,
      token: session.token,
      requiresDeviceVerification,
      sessionId
    };
  } catch (error: any) {
    logger.error('Login error', LogCategory.AUTH, error instanceof Error ? error : new Error(error?.message || 'Unknown error'), { email });
    throw error;
  }
};

/**
 * Create password reset token
 */
export const createPasswordResetToken = async (email: string): Promise<string> => {
  try {
    // Connect to the database
    await connectToDatabase();

    // Find user by email
    const user = await User.findByEmail(email);
    if (!user) {
      throw new Error('User with this email does not exist');
    }

    // Generate reset token
    const resetToken = user.createPasswordResetToken();
    await user.save({ validateBeforeSave: false });

    return resetToken;
  } catch (error: any) {
    logger.error('Error creating password reset token', LogCategory.AUTH, error instanceof Error ? error : new Error(error?.message || 'Unknown error'), { email });
    throw error;
  }
};

/**
 * Reset password
 */
export const resetPassword = async (token: string, newPassword: string): Promise<void> => {
  try {
    // Connect to the database
    await connectToDatabase();

    // Hash token
    const hashedToken = crypto
      .createHash('sha256')
      .update(token)
      .digest('hex');

    // Find user by token
    const user = await User.findOne({
      passwordResetToken: hashedToken,
      passwordResetExpires: { $gt: Date.now() }
    });

    if (!user) {
      throw new Error('Token is invalid or has expired');
    }

    // Update password
    user.password = newPassword;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    await user.save();
  } catch (error: any) {
    logger.error('Error resetting password', LogCategory.AUTH, error instanceof Error ? error : new Error(error?.message || 'Unknown error'), { token: '***' });
    throw error;
  }
};

/**
 * Update user status
 */
export const updateUserStatus = async (userId: string, status: UserStatus): Promise<IUser> => {
  try {
    // Connect to the database
    await connectToDatabase();

    // Find user by id
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Update status
    user.status = status;
    await user.save({ validateBeforeSave: false });

    return user;
  } catch (error: any) {
    logger.error('Error updating user status', LogCategory.AUTH, error instanceof Error ? error : new Error(error?.message || 'Unknown error'), { userId, status });
    throw error;
  }
};

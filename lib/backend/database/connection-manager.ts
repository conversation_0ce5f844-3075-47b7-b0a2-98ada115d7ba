// lib/backend/database/connection-manager.ts
import mongoose from 'mongoose';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Default connection options
const defaultConnectionOptions = {
  serverSelectionTimeoutMS: 30000, // 30 seconds
  socketTimeoutMS: 45000, // 45 seconds
  connectTimeoutMS: 30000, // 30 seconds
  maxPoolSize: 20, // Increased from 10 to handle more connections
  minPoolSize: 5,
  retryWrites: true,
  retryReads: true,
  maxIdleTimeMS: 60000, // 1 minute
};

/**
 * Singleton class to manage MongoDB connections
 */
class DatabaseConnectionManager {
  private static instance: DatabaseConnectionManager;
  private isConnected: boolean = false;
  private connectionPromise: Promise<typeof mongoose> | null = null;
  private connectionAttempts: number = 0;
  private readonly MAX_CONNECTION_ATTEMPTS = 3;
  private readonly CONNECTION_RETRY_DELAY = 5000; // 5 seconds

  private constructor() {
    // Set up connection event handlers (only once)
    mongoose.connection.setMaxListeners(30); // Increase max listeners to prevent warnings

    // Remove any existing listeners to prevent duplicates
    mongoose.connection.removeAllListeners('error');
    mongoose.connection.removeAllListeners('disconnected');
    mongoose.connection.removeAllListeners('reconnected');

    // Handle connection events
    mongoose.connection.on('error', (error) => {
      this.isConnected = false;
      logger.error('MongoDB connection error', LogCategory.DATABASE, error);
    });

    mongoose.connection.on('disconnected', () => {
      this.isConnected = false;
      logger.warn('MongoDB disconnected', LogCategory.DATABASE);
    });

    mongoose.connection.on('reconnected', () => {
      this.isConnected = true;
      logger.info('MongoDB reconnected', LogCategory.DATABASE);
    });

    // Handle process termination - only add this listener once
    const sigintListeners = process.listeners('SIGINT').length;
    if (sigintListeners === 0) {
      process.on('SIGINT', async () => {
        await this.disconnect();
        process.exit(0);
      });
    }
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): DatabaseConnectionManager {
    if (!DatabaseConnectionManager.instance) {
      DatabaseConnectionManager.instance = new DatabaseConnectionManager();
    }
    return DatabaseConnectionManager.instance;
  }

  /**
   * Connect to MongoDB database
   * @param options - MongoDB connection options
   * @returns Mongoose connection
   */
  public async connect(options = {}): Promise<typeof mongoose> {
    // If already connected and connection is ready, return the existing connection
    if (this.isConnected && mongoose.connection.readyState === 1) {
      logger.debug('Using existing MongoDB connection', LogCategory.DATABASE);
      return mongoose;
    }

    // If connection is in connecting state, wait for it to complete
    if (mongoose.connection.readyState === 2) {
      logger.debug('MongoDB connection is in progress, waiting...', LogCategory.DATABASE);
      return new Promise((resolve, reject) => {
        const onConnected = () => {
          mongoose.connection.removeListener('error', onError);
          this.isConnected = true;
          resolve(mongoose);
        };

        const onError = (err: Error) => {
          mongoose.connection.removeListener('connected', onConnected);
          reject(err);
        };

        mongoose.connection.once('connected', onConnected);
        mongoose.connection.once('error', onError);
      });
    }

    // If a connection attempt is already in progress, return that promise
    if (this.connectionPromise) {
      logger.debug('Connection attempt already in progress, reusing promise', LogCategory.DATABASE);
      return this.connectionPromise;
    }

    // Reset connection attempts if this is a new connection attempt
    if (mongoose.connection.readyState === 0) {
      this.connectionAttempts = 0;
    }

    // Create a new connection promise
    this.connectionPromise = this.createConnection(options);

    try {
      const result = await this.connectionPromise;
      return result;
    } finally {
      // Clear the connection promise after it resolves or rejects
      this.connectionPromise = null;
    }
  }

  /**
   * Create a new MongoDB connection
   * @param options - MongoDB connection options
   * @returns Mongoose connection
   */
  private async createConnection(options = {}): Promise<typeof mongoose> {
    try {
      // Get MongoDB URI from environment variables
      const uri = process.env.MONGODB_URI || 'mongodb+srv://winstonmhango23:<EMAIL>/?retryWrites=true&w=majority&appName=resources';

      if (!uri) {
        const error = new Error('MongoDB URI is not defined in environment variables');
        logger.error('MongoDB connection failed', LogCategory.DATABASE, error);
        throw error;
      }

      logger.info(`Connecting to MongoDB: ${uri}`, LogCategory.DATABASE);

      // Configure Mongoose
      mongoose.set('strictQuery', true);

      // Merge default options with provided options
      const connectionOptions = {
        ...defaultConnectionOptions,
        ...options
      };

      // If there's an existing connection that's disconnected, close it properly first
      if (mongoose.connection.readyState !== 0) {
        try {
          logger.debug('Closing existing MongoDB connection before creating a new one', LogCategory.DATABASE);
          await mongoose.connection.close();
        } catch (closeError) {
          logger.warn('Error closing existing MongoDB connection', LogCategory.DATABASE, closeError);
          // Continue anyway
        }
      }

      // Connect to MongoDB with retry logic
      while (this.connectionAttempts < this.MAX_CONNECTION_ATTEMPTS) {
        try {
          this.connectionAttempts++;
          logger.info(`Connecting to MongoDB (attempt ${this.connectionAttempts}/${this.MAX_CONNECTION_ATTEMPTS})...`, LogCategory.DATABASE);

          // Use a timeout promise to handle connection timeouts
          const connectPromise = mongoose.connect(uri, connectionOptions);
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Connection timeout')), connectionOptions.connectTimeoutMS);
          });

          // Race the connect promise against the timeout
          await Promise.race([connectPromise, timeoutPromise]);

          this.isConnected = true;
          logger.info('Connected to MongoDB', LogCategory.DATABASE, {
            host: mongoose.connection.host,
            database: mongoose.connection.name,
            readyState: mongoose.connection.readyState
          });

          return mongoose; // Connection successful
        } catch (connectionError: any) {
          logger.error(`MongoDB connection attempt ${this.connectionAttempts} failed`, LogCategory.DATABASE, connectionError);

          if (this.connectionAttempts >= this.MAX_CONNECTION_ATTEMPTS) {
            throw connectionError; // Rethrow the error after max attempts
          }

          // Wait before retrying
          logger.info(`Retrying in ${this.CONNECTION_RETRY_DELAY / 1000} seconds...`, LogCategory.DATABASE);
          await new Promise(resolve => setTimeout(resolve, this.CONNECTION_RETRY_DELAY));
        }
      }

      throw new Error('Failed to connect to MongoDB after all attempts');
    } catch (error: any) {
      this.isConnected = false;
      logger.error('MongoDB connection failed after all attempts', LogCategory.DATABASE, error);
      throw error;
    }
  }

  /**
   * Disconnect from MongoDB database
   */
  public async disconnect(): Promise<void> {
    if (!this.isConnected || mongoose.connection.readyState === 0) {
      logger.debug('No active MongoDB connection to disconnect', LogCategory.DATABASE);
      return;
    }

    try {
      // Clear any pending connection promise
      this.connectionPromise = null;

      // Close the connection
      await mongoose.connection.close();
      this.isConnected = false;
      logger.info('Disconnected from MongoDB', LogCategory.DATABASE);
    } catch (error: any) {
      logger.error('MongoDB disconnection failed', LogCategory.DATABASE, error);
      // Don't throw the error, just log it
      // This prevents cascading errors when shutting down
    } finally {
      // Ensure we reset the connection state
      this.isConnected = false;
    }
  }

  /**
   * Check if connected to MongoDB database
   * @returns True if connected, false otherwise
   */
  public isConnectedToDatabase(): boolean {
    // Check both our flag and the actual connection state
    return this.isConnected && mongoose.connection.readyState === 1;
  }

  /**
   * Get the current MongoDB connection status
   * @returns Connection status object
   */
  public getDatabaseConnectionStatus(): {
    isConnected: boolean;
    readyState: number;
    readyStateText: string;
    host?: string;
    database?: string;
  } {
    const readyState = mongoose.connection.readyState;

    // Map readyState to text
    let readyStateText: string;
    switch (readyState) {
      case 0:
        readyStateText = 'disconnected';
        break;
      case 1:
        readyStateText = 'connected';
        break;
      case 2:
        readyStateText = 'connecting';
        break;
      case 3:
        readyStateText = 'disconnecting';
        break;
      default:
        readyStateText = 'unknown';
    }

    return {
      isConnected: readyState === 1,
      readyState,
      readyStateText,
      host: mongoose.connection.host,
      database: mongoose.connection.name
    };
  }
}

export default DatabaseConnectionManager;

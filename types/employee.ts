// Define MongoDB ObjectId type
export interface MongoObjectId {
  $oid: string
}

// Define the Employee interface to match the backend model
export interface Employee {
  _id?: string | MongoObjectId
  employeeId?: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  dateOfBirth?: Date | string
  gender?: string
  maritalStatus?: 'single' | 'married' | 'divorced' | 'widowed'
  numberOfChildren?: number
  address?: string
  city?: string
  state?: string
  postalCode?: string
  country?: string
  village?: string
  traditionalAuthority?: string
  district?: string
  nationalId?: string
  departmentId?: string | MongoObjectId
  position?: string
  roleId?: string | MongoObjectId
  managerId?: string | MongoObjectId
  employmentType?: 'full-time' | 'part-time' | 'contract' | 'intern' | 'temporary' | 'volunteer'
  employmentSubType?: 'standard' | 'attendance-based' | 'task-based' | 'hybrid'
  employmentStatus?: 'active' | 'inactive' | 'on-leave' | 'terminated'
  hireDate?: Date | string | { $date: { $numberLong: string } }
  terminationDate?: Date | string | { $date: { $numberLong: string } }
  salary?: number | { $numberInt: string } // Deprecated - use EmployeeSalary model
  hourlyRate?: number | { $numberInt: string }
  salaryCalculationRules?: {
    attendanceRequired?: boolean
    taskCompletionRequired?: boolean
    minimumHours?: number
    overtimeMultiplier?: number
    performanceBonusEnabled?: boolean
    taskRateOverride?: number
  }
  bankName?: string
  bankAccountNumber?: string
  nextOfKin?: {
    name?: string
    relationship?: string
    phone?: string
    address?: string
  }
  emergencyContactName?: string
  emergencyContactPhone?: string
  emergencyContactRelationship?: string
  photo?: string
  notes?: string

  // Access control fields
  addedBy?: string | MongoObjectId | {
    _id: string | MongoObjectId
    firstName: string
    lastName: string
    email: string
  }
  lastModifiedBy?: string | MongoObjectId | {
    _id: string | MongoObjectId
    firstName: string
    lastName: string
    email: string
  }
  accessibleBy?: (string | MongoObjectId)[]

  // Security fields
  isBlocked?: boolean
  blockedReason?: string
  blockedBy?: string | MongoObjectId
  blockedAt?: Date | string

  createdAt?: Date | string | { $date: { $numberLong: string } }
  updatedAt?: Date | string | { $date: { $numberLong: string } }
  __v?: number | { $numberInt: string }
}

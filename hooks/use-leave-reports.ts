"use client"

import { useState, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

interface Department {
  id: string;
  name: string;
  code: string;
  employeeCount: number;
}

interface OverviewReport {
  overview: {
    totalLeaveDays: number;
    pendingRequests: number;
    approvalRate: number;
    avgResponseTime: number;
    previousPeriodComparison: {
      totalLeaveDaysChange: number;
      pendingRequestsChange: number;
      approvalRateChange: number;
      avgResponseTimeChange: number;
    };
  };
  leavesByType: Array<{ type: string; count: number; days: number; percentage: number }>;
  leavesByDepartment: Array<{ department: string; count: number; days: number; percentage: number }>;
  monthlyTrends: Array<{ month: string; count: number; days: number }>;
}

interface UseLeaveReportsReturn {
  loading: boolean;
  error: string | null;
  generateOverviewReport: (timeRange: string, departmentId?: string, format?: 'json' | 'csv' | 'excel' | 'pdf') => Promise<OverviewReport | null>;
  generateUtilizationReport: (startDate: Date, endDate: Date, format?: 'json' | 'csv') => Promise<any>;
  generateDepartmentReport: (year: number, format?: 'json' | 'csv') => Promise<any>;
  generateTrendReport: (period: 'monthly' | 'quarterly' | 'yearly', periodsBack?: number, format?: 'json' | 'csv') => Promise<any>;
  generateEmployeeReport: (employeeId: string, year: number, format?: 'json' | 'csv') => Promise<any>;
  getDepartments: () => Promise<Department[]>;
  exportReport: (reportType: string, params: any, format: 'csv' | 'excel' | 'pdf') => Promise<void>;
}

export function useLeaveReports(): UseLeaveReportsReturn {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateOverviewReport = useCallback(async (
    timeRange: string,
    departmentId?: string,
    format: 'json' | 'csv' | 'excel' | 'pdf' = 'json'
  ): Promise<OverviewReport | null> => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        timeRange,
        format
      });

      if (departmentId) {
        params.append('departmentId', departmentId);
      }

      const response = await fetch(`/api/leave/reports/overview?${params.toString()}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate overview report');
      }

      if (format !== 'json') {
        // Handle file download for non-JSON formats
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `leave-overview-report-${timeRange}.${format === 'excel' ? 'xlsx' : format}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        toast({
          title: "Export Successful",
          description: `Overview report exported as ${format.toUpperCase()}`,
        });

        return null;
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to generate overview report');
      }

      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while generating overview report';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const getDepartments = useCallback(async (): Promise<Department[]> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/leave/reports/departments');

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch departments');
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch departments');
      }

      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching departments';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return [];
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const exportReport = useCallback(async (
    reportType: string,
    params: any,
    format: 'csv' | 'excel' | 'pdf'
  ): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      let url = '';
      const searchParams = new URLSearchParams({ ...params, format });

      switch (reportType) {
        case 'overview':
          url = `/api/leave/reports/overview?${searchParams.toString()}`;
          break;
        case 'utilization':
          url = `/api/leave/reports/utilization?${searchParams.toString()}`;
          break;
        case 'department':
          url = `/api/leave/reports/department?${searchParams.toString()}`;
          break;
        case 'trends':
          url = `/api/leave/reports/trends?${searchParams.toString()}`;
          break;
        default:
          throw new Error(`Unsupported report type: ${reportType}`);
      }

      const response = await fetch(url);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to export report');
      }

      // Handle file download
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `leave-${reportType}-report.${format === 'excel' ? 'xlsx' : format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);

      toast({
        title: "Export Successful",
        description: `${reportType.charAt(0).toUpperCase() + reportType.slice(1)} report exported as ${format.toUpperCase()}`,
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while exporting report';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const generateUtilizationReport = useCallback(async (
    startDate: Date,
    endDate: Date,
    format: 'json' | 'csv' = 'json'
  ): Promise<any> => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        format
      });

      const response = await fetch(`/api/leave/reports/utilization?${params.toString()}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate utilization report');
      }

      if (format === 'csv') {
        // Handle CSV download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `leave-utilization-report-${startDate.toISOString().split('T')[0]}-to-${endDate.toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast({
          title: "Success",
          description: "Utilization report downloaded successfully",
        });

        return null;
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: "Utilization report generated successfully",
      });

      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while generating utilization report';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const generateDepartmentReport = useCallback(async (
    year: number,
    format: 'json' | 'csv' = 'json'
  ): Promise<any> => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        year: year.toString(),
        format
      });

      const response = await fetch(`/api/leave/reports/department?${params.toString()}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate department report');
      }

      if (format === 'csv') {
        // Handle CSV download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `department-analytics-report-${year}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast({
          title: "Success",
          description: "Department report downloaded successfully",
        });

        return null;
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: "Department report generated successfully",
      });

      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while generating department report';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const generateTrendReport = useCallback(async (
    period: 'monthly' | 'quarterly' | 'yearly',
    periodsBack: number = 12,
    format: 'json' | 'csv' = 'json'
  ): Promise<any> => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        period,
        periodsBack: periodsBack.toString(),
        format
      });

      const response = await fetch(`/api/leave/reports/trends?${params.toString()}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate trend report');
      }

      if (format === 'csv') {
        // Handle CSV download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `trend-analysis-report-${period}-${periodsBack}periods.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast({
          title: "Success",
          description: "Trend report downloaded successfully",
        });

        return null;
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: "Trend report generated successfully",
      });

      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while generating trend report';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const generateEmployeeReport = useCallback(async (
    employeeId: string,
    year: number,
    format: 'json' | 'csv' = 'json'
  ): Promise<any> => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        year: year.toString(),
        format
      });

      const response = await fetch(`/api/leave/reports/employee/${employeeId}?${params.toString()}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate employee report');
      }

      if (format === 'csv') {
        // Handle CSV download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `employee-leave-report-${employeeId}-${year}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast({
          title: "Success",
          description: "Employee report downloaded successfully",
        });

        return null;
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: "Employee report generated successfully",
      });

      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while generating employee report';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  return {
    loading,
    error,
    generateOverviewReport,
    generateUtilizationReport,
    generateDepartmentReport,
    generateTrendReport,
    generateEmployeeReport,
    getDepartments,
    exportReport,
  };
}

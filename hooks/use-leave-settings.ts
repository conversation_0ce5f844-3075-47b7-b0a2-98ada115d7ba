import { useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';

export interface LeaveSettings {
  // General Settings
  carryOverEnabled: boolean;
  maxCarryOverDays: number;
  minNoticeDays: number;
  autoApproveEnabled: boolean;
  approvalWorkflow: 'single' | 'multi' | 'department';
  
  // Leave Year Settings
  defaultLeaveYear: 'calendar' | 'fiscal' | 'anniversary';
  fiscalYearStart: string;
  
  // Accrual Settings
  leaveAccrualFrequency: 'monthly' | 'quarterly' | 'annually';
  proRataCalculationEnabled: boolean;
  holidayExclusionEnabled: boolean;
  weekendExclusionEnabled: boolean;
  halfDayLeaveEnabled: boolean;
  hourlyLeaveEnabled: boolean;
  
  // Encashment Settings
  leaveEncashmentEnabled: boolean;
  leaveEncashmentLimit: number;
  leaveEncashmentRate: number;
  
  // Request Management
  leaveRequestCancellationEnabled: boolean;
  maxCancellationDays: number;
  
  // Balance & Warnings
  leaveBalanceDisplayEnabled: boolean;
  leaveBalanceWarningThreshold: number;
  leaveBalanceWarningEnabled: boolean;
  
  // Documentation
  leaveDocumentationRequired: boolean;
  leaveDocumentationDays: number;
  
  // Notification Settings
  emailNotificationsEnabled: boolean;
  calendarIntegrationEnabled: boolean;
}

interface UseLeaveSettingsReturn {
  loading: boolean;
  error: string | null;
  settings: LeaveSettings | null;
  getSettings: (organizationId?: string) => Promise<LeaveSettings | null>;
  updateSettings: (settings: Partial<LeaveSettings>, organizationId?: string) => Promise<boolean>;
  resetSettings: (organizationId?: string) => Promise<boolean>;
}

export function useLeaveSettings(): UseLeaveSettingsReturn {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [settings, setSettings] = useState<LeaveSettings | null>(null);
  const { toast } = useToast();

  const getSettings = useCallback(async (organizationId?: string): Promise<LeaveSettings | null> => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (organizationId) {
        params.append('organizationId', organizationId);
      }

      const response = await fetch(`/api/leave/settings?${params.toString()}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get leave settings');
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to get leave settings');
      }

      setSettings(result.data);
      return result.data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get leave settings';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const updateSettings = useCallback(async (
    settingsData: Partial<LeaveSettings>,
    organizationId?: string
  ): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/leave/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          settings: settingsData,
          organizationId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update leave settings');
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to update leave settings');
      }

      setSettings(result.data);
      
      toast({
        title: "Success",
        description: result.message || "Leave settings updated successfully",
      });

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update leave settings';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const resetSettings = useCallback(async (organizationId?: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/leave/settings/reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          organizationId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reset leave settings');
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to reset leave settings');
      }

      setSettings(result.data);
      
      toast({
        title: "Success",
        description: result.message || "Leave settings reset to defaults successfully",
      });

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to reset leave settings';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  return {
    loading,
    error,
    settings,
    getSettings,
    updateSettings,
    resetSettings,
  };
}
